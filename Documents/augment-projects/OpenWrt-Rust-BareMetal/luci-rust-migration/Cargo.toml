[workspace]
members = [
    "crates/web-ui",
    "crates/backend", 
    "crates/shared-types",
    "crates/utilities",
    "crates/opkg-integration",
    "crates/uci-bindings",
    "crates/auth-system",
    "crates/network-config",
    "crates/system-monitor",
    "crates/package-manager"
]

resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["OpenWrt LuCI Migration Team"]
license = "GPL-2.0"
repository = "https://github.com/openwrt/luci-rust-migration"
homepage = "https://openwrt.org"
documentation = "https://docs.rs/luci-rust-migration"
keywords = ["openwrt", "luci", "web-interface", "router", "embedded"]
categories = ["web-programming", "embedded", "network-programming"]

[workspace.dependencies]
# Core Rust dependencies
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"

# Web framework dependencies
leptos = { version = "0.6", features = ["ssr"] }
leptos_axum = "0.6"
leptos_meta = "0.6"
leptos_router = "0.6"
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["fs", "cors", "trace"] }

# CSS and styling
tailwindcss = "3.4"

# Database and configuration
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }

# Authentication and security
jsonwebtoken = "9.0"
bcrypt = "0.15"
uuid = { version = "1.0", features = ["v4", "serde"] }

# System integration
libc = "0.2"
nix = "0.27"

# Embedded and no-std support
heapless = "0.8"
embedded-hal = "1.0"

# Async and futures
futures = "0.3"
async-trait = "0.1"

# Logging and monitoring
log = "0.4"
env_logger = "0.10"

# Random number generation
rand = "0.8"

# Configuration management
config = "0.14"
toml = "0.8"

# HTTP client
reqwest = { version = "0.11", features = ["json", "rustls-tls"], default-features = false }
urlencoding = "2.1"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Template engine (backup for complex cases)
tera = "1.19"

# Development dependencies
cargo-leptos = "0.2"

# Note: build-dependencies are not allowed in workspace manifests
# Individual crates should specify their own build-dependencies if needed

[profile.release]
opt-level = "z"     # Optimize for size
lto = true          # Enable Link Time Optimization
codegen-units = 1   # Reduce number of codegen units to increase optimizations
panic = "abort"     # Abort on panic (reduces binary size)
strip = true        # Strip symbols from binary

[profile.dev]
opt-level = 0
debug = true
split-debuginfo = "unpacked"
