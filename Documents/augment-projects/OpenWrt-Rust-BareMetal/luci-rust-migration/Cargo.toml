[workspace]
members = [
    "crates/web-ui",
    "crates/backend", 
    "crates/shared-types",
    "crates/utilities",
    "crates/opkg-integration",
    "crates/uci-bindings",
    "crates/auth-system",
    "crates/network-config",
    "crates/system-monitor",
    "crates/package-manager"
]

resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["OpenWrt LuCI Migration Team"]
license = "GPL-2.0"
repository = "https://github.com/openwrt/luci-rust-migration"
homepage = "https://openwrt.org"
documentation = "https://docs.rs/luci-rust-migration"
keywords = ["openwrt", "luci", "web-interface", "router", "embedded"]
categories = ["web-programming", "embedded", "network-programming"]

[workspace.dependencies]
# Core Rust dependencies
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"

# Web framework dependencies
leptos = { version = "0.6", features = ["ssr"] }
leptos_axum = "0.6"
leptos_meta = "0.6"
leptos_router = "0.6"
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["fs", "cors", "trace"] }

# CSS and styling
tailwindcss = "3.4"

# Database and configuration
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }

# Authentication and security
jsonwebtoken = "9.0"
bcrypt = "0.15"
uuid = { version = "1.0", features = ["v4", "serde"] }

# System integration
libc = "0.2"
nix = "0.27"

# Embedded and no-std support
heapless = "0.8"
embedded-hal = "1.0"

# Async and futures
futures = "0.3"
async-trait = "0.1"

# Logging and monitoring
log = "0.4"
env_logger = "0.10"

# Random number generation
rand = "0.8"

# Configuration management
config = "0.14"
toml = "0.8"

# Test dependencies
tempfile = "3.0"
md5 = "0.7"
sha2 = "0.10"

# HTTP client
reqwest = { version = "0.11", features = ["json", "rustls-tls"], default-features = false }
urlencoding = "2.1"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Template engine (backup for complex cases)
tera = "1.19"

# Development dependencies
cargo-leptos = "0.2"

# Note: build-dependencies are not allowed in workspace manifests
# Individual crates should specify their own build-dependencies if needed

# Leptos configuration for cargo-leptos
[[workspace.metadata.leptos]]
# The name of the package, usually the same as the package name
name = "web-ui"
# The name used by wasm-pack/trunk for the JS/WASM bundle. Defaults to the crate name
output-name = "luci-web-ui"

# The site root folder is where cargo-leptos generate all output. WARNING: all content of this folder will be erased on a rebuild. Use it in your server setup.
site-root = "target/site"

# The site-root relative folder where all compiled output (JS, WASM and CSS) is written
# Defaults to pkg
site-pkg-dir = "pkg"

# [Optional] The source CSS file. If it ends with .sass or .scss then it will be compiled by dart-sass into CSS. The CSS is optimized by Lightning CSS before being written to <site-root>/<site-pkg-dir>/app.css
style-file = "style/main.css"

# Assets source dir. All files found here will be copied and synchronized to site-root.
# The assets-dir cannot have a sub directory with the same name/path as site-pkg-dir.
#
# Optional. Env: LEPTOS_ASSETS_DIR.
assets-dir = "assets"

# The IP and port (ex: 127.0.0.1:3000) where the server serves the content. Use it in your server setup.
site-addr = "0.0.0.0:3000"

# The port to use for automatic reload monitoring
reload-port = 3001

# [Optional] Command to use when running end2end tests. It will run in the end2end dir.
#   [Windows] for non-WSL use "npx.cmd playwright test"
#   This binary name can be checked in Powershell with Get-Command npx
end2end-cmd = "npx playwright test"
end2end-dir = "end2end"

#  The browserlist query used for optimizing the CSS.
browserquery = "defaults"

# Set by cargo-leptos watch when building with that tool. Controls whether autoreload JS will be included in the head
watch = false

# The environment Leptos will run in, usually either "DEV" or "PROD"
env = "DEV"

# The features to use when compiling the bin target
#
# Optional. Can be over-ridden with the command line parameter --bin-features
bin-features = ["ssr"]

# If the --no-default-features flag should be used when compiling the bin target
#
# Optional. Defaults to false.
bin-default-features = false

# The features to use when compiling the lib target
#
# Optional. Can be over-ridden with the command line parameter --lib-features
lib-features = ["hydrate"]

# If the --no-default-features flag should be used when compiling the lib target
#
# Optional. Defaults to false.
lib-default-features = false

# The profile to use for the lib target when compiling for release
#
# Optional. Defaults to "release".
lib-profile-release = "wasm-release"

# The tailwind input file.
#
# Optional, Activates the tailwind build
tailwind-input-file = "style/tailwind.css"

# The tailwind config file.
#
# Optional, defaults to "tailwind.config.js" which if is not present
# is generated for you
tailwind-config-file = "tailwind.config.js"

[profile.release]
opt-level = "z"     # Optimize for size
lto = true          # Enable Link Time Optimization
codegen-units = 1   # Reduce number of codegen units to increase optimizations
panic = "abort"     # Abort on panic (reduces binary size)
strip = true        # Strip symbols from binary

[profile.dev]
opt-level = 0
debug = true
split-debuginfo = "unpacked"
