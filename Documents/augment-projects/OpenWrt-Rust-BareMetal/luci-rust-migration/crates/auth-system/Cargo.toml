[package]
name = "luci-auth-system"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "LuCI Authentication System - JWT-based authentication and authorization"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Workspace dependencies
luci-shared-types = { path = "../shared-types" }
luci-utilities = { path = "../utilities" }

# Authentication
jsonwebtoken = { workspace = true }
bcrypt = { workspace = true }

# Serialization
serde = { workspace = true }
serde_json = { workspace = true }

# Database
sqlx = { workspace = true, optional = true }

# Async support
tokio = { workspace = true, optional = true }
futures = { workspace = true, optional = true }

# Utilities
uuid = { workspace = true }
chrono = { workspace = true }
regex = "1.0"
rand = "0.8"
sha2 = "0.10"

# Logging
log = { workspace = true }

# Error handling
anyhow = { workspace = true }
thiserror = { workspace = true }

# Embedded support
heapless = { workspace = true, optional = true }

[features]
default = ["async", "database"]
async = ["tokio", "futures"]
database = ["sqlx"]
embedded = ["heapless", "luci-shared-types/embedded", "luci-utilities/embedded"]
