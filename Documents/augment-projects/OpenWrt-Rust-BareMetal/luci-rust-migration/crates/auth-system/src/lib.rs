//! Authentication System for LuCI Rust Migration
//!
//! This crate provides comprehensive authentication and session management
//! for the OpenWrt LuCI web interface, including JWT token handling,
//! secure session storage, and user authentication.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use chrono::{Duration, Utc};
use jsonwebtoken::{Algorithm, DecodingKey, EncodingKey};
use rand::{Rng, thread_rng};
use sha2::{Sha256, Digest};
use bcrypt::{hash, DEFAULT_COST};
use thiserror::Error;

pub mod session;
pub mod jwt;
pub mod user;
pub mod password;

pub use session::*;
pub use jwt::*;
pub use user::*;
pub use password::*;

/// Authentication system errors
#[derive(Error, Debug)]
pub enum AuthError {
    #[error("Invalid credentials")]
    InvalidCredentials,
    #[error("Session not found")]
    SessionNotFound,
    #[error("Session expired")]
    SessionExpired,
    #[error("Token invalid: {0}")]
    TokenInvalid(String),
    #[error("Token expired")]
    TokenExpired,
    #[error("Permission denied")]
    PermissionDenied,
    #[error("User not found")]
    UserNotFound,
    #[error("Password hash error: {0}")]
    PasswordHashError(String),
    #[error("JWT error: {0}")]
    JwtError(#[from] jsonwebtoken::errors::Error),
    #[error("Bcrypt error: {0}")]
    BcryptError(#[from] bcrypt::BcryptError),
    #[error("Internal error: {0}")]
    Internal(String),
}

/// Result type for authentication operations
pub type AuthResult<T> = Result<T, AuthError>;

/// User roles in the system
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum UserRole {
    Admin,
    User,
    Guest,
}

impl UserRole {
    /// Get permissions for this role
    pub fn permissions(&self) -> Vec<String> {
        match self {
            UserRole::Admin => vec![
                "system.read".to_string(),
                "system.write".to_string(),
                "network.read".to_string(),
                "network.write".to_string(),
                "packages.read".to_string(),
                "packages.write".to_string(),
                "users.read".to_string(),
                "users.write".to_string(),
            ],
            UserRole::User => vec![
                "system.read".to_string(),
                "network.read".to_string(),
                "packages.read".to_string(),
            ],
            UserRole::Guest => vec![
                "system.read".to_string(),
            ],
        }
    }
}

/// Authentication configuration
#[derive(Debug, Clone)]
pub struct AuthConfig {
    /// JWT secret key
    pub jwt_secret: String,
    /// Session timeout in seconds
    pub session_timeout: i64,
    /// Maximum number of active sessions per user
    pub max_sessions_per_user: usize,
    /// Enable session cleanup
    pub enable_cleanup: bool,
    /// Cleanup interval in seconds
    pub cleanup_interval: u64,
}

impl Default for AuthConfig {
    fn default() -> Self {
        Self {
            jwt_secret: "default-secret-change-in-production".to_string(),
            session_timeout: 3600, // 1 hour
            max_sessions_per_user: 5,
            enable_cleanup: true,
            cleanup_interval: 300, // 5 minutes
        }
    }
}

/// Main authentication manager
pub struct AuthManager {
    config: AuthConfig,
    sessions: Arc<RwLock<HashMap<String, Session>>>,
    users: Arc<RwLock<HashMap<String, User>>>,
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
}

impl AuthManager {
    /// Create a new authentication manager
    pub fn new(config: AuthConfig) -> AuthResult<Self> {
        let encoding_key = EncodingKey::from_secret(config.jwt_secret.as_ref());
        let decoding_key = DecodingKey::from_secret(config.jwt_secret.as_ref());

        let mut manager = Self {
            config,
            sessions: Arc::new(RwLock::new(HashMap::new())),
            users: Arc::new(RwLock::new(HashMap::new())),
            encoding_key,
            decoding_key,
        };

        // Initialize with default admin user
        manager.initialize_default_users()?;

        Ok(manager)
    }

    /// Initialize default users (admin user for OpenWrt)
    fn initialize_default_users(&mut self) -> AuthResult<()> {
        let admin_user = User {
            username: "root".to_string(),
            password_hash: hash("admin", DEFAULT_COST)
                .map_err(|e| AuthError::PasswordHashError(e.to_string()))?,
            role: UserRole::Admin,
            created_at: Utc::now(),
            last_login: None,
            enabled: true,
        };

        let mut users = self.users.write().unwrap();
        users.insert("root".to_string(), admin_user);
        Ok(())
    }

    /// Authenticate user and create session
    pub fn authenticate(&self, username: &str, password: &str, ip_address: Option<String>, user_agent: Option<String>) -> AuthResult<(Session, String)> {
        // Authenticate user
        let user = {
            let mut users = self.users.write().unwrap();
            let user = users.get_mut(username)
                .ok_or(AuthError::InvalidCredentials)?;

            if !user.verify_password(password)? {
                return Err(AuthError::InvalidCredentials);
            }

            user.update_last_login();
            user.clone()
        };

        // Create session
        let session = Session::new(
            user.username.clone(),
            user.role.clone(),
            self.config.session_timeout,
            ip_address,
            user_agent,
        );

        // Create JWT token
        let claims = jwt::Claims::new(
            user.username,
            user.role,
            session.id.clone(),
            self.config.session_timeout,
        );

        let jwt_manager = jwt::JwtManager::new(&self.config.jwt_secret);
        let token = jwt_manager.create_token(&claims)?;

        // Store session
        {
            let mut sessions = self.sessions.write().unwrap();

            // Limit sessions per user
            let user_session_count = sessions.values()
                .filter(|s| s.username == session.username && s.is_valid())
                .count();

            if user_session_count >= self.config.max_sessions_per_user {
                // Remove oldest session for this user
                let oldest_session_id = sessions.values()
                    .filter(|s| s.username == session.username && s.is_valid())
                    .min_by_key(|s| s.created_at)
                    .map(|s| s.id.clone());

                if let Some(id) = oldest_session_id {
                    sessions.remove(&id);
                }
            }

            sessions.insert(session.id.clone(), session.clone());
        }

        Ok((session, token))
    }

    /// Validate JWT token and get session
    pub fn validate_token(&self, token: &str) -> AuthResult<Session> {
        let jwt_manager = jwt::JwtManager::new(&self.config.jwt_secret);
        let claims = jwt_manager.validate_token(token)?;

        let sessions = self.sessions.read().unwrap();
        let session = sessions.get(&claims.sid)
            .ok_or(AuthError::SessionNotFound)?;

        if !session.is_valid() {
            return Err(AuthError::SessionExpired);
        }

        Ok(session.clone())
    }

    /// Refresh JWT token
    pub fn refresh_token(&self, token: &str) -> AuthResult<String> {
        let jwt_manager = jwt::JwtManager::new(&self.config.jwt_secret);
        jwt_manager.refresh_token(token, self.config.session_timeout)
    }

    /// Logout user (invalidate session)
    pub fn logout(&self, session_id: &str) -> AuthResult<()> {
        let mut sessions = self.sessions.write().unwrap();
        if let Some(session) = sessions.get_mut(session_id) {
            session.invalidate();
        }
        sessions.remove(session_id);
        Ok(())
    }

    /// Logout all sessions for a user
    pub fn logout_user(&self, username: &str) -> AuthResult<usize> {
        let mut sessions = self.sessions.write().unwrap();
        let session_ids: Vec<String> = sessions.values()
            .filter(|s| s.username == username)
            .map(|s| s.id.clone())
            .collect();

        let count = session_ids.len();
        for id in session_ids {
            sessions.remove(&id);
        }

        Ok(count)
    }

    /// Get active session by ID
    pub fn get_session(&self, session_id: &str) -> Option<Session> {
        let sessions = self.sessions.read().unwrap();
        sessions.get(session_id).cloned()
    }

    /// Update session last access time
    pub fn touch_session(&self, session_id: &str) -> AuthResult<()> {
        let mut sessions = self.sessions.write().unwrap();
        if let Some(session) = sessions.get_mut(session_id) {
            session.touch(self.config.session_timeout);
            Ok(())
        } else {
            Err(AuthError::SessionNotFound)
        }
    }

    /// Clean up expired sessions
    pub fn cleanup_expired_sessions(&self) -> usize {
        let mut sessions = self.sessions.write().unwrap();
        let expired_ids: Vec<String> = sessions.iter()
            .filter(|(_, session)| !session.is_valid())
            .map(|(id, _)| id.clone())
            .collect();

        let count = expired_ids.len();
        for id in expired_ids {
            sessions.remove(&id);
        }

        count
    }

    /// Get session statistics
    pub fn get_session_stats(&self) -> SessionStats {
        let sessions = self.sessions.read().unwrap();
        let total_sessions = sessions.len();
        let active_sessions = sessions.values().filter(|s| s.is_valid()).count();
        let expired_sessions = total_sessions - active_sessions;

        let mut user_sessions = std::collections::HashMap::new();
        for session in sessions.values() {
            if session.is_valid() {
                *user_sessions.entry(session.username.clone()).or_insert(0) += 1;
            }
        }

        SessionStats {
            total_sessions,
            active_sessions,
            expired_sessions,
            user_sessions,
        }
    }

    /// Change user password
    pub fn change_user_password(&self, username: &str, request: ChangePasswordRequest) -> AuthResult<()> {
        let mut users = self.users.write().unwrap();
        let user = users.get_mut(username)
            .ok_or(AuthError::UserNotFound)?;

        if !user.verify_password(&request.current_password)? {
            return Err(AuthError::InvalidCredentials);
        }

        user.update_password(&request.new_password)?;
        Ok(())
    }
}

/// Session statistics
#[derive(Debug, Serialize)]
pub struct SessionStats {
    pub total_sessions: usize,
    pub active_sessions: usize,
    pub expired_sessions: usize,
    pub user_sessions: std::collections::HashMap<String, usize>,
}
