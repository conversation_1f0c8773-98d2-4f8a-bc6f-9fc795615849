//! Session management for authentication system
//!
//! Provides secure session handling with automatic cleanup and validation.

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use rand::{Rng, thread_rng};
use sha2::{Sha256, Digest};
use crate::{AuthResult, UserRole};

/// Session information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    /// Unique session identifier
    pub id: String,
    /// Username associated with this session
    pub username: String,
    /// User role
    pub role: UserRole,
    /// Session creation time
    pub created_at: DateTime<Utc>,
    /// Last access time
    pub last_access: DateTime<Utc>,
    /// Session expiration time
    pub expires_at: DateTime<Utc>,
    /// Client IP address
    pub ip_address: Option<String>,
    /// User agent string
    pub user_agent: Option<String>,
    /// Whether session is active
    pub active: bool,
    /// CSRF token for this session
    pub csrf_token: String,
}

impl Session {
    /// Create a new session
    pub fn new(
        username: String,
        role: UserRole,
        timeout_seconds: i64,
        ip_address: Option<String>,
        user_agent: Option<String>,
    ) -> Self {
        let now = Utc::now();
        let expires_at = now + chrono::Duration::seconds(timeout_seconds);

        Self {
            id: Uuid::new_v4().to_string(),
            username,
            role,
            created_at: now,
            last_access: now,
            expires_at,
            ip_address,
            user_agent,
            active: true,
            csrf_token: Self::generate_csrf_token(),
        }
    }

    /// Check if session is expired
    pub fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }

    /// Check if session is valid (active and not expired)
    pub fn is_valid(&self) -> bool {
        self.active && !self.is_expired()
    }

    /// Update last access time and extend expiration
    pub fn touch(&mut self, timeout_seconds: i64) {
        let now = Utc::now();
        self.last_access = now;
        self.expires_at = now + chrono::Duration::seconds(timeout_seconds);
    }

    /// Invalidate the session
    pub fn invalidate(&mut self) {
        self.active = false;
    }

    /// Get session age in seconds
    pub fn age_seconds(&self) -> i64 {
        (Utc::now() - self.created_at).num_seconds()
    }

    /// Get time since last access in seconds
    pub fn idle_seconds(&self) -> i64 {
        (Utc::now() - self.last_access).num_seconds()
    }

    /// Check if user has permission
    pub fn has_permission(&self, permission: &str) -> bool {
        if !self.is_valid() {
            return false;
        }

        let permissions = self.role.permissions();
        permissions.contains(&permission.to_string())
    }

    /// Generate a new CSRF token
    pub fn generate_csrf_token() -> String {
        let mut rng = thread_rng();
        let random_bytes: [u8; 32] = rng.gen();

        let mut hasher = Sha256::new();
        hasher.update(&random_bytes);
        hasher.update(Utc::now().timestamp().to_be_bytes());

        format!("{:x}", hasher.finalize())
    }

    /// Regenerate CSRF token for this session
    pub fn regenerate_csrf_token(&mut self) {
        self.csrf_token = Self::generate_csrf_token();
    }

    /// Validate CSRF token
    pub fn validate_csrf_token(&self, token: &str) -> bool {
        if !self.is_valid() {
            return false;
        }

        // Use constant-time comparison to prevent timing attacks
        self.csrf_token.len() == token.len() &&
        self.csrf_token.as_bytes().iter()
            .zip(token.as_bytes().iter())
            .fold(0u8, |acc, (a, b)| acc | (a ^ b)) == 0
    }
}

/// Session store for managing active sessions
#[derive(Debug)]
pub struct SessionStore {
    sessions: std::collections::HashMap<String, Session>,
    user_sessions: std::collections::HashMap<String, Vec<String>>, // username -> session_ids
}

impl SessionStore {
    /// Create a new session store
    pub fn new() -> Self {
        Self {
            sessions: std::collections::HashMap::new(),
            user_sessions: std::collections::HashMap::new(),
        }
    }

    /// Add a session to the store
    pub fn add_session(&mut self, session: Session) -> AuthResult<()> {
        let session_id = session.id.clone();
        let username = session.username.clone();

        // Add to sessions map
        self.sessions.insert(session_id.clone(), session);

        // Add to user sessions map
        self.user_sessions
            .entry(username)
            .or_insert_with(Vec::new)
            .push(session_id);

        Ok(())
    }

    /// Get a session by ID
    pub fn get_session(&self, session_id: &str) -> Option<&Session> {
        self.sessions.get(session_id)
    }

    /// Get a mutable session by ID
    pub fn get_session_mut(&mut self, session_id: &str) -> Option<&mut Session> {
        self.sessions.get_mut(session_id)
    }

    /// Remove a session
    pub fn remove_session(&mut self, session_id: &str) -> Option<Session> {
        if let Some(session) = self.sessions.remove(session_id) {
            // Remove from user sessions map
            if let Some(user_sessions) = self.user_sessions.get_mut(&session.username) {
                user_sessions.retain(|id| id != session_id);
                if user_sessions.is_empty() {
                    self.user_sessions.remove(&session.username);
                }
            }
            Some(session)
        } else {
            None
        }
    }

    /// Get all sessions for a user
    pub fn get_user_sessions(&self, username: &str) -> Vec<&Session> {
        if let Some(session_ids) = self.user_sessions.get(username) {
            session_ids
                .iter()
                .filter_map(|id| self.sessions.get(id))
                .collect()
        } else {
            Vec::new()
        }
    }

    /// Remove all sessions for a user
    pub fn remove_user_sessions(&mut self, username: &str) -> Vec<Session> {
        let mut removed_sessions = Vec::new();

        if let Some(session_ids) = self.user_sessions.remove(username) {
            for session_id in session_ids {
                if let Some(session) = self.sessions.remove(&session_id) {
                    removed_sessions.push(session);
                }
            }
        }

        removed_sessions
    }

    /// Clean up expired sessions
    pub fn cleanup_expired(&mut self) -> usize {
        let expired_ids: Vec<String> = self
            .sessions
            .iter()
            .filter(|(_, session)| session.is_expired())
            .map(|(id, _)| id.clone())
            .collect();

        let count = expired_ids.len();
        for id in expired_ids {
            self.remove_session(&id);
        }

        count
    }

    /// Get total number of active sessions
    pub fn active_session_count(&self) -> usize {
        self.sessions.len()
    }

    /// Get number of sessions for a specific user
    pub fn user_session_count(&self, username: &str) -> usize {
        self.user_sessions
            .get(username)
            .map(|sessions| sessions.len())
            .unwrap_or(0)
    }

    /// Limit sessions per user (remove oldest if over limit)
    pub fn limit_user_sessions(&mut self, username: &str, max_sessions: usize) -> usize {
        let mut removed_count = 0;

        if let Some(session_ids) = self.user_sessions.get(username) {
            if session_ids.len() > max_sessions {
                // Get sessions with their creation times
                let mut user_sessions: Vec<_> = session_ids
                    .iter()
                    .filter_map(|id| self.sessions.get(id).map(|s| (id.clone(), s.created_at)))
                    .collect();

                // Sort by creation time (oldest first)
                user_sessions.sort_by_key(|(_, created_at)| *created_at);

                // Remove oldest sessions
                let to_remove = user_sessions.len() - max_sessions;
                for (session_id, _) in user_sessions.into_iter().take(to_remove) {
                    self.remove_session(&session_id);
                    removed_count += 1;
                }
            }
        }

        removed_count
    }
}

impl Default for SessionStore {
    fn default() -> Self {
        Self::new()
    }
}
