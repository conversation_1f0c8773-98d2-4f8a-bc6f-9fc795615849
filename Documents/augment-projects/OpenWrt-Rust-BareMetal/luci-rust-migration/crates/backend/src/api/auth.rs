//! Authentication API endpoints

use axum::{
    extract::{State, Request},
    http::{HeaderMap, StatusCode},
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use luci_auth_system::{AuthManager, AuthError, jwt::extract_bearer_token, ChangePasswordRequest, PasswordManager, PasswordValidation, PasswordPolicy};
use crate::{AppState, api::{ApiResponse, ApiError}};

/// Login request
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

/// Login response
#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub token_type: String,
    pub expires_in: i64,
    pub expires_at: chrono::DateTime<chrono::Utc>,
    pub session_id: String,
    pub user: UserInfo,
}

/// User information
#[derive(Debug, Serialize)]
pub struct UserInfo {
    pub username: String,
    pub role: String,
    pub permissions: Vec<String>,
    pub last_login: Option<chrono::DateTime<chrono::Utc>>,
}

/// Authentication status
#[derive(Debug, Serialize)]
pub struct AuthStatus {
    pub authenticated: bool,
    pub user: Option<UserInfo>,
    pub session_id: Option<String>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// Password validation request
#[derive(Debug, Deserialize)]
pub struct ValidatePasswordRequest {
    pub password: String,
    pub username: Option<String>,
}

/// Password validation response
#[derive(Debug, Serialize)]
pub struct ValidatePasswordResponse {
    pub valid: bool,
    pub strength: String,
    pub messages: Vec<String>,
    pub score: u8,
}

/// Password policy response
#[derive(Debug, Serialize)]
pub struct PasswordPolicyResponse {
    pub min_length: usize,
    pub max_length: usize,
    pub require_uppercase: bool,
    pub require_lowercase: bool,
    pub require_numbers: bool,
    pub require_special: bool,
    pub disallow_common: bool,
    pub disallow_username: bool,
}

/// Login endpoint
pub async fn login(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
    Json(request): Json<LoginRequest>,
) -> Result<Json<ApiResponse<LoginResponse>>, ApiError> {
    // Extract client information
    let ip_address = headers.get("x-forwarded-for")
        .or_else(|| headers.get("x-real-ip"))
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string());

    let user_agent = headers.get("user-agent")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string());

    // Authenticate user
    match state.auth_manager.authenticate(&request.username, &request.password, ip_address, user_agent) {
        Ok((session, token)) => {
            let user_info = UserInfo {
                username: session.username.clone(),
                role: format!("{:?}", session.role).to_lowercase(),
                permissions: session.role.permissions(),
                last_login: Some(session.created_at),
            };

            let response = LoginResponse {
                access_token: token,
                token_type: "Bearer".to_string(),
                expires_in: (session.expires_at - session.created_at).num_seconds(),
                expires_at: session.expires_at,
                session_id: session.id,
                user: user_info,
            };

            Ok(Json(ApiResponse::success(response)))
        }
        Err(AuthError::InvalidCredentials) => {
            Err(ApiError::Unauthorized("Invalid username or password".to_string()))
        }
        Err(e) => {
            tracing::error!("Authentication error: {:?}", e);
            Err(ApiError::Internal("Authentication failed".to_string()))
        }
    }
}

/// Logout endpoint
pub async fn logout(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
) -> Result<Json<ApiResponse<String>>, ApiError> {
    // Extract token from Authorization header
    let auth_header = headers.get("authorization")
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| ApiError::Unauthorized("Missing authorization header".to_string()))?;

    let token = extract_bearer_token(auth_header)
        .ok_or_else(|| ApiError::Unauthorized("Invalid authorization header format".to_string()))?;

    // Validate token and get session
    match state.auth_manager.validate_token(token) {
        Ok(session) => {
            // Logout the session
            if let Err(e) = state.auth_manager.logout(&session.id) {
                tracing::error!("Logout error: {:?}", e);
                return Err(ApiError::Internal("Logout failed".to_string()));
            }

            Ok(Json(ApiResponse::success("Successfully logged out".to_string())))
        }
        Err(AuthError::TokenExpired) => {
            Err(ApiError::Unauthorized("Token expired".to_string()))
        }
        Err(AuthError::TokenInvalid(_)) => {
            Err(ApiError::Unauthorized("Invalid token".to_string()))
        }
        Err(e) => {
            tracing::error!("Token validation error: {:?}", e);
            Err(ApiError::Internal("Logout failed".to_string()))
        }
    }
}

/// Get authentication status
pub async fn get_auth_status(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
) -> Result<Json<ApiResponse<AuthStatus>>, ApiError> {
    // Try to extract token from Authorization header
    let auth_header = headers.get("authorization")
        .and_then(|h| h.to_str().ok());

    if let Some(auth_header) = auth_header {
        if let Some(token) = extract_bearer_token(auth_header) {
            // Validate token and get session
            match state.auth_manager.validate_token(token) {
                Ok(session) => {
                    let user_info = UserInfo {
                        username: session.username.clone(),
                        role: format!("{:?}", session.role).to_lowercase(),
                        permissions: session.role.permissions(),
                        last_login: Some(session.last_access),
                    };

                    let status = AuthStatus {
                        authenticated: true,
                        user: Some(user_info),
                        session_id: Some(session.id),
                        expires_at: Some(session.expires_at),
                    };

                    return Ok(Json(ApiResponse::success(status)));
                }
                Err(_) => {
                    // Token is invalid or expired, fall through to unauthenticated response
                }
            }
        }
    }

    // Not authenticated
    let status = AuthStatus {
        authenticated: false,
        user: None,
        session_id: None,
        expires_at: None,
    };
    Ok(Json(ApiResponse::success(status)))
}

/// Refresh token endpoint
pub async fn refresh_token(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
) -> Result<Json<ApiResponse<LoginResponse>>, ApiError> {
    // Extract token from Authorization header
    let auth_header = headers.get("authorization")
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| ApiError::Unauthorized("Missing authorization header".to_string()))?;

    let token = extract_bearer_token(auth_header)
        .ok_or_else(|| ApiError::Unauthorized("Invalid authorization header format".to_string()))?;

    // Refresh the token
    match state.auth_manager.refresh_token(token) {
        Ok(new_token) => {
            // Get session info for response
            match state.auth_manager.validate_token(&new_token) {
                Ok(session) => {
                    let user_info = UserInfo {
                        username: session.username.clone(),
                        role: format!("{:?}", session.role).to_lowercase(),
                        permissions: session.role.permissions(),
                        last_login: Some(session.last_access),
                    };

                    let response = LoginResponse {
                        access_token: new_token,
                        token_type: "Bearer".to_string(),
                        expires_in: (session.expires_at - chrono::Utc::now()).num_seconds(),
                        expires_at: session.expires_at,
                        session_id: session.id,
                        user: user_info,
                    };

                    Ok(Json(ApiResponse::success(response)))
                }
                Err(e) => {
                    tracing::error!("Token validation error after refresh: {:?}", e);
                    Err(ApiError::Internal("Token refresh failed".to_string()))
                }
            }
        }
        Err(AuthError::TokenExpired) => {
            Err(ApiError::Unauthorized("Token expired, please login again".to_string()))
        }
        Err(AuthError::TokenInvalid(_)) => {
            Err(ApiError::Unauthorized("Invalid token".to_string()))
        }
        Err(e) => {
            tracing::error!("Token refresh error: {:?}", e);
            Err(ApiError::Internal("Token refresh failed".to_string()))
        }
    }
}

/// Change password endpoint
pub async fn change_password(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
    Json(request): Json<ChangePasswordRequest>,
) -> Result<Json<ApiResponse<String>>, ApiError> {
    // Extract token from Authorization header
    let auth_header = headers.get("authorization")
        .and_then(|h| h.to_str().ok())
        .ok_or_else(|| ApiError::Unauthorized("Missing authorization header".to_string()))?;

    let token = extract_bearer_token(auth_header)
        .ok_or_else(|| ApiError::Unauthorized("Invalid authorization header format".to_string()))?;

    // Validate token and get session
    let session = match state.auth_manager.validate_token(token) {
        Ok(session) => session,
        Err(AuthError::TokenExpired) => {
            return Err(ApiError::Unauthorized("Token expired".to_string()));
        }
        Err(AuthError::TokenInvalid(_)) => {
            return Err(ApiError::Unauthorized("Invalid token".to_string()));
        }
        Err(e) => {
            tracing::error!("Token validation error: {:?}", e);
            return Err(ApiError::Internal("Authentication failed".to_string()));
        }
    };

    // Validate new password before attempting change
    let password_manager = PasswordManager::new();
    let validation = password_manager.validate_password(&request.new_password, Some(&session.username));

    if !validation.valid {
        return Err(ApiError::BadRequest(format!("Password validation failed: {}", validation.messages.join(", "))));
    }

    // Change password
    match state.auth_manager.change_user_password(&session.username, request) {
        Ok(_) => {
            tracing::info!("Password changed successfully for user: {}", session.username);
            Ok(Json(ApiResponse::success("Password changed successfully".to_string())))
        }
        Err(AuthError::InvalidCredentials) => {
            Err(ApiError::Unauthorized("Current password is incorrect".to_string()))
        }
        Err(AuthError::UserNotFound) => {
            Err(ApiError::NotFound("User not found".to_string()))
        }
        Err(e) => {
            tracing::error!("Password change error: {:?}", e);
            Err(ApiError::Internal("Password change failed".to_string()))
        }
    }
}

/// Validate password endpoint
pub async fn validate_password(
    Json(request): Json<ValidatePasswordRequest>,
) -> Result<Json<ApiResponse<ValidatePasswordResponse>>, ApiError> {
    let password_manager = PasswordManager::new();
    let validation = password_manager.validate_password(&request.password, request.username.as_deref());

    let strength_str = match validation.strength {
        luci_auth_system::PasswordStrength::Weak => "weak",
        luci_auth_system::PasswordStrength::Fair => "fair",
        luci_auth_system::PasswordStrength::Good => "good",
        luci_auth_system::PasswordStrength::Strong => "strong",
    };

    let response = ValidatePasswordResponse {
        valid: validation.valid,
        strength: strength_str.to_string(),
        messages: validation.messages,
        score: validation.score,
    };

    Ok(Json(ApiResponse::success(response)))
}

/// Get password policy endpoint
pub async fn get_password_policy() -> Result<Json<ApiResponse<PasswordPolicyResponse>>, ApiError> {
    let password_manager = PasswordManager::new();
    let policy = password_manager.get_policy();

    let response = PasswordPolicyResponse {
        min_length: policy.min_length,
        max_length: policy.max_length,
        require_uppercase: policy.require_uppercase,
        require_lowercase: policy.require_lowercase,
        require_numbers: policy.require_numbers,
        require_special: policy.require_special,
        disallow_common: policy.disallow_common,
        disallow_username: policy.disallow_username,
    };

    Ok(Json(ApiResponse::success(response)))
}
