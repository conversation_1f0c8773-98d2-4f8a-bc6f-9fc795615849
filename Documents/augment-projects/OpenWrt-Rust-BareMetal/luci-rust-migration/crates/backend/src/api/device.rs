//! Device information API endpoints

use axum::{
    extract::State,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use crate::{AppState, api::{ApiResponse, ApiError}};
use luci_system_monitor::{SystemMonitor, MonitorResult};

/// Device information response
#[derive(Debug, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub system_info: SystemInfo,
    pub hardware_info: HardwareInfo,
    pub memory_info: MemoryInfo,
    pub storage_info: StorageInfo,
    pub network_info: Vec<NetworkDevice>,
}

/// System information
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemInfo {
    pub hostname: String,
    pub model: String,
    pub architecture: String,
    pub kernel_version: String,
    pub firmware_version: String,
    pub uptime: String,
    pub load_average: String,
    pub current_time: String,
}

/// Hardware information
#[derive(Debug, Serialize, Deserialize)]
pub struct HardwareInfo {
    pub cpu_model: String,
    pub cpu_cores: u32,
    pub cpu_frequency: String,
    pub cpu_temperature: Option<f32>,
    pub board_name: String,
    pub target: String,
}

/// Memory information
#[derive(Debug, Serialize, Deserialize)]
pub struct MemoryInfo {
    pub total_ram: u64,
    pub available_ram: u64,
    pub used_ram: u64,
    pub free_ram: u64,
    pub cached_ram: u64,
    pub buffered_ram: u64,
    pub swap_total: u64,
    pub swap_used: u64,
    pub swap_free: u64,
}

/// Storage information
#[derive(Debug, Serialize, Deserialize)]
pub struct StorageInfo {
    pub total_flash: u64,
    pub available_flash: u64,
    pub used_flash: u64,
    pub filesystems: Vec<FilesystemInfo>,
}

/// Filesystem information
#[derive(Debug, Serialize, Deserialize)]
pub struct FilesystemInfo {
    pub device: String,
    pub mount_point: String,
    pub filesystem_type: String,
    pub total_size: u64,
    pub used_size: u64,
    pub available_size: u64,
    pub usage_percentage: f32,
}

/// Network device information
#[derive(Debug, Serialize, Deserialize)]
pub struct NetworkDevice {
    pub name: String,
    pub device_type: String,
    pub mac_address: String,
    pub ip_addresses: Vec<String>,
    pub status: String,
    pub mtu: u32,
    pub rx_bytes: u64,
    pub tx_bytes: u64,
    pub rx_packets: u64,
    pub tx_packets: u64,
}

/// Get device information
pub async fn get_device_info(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<DeviceInfo>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let device_info = collect_device_info(&mut monitor).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect device info: {}", e)))?;

    Ok(Json(ApiResponse::success(device_info)))
}

/// Collect device information from the system
async fn collect_device_info(monitor: &mut SystemMonitor) -> anyhow::Result<DeviceInfo> {
    // Get system information
    let system_info = monitor.get_system_info()
        .map_err(|e| anyhow::anyhow!("Failed to get system info: {}", e))?;

    // Get memory information
    let memory_info = monitor.memory_monitor().get_memory_info()
        .map_err(|e| anyhow::anyhow!("Failed to get memory info: {}", e))?;

    // Get storage information
    let storage_info = monitor.storage_monitor().get_all_usage()
        .map_err(|e| anyhow::anyhow!("Failed to get storage info: {}", e))?;

    // Get network information
    let network_stats = monitor.network_monitor().get_all_stats()
        .map_err(|e| anyhow::anyhow!("Failed to get network stats: {}", e))?;

    // Convert to API format
    let device_info = DeviceInfo {
        system_info: SystemInfo {
            hostname: system_info.hostname,
            model: "OpenWrt Device".to_string(), // TODO: Get actual model from system
            architecture: system_info.architecture,
            kernel_version: system_info.kernel_version,
            firmware_version: "Unknown".to_string(), // TODO: Get firmware version
            uptime: format_uptime(system_info.uptime.uptime_seconds),
            load_average: format!("{:.2} {:.2} {:.2}",
                system_info.load_average.one_minute,
                system_info.load_average.five_minutes,
                system_info.load_average.fifteen_minutes),
            current_time: chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC").to_string(),
        },
        hardware_info: HardwareInfo {
            cpu_model: system_info.cpu_model,
            cpu_cores: system_info.cpu_cores,
            cpu_frequency: "Unknown MHz".to_string(), // TODO: Get CPU frequency
            cpu_temperature: None, // TODO: Get CPU temperature
            board_name: "Unknown".to_string(), // TODO: Get board name
            target: "Unknown".to_string(), // TODO: Get target platform
        },
        memory_info: MemoryInfo {
            total_ram: memory_info.total,
            available_ram: memory_info.available,
            used_ram: memory_info.used,
            free_ram: memory_info.free,
            cached_ram: memory_info.cached,
            buffered_ram: memory_info.buffers,
            swap_total: 0, // TODO: Get swap info from MemoryUsage instead
            swap_used: 0,  // TODO: Get swap info from MemoryUsage instead
            swap_free: 0,  // TODO: Get swap info from MemoryUsage instead
        },
        storage_info: StorageInfo {
            total_flash: storage_info.iter().map(|s| s.total).sum(),
            available_flash: storage_info.iter().map(|s| s.available).sum(),
            used_flash: storage_info.iter().map(|s| s.used).sum(),
            filesystems: storage_info.into_iter().map(|fs| {
                FilesystemInfo {
                    device: fs.mount_point.clone(),
                    mount_point: fs.mount_point,
                    filesystem_type: fs.filesystem,
                    total_size: fs.total,
                    used_size: fs.used,
                    available_size: fs.available,
                    usage_percentage: fs.usage_percent as f32,
                }
            }).collect(),
        },
        network_info: network_stats.into_iter().map(|stats| {
            NetworkDevice {
                name: stats.interface.clone(),
                device_type: if stats.interface.starts_with("eth") {
                    "Ethernet".to_string()
                } else if stats.interface.starts_with("wlan") {
                    "WiFi".to_string()
                } else if stats.interface.starts_with("lo") {
                    "Loopback".to_string()
                } else {
                    "Other".to_string()
                },
                mac_address: "00:00:00:00:00:00".to_string(), // TODO: Get actual MAC
                ip_addresses: vec![], // TODO: Get actual IP addresses
                status: "up".to_string(), // TODO: Determine interface status from stats
                mtu: 1500, // TODO: Get actual MTU
                rx_bytes: stats.rx_bytes,
                tx_bytes: stats.tx_bytes,
                rx_packets: stats.rx_packets,
                tx_packets: stats.tx_packets,
            }
        }).collect(),
    };

    Ok(device_info)
}

/// Format uptime in human-readable format
fn format_uptime(uptime_seconds: u64) -> String {
    let days = uptime_seconds / 86400;
    let hours = (uptime_seconds % 86400) / 3600;
    let minutes = (uptime_seconds % 3600) / 60;
    let seconds = uptime_seconds % 60;

    if days > 0 {
        format!("{} days, {}:{:02}:{:02}", days, hours, minutes, seconds)
    } else if hours > 0 {
        format!("{}:{:02}:{:02}", hours, minutes, seconds)
    } else {
        format!("{}:{:02}", minutes, seconds)
    }
}
