//! System logs API endpoints

use axum::{
    extract::{Query, State},
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::{sync::Arc, collections::HashMap};
use crate::{AppState, api::{ApiResponse, ApiError}};
use luci_system_monitor::{SystemMonitor, MonitorResult};

/// Log level
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum LogLevel {
    Emergency = 0,
    Alert = 1,
    Critical = 2,
    Error = 3,
    Warning = 4,
    Notice = 5,
    Info = 6,
    Debug = 7,
}

/// Log entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub timestamp: String,
    pub level: LogLevel,
    pub facility: String,
    pub process: String,
    pub pid: Option<u32>,
    pub message: String,
}

/// Log filter parameters
#[derive(Debug, Deserialize)]
pub struct LogFilter {
    pub level: Option<LogLevel>,
    pub facility: Option<String>,
    pub process: Option<String>,
    pub search_text: Option<String>,
    pub limit: Option<usize>,
}

/// Log response
#[derive(Debug, Serialize)]
pub struct LogResponse {
    pub logs: Vec<LogEntry>,
    pub available_facilities: Vec<String>,
    pub available_processes: Vec<String>,
}

/// Get system logs
pub async fn get_logs(
    Query(filter): Query<LogFilter>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<LogResponse>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let response = collect_logs(&mut monitor, filter).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect logs: {}", e)))?;

    Ok(Json(ApiResponse::success(response)))
}

/// Collect logs from the system
async fn collect_logs(monitor: &mut SystemMonitor, filter: LogFilter) -> anyhow::Result<LogResponse> {
    // Get logs from monitor
    let log_entries = monitor.log_monitor().get_logs()
        .map_err(|e| anyhow::anyhow!("Failed to get logs: {}", e))?;

    // Convert system monitor log entries to API format
    let mut logs: Vec<LogEntry> = log_entries.iter().map(|entry| {
        LogEntry {
            timestamp: format_timestamp(&entry.timestamp),
            level: convert_log_level(&entry.level),
            facility: format_facility(&entry.facility),
            process: entry.process.clone(),
            pid: entry.pid,
            message: entry.message.clone(),
        }
    }).collect();

    // Apply filters
    if let Some(level) = &filter.level {
        logs.retain(|entry| entry.level <= *level);
    }

    if let Some(facility) = &filter.facility {
        logs.retain(|entry| entry.facility == *facility);
    }

    if let Some(process) = &filter.process {
        logs.retain(|entry| entry.process == *process);
    }

    if let Some(search_text) = &filter.search_text {
        if !search_text.is_empty() {
            logs.retain(|entry| {
                entry.message.to_lowercase().contains(&search_text.to_lowercase()) ||
                entry.process.to_lowercase().contains(&search_text.to_lowercase())
            });
        }
    }

    // Apply limit
    if let Some(limit) = filter.limit {
        logs.truncate(limit);
    }

    // Collect available facilities and processes
    let available_facilities: Vec<String> = log_entries.iter()
        .map(|entry| format_facility(&entry.facility))
        .collect::<std::collections::HashSet<_>>()
        .into_iter()
        .collect();

    let available_processes: Vec<String> = log_entries.iter()
        .map(|entry| entry.process.clone())
        .collect::<std::collections::HashSet<_>>()
        .into_iter()
        .collect();

    Ok(LogResponse {
        logs,
        available_facilities,
        available_processes,
    })
}

/// Convert system monitor log level to API log level
fn convert_log_level(level: &luci_system_monitor::logs::LogLevel) -> LogLevel {
    match level {
        luci_system_monitor::logs::LogLevel::Emergency => LogLevel::Emergency,
        luci_system_monitor::logs::LogLevel::Alert => LogLevel::Alert,
        luci_system_monitor::logs::LogLevel::Critical => LogLevel::Critical,
        luci_system_monitor::logs::LogLevel::Error => LogLevel::Error,
        luci_system_monitor::logs::LogLevel::Warning => LogLevel::Warning,
        luci_system_monitor::logs::LogLevel::Notice => LogLevel::Notice,
        luci_system_monitor::logs::LogLevel::Info => LogLevel::Info,
        luci_system_monitor::logs::LogLevel::Debug => LogLevel::Debug,
    }
}

/// Format facility for display
fn format_facility(facility: &luci_system_monitor::logs::LogFacility) -> String {
    match facility {
        luci_system_monitor::logs::LogFacility::Kernel => "kernel".to_string(),
        luci_system_monitor::logs::LogFacility::User => "user".to_string(),
        luci_system_monitor::logs::LogFacility::Mail => "mail".to_string(),
        luci_system_monitor::logs::LogFacility::Daemon => "daemon".to_string(),
        luci_system_monitor::logs::LogFacility::Auth => "auth".to_string(),
        luci_system_monitor::logs::LogFacility::Syslog => "syslog".to_string(),
        luci_system_monitor::logs::LogFacility::Lpr => "lpr".to_string(),
        luci_system_monitor::logs::LogFacility::News => "news".to_string(),
        luci_system_monitor::logs::LogFacility::Uucp => "uucp".to_string(),
        luci_system_monitor::logs::LogFacility::Cron => "cron".to_string(),
        luci_system_monitor::logs::LogFacility::Authpriv => "authpriv".to_string(),
        luci_system_monitor::logs::LogFacility::Ftp => "ftp".to_string(),
        luci_system_monitor::logs::LogFacility::Local0 => "local0".to_string(),
        luci_system_monitor::logs::LogFacility::Local1 => "local1".to_string(),
        luci_system_monitor::logs::LogFacility::Local2 => "local2".to_string(),
        luci_system_monitor::logs::LogFacility::Local3 => "local3".to_string(),
        luci_system_monitor::logs::LogFacility::Local4 => "local4".to_string(),
        luci_system_monitor::logs::LogFacility::Local5 => "local5".to_string(),
        luci_system_monitor::logs::LogFacility::Local6 => "local6".to_string(),
        luci_system_monitor::logs::LogFacility::Local7 => "local7".to_string(),
    }
}

/// Format timestamp for display
fn format_timestamp(timestamp: &std::time::SystemTime) -> String {
    use std::time::UNIX_EPOCH;
    
    let duration = timestamp.duration_since(UNIX_EPOCH)
        .unwrap_or_default();
    
    let datetime = chrono::DateTime::<chrono::Utc>::from_timestamp(
        duration.as_secs() as i64,
        duration.subsec_nanos()
    ).unwrap_or_default();
    
    datetime.format("%Y-%m-%d %H:%M:%S").to_string()
}
