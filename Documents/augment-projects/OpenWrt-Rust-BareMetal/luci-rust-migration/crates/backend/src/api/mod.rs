//! API endpoints for LuCI backend
//! 
//! This module contains all REST API endpoints for system management,
//! network configuration, package management, and other OpenWrt functions.

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::{IntoResponse, Json},
};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use crate::AppState;

pub mod system;
pub mod network;
pub mod packages;
pub mod processes;
pub mod uci;
pub mod auth;

/// Standard API response wrapper
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: chrono::Utc::now(),
        }
    }
    
    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
            timestamp: chrono::Utc::now(),
        }
    }
}

impl<T> IntoResponse for ApiResponse<T>
where
    T: Serialize,
{
    fn into_response(self) -> axum::response::Response {
        let status = if self.success {
            StatusCode::OK
        } else {
            StatusCode::INTERNAL_SERVER_ERROR
        };
        
        (status, Json(self)).into_response()
    }
}

/// API error types
#[derive(Debug, thiserror::Error)]
pub enum ApiError {
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Bad request: {0}")]
    BadRequest(String),
    
    #[error("Unauthorized: {0}")]
    Unauthorized(String),
    
    #[error("Forbidden: {0}")]
    Forbidden(String),
    
    #[error("Internal server error: {0}")]
    Internal(String),
    
    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),
}

impl IntoResponse for ApiError {
    fn into_response(self) -> axum::response::Response {
        let (status, message) = match self {
            ApiError::NotFound(msg) => (StatusCode::NOT_FOUND, msg),
            ApiError::BadRequest(msg) => (StatusCode::BAD_REQUEST, msg),
            ApiError::Unauthorized(msg) => (StatusCode::UNAUTHORIZED, msg),
            ApiError::Forbidden(msg) => (StatusCode::FORBIDDEN, msg),
            ApiError::Internal(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
            ApiError::ServiceUnavailable(msg) => (StatusCode::SERVICE_UNAVAILABLE, msg),
        };
        
        let response = ApiResponse::<()>::error(message);
        (status, Json(response)).into_response()
    }
}

/// Common query parameters for pagination
#[derive(Debug, Deserialize)]
pub struct PaginationQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub sort: Option<String>,
    pub order: Option<String>,
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            limit: Some(20),
            sort: None,
            order: Some("asc".to_string()),
        }
    }
}

/// Paginated response wrapper
#[derive(Debug, Serialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: u32,
    pub page: u32,
    pub limit: u32,
    pub pages: u32,
}

impl<T> PaginatedResponse<T> {
    pub fn new(items: Vec<T>, total: u32, page: u32, limit: u32) -> Self {
        let pages = (total + limit - 1) / limit; // Ceiling division
        
        Self {
            items,
            total,
            page,
            limit,
            pages,
        }
    }
}

/// Common search parameters
#[derive(Debug, Deserialize)]
pub struct SearchQuery {
    pub q: Option<String>,
    pub category: Option<String>,
    pub status: Option<String>,
    #[serde(flatten)]
    pub pagination: PaginationQuery,
}

/// Health check response
#[derive(Debug, Serialize)]
pub struct HealthResponse {
    pub status: String,
    pub version: String,
    pub uptime_seconds: u64,
    pub mode: String,
    pub memory_usage: Option<u64>,
    pub cpu_usage: Option<f32>,
}

/// Generic success response
#[derive(Debug, Serialize)]
pub struct SuccessResponse {
    pub message: String,
}

impl SuccessResponse {
    pub fn new(message: impl Into<String>) -> Self {
        Self {
            message: message.into(),
        }
    }
}

/// Utility functions for API handlers
pub mod utils {
    use super::*;
    
    /// Extract pagination parameters with defaults
    pub fn extract_pagination(query: &PaginationQuery) -> (u32, u32) {
        let page = query.page.unwrap_or(1).max(1);
        let limit = query.limit.unwrap_or(20).min(100).max(1);
        (page, limit)
    }
    
    /// Calculate offset for database queries
    pub fn calculate_offset(page: u32, limit: u32) -> u32 {
        (page - 1) * limit
    }
    
    /// Validate sort field against allowed fields
    pub fn validate_sort_field(field: &str, allowed: &[&str]) -> bool {
        allowed.contains(&field)
    }
    
    /// Parse sort order
    pub fn parse_sort_order(order: Option<&String>) -> String {
        match order.map(|s| s.to_lowercase()).as_deref() {
            Some("desc") => "DESC".to_string(),
            _ => "ASC".to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_api_response_success() {
        let response = ApiResponse::success("test data");
        assert!(response.success);
        assert_eq!(response.data, Some("test data"));
        assert!(response.error.is_none());
    }
    
    #[test]
    fn test_api_response_error() {
        let response = ApiResponse::<()>::error("test error".to_string());
        assert!(!response.success);
        assert!(response.data.is_none());
        assert_eq!(response.error, Some("test error".to_string()));
    }
    
    #[test]
    fn test_pagination_defaults() {
        let query = PaginationQuery::default();
        assert_eq!(query.page, Some(1));
        assert_eq!(query.limit, Some(20));
        assert_eq!(query.order, Some("asc".to_string()));
    }
    
    #[test]
    fn test_paginated_response() {
        let items = vec!["item1", "item2", "item3"];
        let response = PaginatedResponse::new(items, 100, 1, 20);
        assert_eq!(response.total, 100);
        assert_eq!(response.pages, 5);
        assert_eq!(response.items.len(), 3);
    }
    
    #[test]
    fn test_extract_pagination() {
        let query = PaginationQuery {
            page: Some(2),
            limit: Some(50),
            sort: None,
            order: None,
        };
        
        let (page, limit) = utils::extract_pagination(&query);
        assert_eq!(page, 2);
        assert_eq!(limit, 50);
    }
    
    #[test]
    fn test_calculate_offset() {
        assert_eq!(utils::calculate_offset(1, 20), 0);
        assert_eq!(utils::calculate_offset(2, 20), 20);
        assert_eq!(utils::calculate_offset(3, 10), 20);
    }
}
