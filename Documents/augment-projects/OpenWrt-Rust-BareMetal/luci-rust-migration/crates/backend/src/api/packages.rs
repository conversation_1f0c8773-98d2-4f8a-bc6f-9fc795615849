//! Package management API endpoints

use axum::{
    extract::{Path, Query, State},
    response::J<PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use crate::{AppState, api::{ApiResponse, ApiError, SearchQuery, PaginatedResponse}};

/// Package information
#[derive(Debug, Serialize, Deserialize)]
pub struct Package {
    pub name: String,
    pub version: String,
    pub description: String,
    pub size: u64,
    pub installed: bool,
    pub category: String,
    pub dependencies: Vec<String>,
    pub conflicts: Vec<String>,
}

/// Package operation request
#[derive(Debug, Deserialize)]
pub struct PackageOperationRequest {
    pub packages: Vec<String>,
    pub force: Option<bool>,
    pub no_deps: Option<bool>,
}

/// Package operation response
#[derive(Debug, Serialize)]
pub struct PackageOperationResponse {
    pub success: bool,
    pub message: String,
    pub affected_packages: Vec<String>,
    pub errors: Vec<String>,
}

/// Repository information
#[derive(Debug, Serialize, Deserialize)]
pub struct Repository {
    pub name: String,
    pub url: String,
    pub enabled: bool,
    pub priority: u32,
    pub description: Option<String>,
}

/// List all packages
pub async fn list_packages(
    Query(query): Query<SearchQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<Package>>>, ApiError> {
    // Mock implementation with sample packages
    let mut packages = vec![
        Package {
            name: "wget".to_string(),
            version: "1.21.3-1".to_string(),
            description: "Non-interactive network downloader".to_string(),
            size: 1024 * 300,
            installed: false,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libpcre".to_string()],
            conflicts: vec![],
        },
        Package {
            name: "curl".to_string(),
            version: "7.85.0-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 500,
            installed: true,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            conflicts: vec![],
        },
        Package {
            name: "nano".to_string(),
            version: "6.4-1".to_string(),
            description: "Small and friendly text editor".to_string(),
            size: 1024 * 200,
            installed: false,
            category: "Utilities".to_string(),
            dependencies: vec!["libc".to_string()],
            conflicts: vec![],
        },
    ];

    // Apply search filter if provided
    if let Some(search_query) = &query.q {
        packages.retain(|p| {
            p.name.contains(search_query) || p.description.contains(search_query)
        });
    }

    // Apply category filter if provided
    if let Some(category) = &query.category {
        packages.retain(|p| p.category == *category);
    }

    let total = packages.len() as u32;
    let page = query.pagination.page.unwrap_or(1);
    let limit = query.pagination.limit.unwrap_or(20);

    let response = PaginatedResponse::new(packages, total, page, limit);
    Ok(Json(ApiResponse::success(response)))
}

/// Search packages
pub async fn search_packages(
    Query(query): Query<SearchQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<Package>>>, ApiError> {
    // Delegate to list_packages with search functionality
    list_packages(Query(query), State(state)).await
}

/// Get specific package
pub async fn get_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Package>>, ApiError> {
    // Mock implementation with sample packages
    match name.as_str() {
        "wget" => Ok(Json(ApiResponse::success(Package {
            name: "wget".to_string(),
            version: "1.21.3-1".to_string(),
            description: "Non-interactive network downloader".to_string(),
            size: 1024 * 300,
            installed: false,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libpcre".to_string()],
            conflicts: vec![],
        }))),
        "curl" => Ok(Json(ApiResponse::success(Package {
            name: "curl".to_string(),
            version: "7.85.0-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 500,
            installed: true,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            conflicts: vec![],
        }))),
        "nano" => Ok(Json(ApiResponse::success(Package {
            name: "nano".to_string(),
            version: "6.4-1".to_string(),
            description: "Small and friendly text editor".to_string(),
            size: 1024 * 200,
            installed: false,
            category: "Utilities".to_string(),
            dependencies: vec!["libc".to_string()],
            conflicts: vec![],
        }))),
        _ => Err(ApiError::NotFound(format!("Package {} not found", name))),
    }
}

/// Install package
pub async fn install_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully installed package: {}", name),
        affected_packages: vec![name],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Remove package
pub async fn remove_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully removed package: {}", name),
        affected_packages: vec![name],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Install multiple packages
pub async fn install_packages(
    State(state): State<Arc<AppState>>,
    Json(request): Json<PackageOperationRequest>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully installed {} packages", request.packages.len()),
        affected_packages: request.packages,
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Remove multiple packages
pub async fn remove_packages(
    State(state): State<Arc<AppState>>,
    Json(request): Json<PackageOperationRequest>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully removed {} packages", request.packages.len()),
        affected_packages: request.packages,
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Get installed packages
pub async fn get_installed_packages(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<Package>>>, ApiError> {
    // Mock implementation - return only installed packages
    let installed_packages = vec![
        Package {
            name: "curl".to_string(),
            version: "7.85.0-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 500,
            installed: true,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            conflicts: vec![],
        },
    ];
    Ok(Json(ApiResponse::success(installed_packages)))
}

/// Get repositories
pub async fn get_repositories(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<Repository>>>, ApiError> {
    // Mock implementation
    let repositories = vec![
        Repository {
            name: "openwrt_core".to_string(),
            url: "https://downloads.openwrt.org/releases/23.05.0/targets/ath79/generic/packages".to_string(),
            enabled: true,
            priority: 1,
            description: Some("OpenWrt core packages".to_string()),
        },
        Repository {
            name: "openwrt_packages".to_string(),
            url: "https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/packages".to_string(),
            enabled: true,
            priority: 2,
            description: Some("OpenWrt additional packages".to_string()),
        },
    ];
    Ok(Json(ApiResponse::success(repositories)))
}

/// Update package lists
pub async fn update_package_lists(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: "Package lists updated successfully".to_string(),
        affected_packages: vec![],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}
