//! Package management API endpoints

use axum::{
    extract::{Path, Query, State},
    response::J<PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use crate::{AppState, api::{ApiResponse, ApiError, SearchQuery, PaginatedResponse}};

/// Package information
#[derive(Debug, Serialize, Deserialize)]
pub struct Package {
    pub name: String,
    pub version: String,
    pub description: String,
    pub size: u64,
    pub installed: bool,
    pub category: String,
    pub dependencies: Vec<String>,
    pub conflicts: Vec<String>,
}

/// Package operation request
#[derive(Debug, Deserialize)]
pub struct PackageOperationRequest {
    pub packages: Vec<String>,
    pub force: Option<bool>,
    pub no_deps: Option<bool>,
}

/// Package operation response
#[derive(Debug, Serialize)]
pub struct PackageOperationResponse {
    pub success: bool,
    pub message: String,
    pub affected_packages: Vec<String>,
    pub errors: Vec<String>,
}

/// Repository information
#[derive(Debug, Serialize, Deserialize)]
pub struct Repository {
    pub name: String,
    pub url: String,
    pub enabled: bool,
    pub priority: u32,
    pub description: Option<String>,
}

/// Removal safety query parameters
#[derive(Debug, Deserialize)]
pub struct RemovalSafetyQuery {
    pub packages: String, // Comma-separated list of package names
}

/// Removal safety information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemovalSafetyInfo {
    pub packages_to_remove: Vec<String>,
    pub affected_packages: Vec<String>,
    pub broken_dependencies: Vec<String>,
    pub safe_to_remove: bool,
}

/// Package update information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageUpdate {
    pub name: String,
    pub current_version: String,
    pub available_version: String,
    pub description: String,
    pub size: u64,
    pub category: String,
    pub dependencies: Vec<String>,
    pub changelog: Option<String>,
    pub security_update: bool,
}

/// Update operation request
#[derive(Debug, Deserialize)]
pub struct UpdateOperationRequest {
    pub packages: Vec<String>,
    pub force: Option<bool>,
    pub no_deps: Option<bool>,
}

/// List all packages
pub async fn list_packages(
    Query(query): Query<SearchQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<Package>>>, ApiError> {
    // Mock implementation with sample packages
    let mut packages = vec![
        Package {
            name: "wget".to_string(),
            version: "1.21.3-1".to_string(),
            description: "Non-interactive network downloader".to_string(),
            size: 1024 * 300,
            installed: false,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libpcre".to_string()],
            conflicts: vec![],
        },
        Package {
            name: "curl".to_string(),
            version: "7.85.0-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 500,
            installed: true,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            conflicts: vec![],
        },
        Package {
            name: "nano".to_string(),
            version: "6.4-1".to_string(),
            description: "Small and friendly text editor".to_string(),
            size: 1024 * 200,
            installed: false,
            category: "Utilities".to_string(),
            dependencies: vec!["libc".to_string()],
            conflicts: vec![],
        },
    ];

    // Apply search filter if provided
    if let Some(search_query) = &query.q {
        packages.retain(|p| {
            p.name.contains(search_query) || p.description.contains(search_query)
        });
    }

    // Apply category filter if provided
    if let Some(category) = &query.category {
        packages.retain(|p| p.category == *category);
    }

    let total = packages.len() as u32;
    let page = query.pagination.page.unwrap_or(1);
    let limit = query.pagination.limit.unwrap_or(20);

    let response = PaginatedResponse::new(packages, total, page, limit);
    Ok(Json(ApiResponse::success(response)))
}

/// Search packages
pub async fn search_packages(
    Query(query): Query<SearchQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PaginatedResponse<Package>>>, ApiError> {
    // Delegate to list_packages with search functionality
    list_packages(Query(query), State(state)).await
}

/// Get specific package
pub async fn get_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Package>>, ApiError> {
    // Mock implementation with sample packages
    match name.as_str() {
        "wget" => Ok(Json(ApiResponse::success(Package {
            name: "wget".to_string(),
            version: "1.21.3-1".to_string(),
            description: "Non-interactive network downloader".to_string(),
            size: 1024 * 300,
            installed: false,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libpcre".to_string()],
            conflicts: vec![],
        }))),
        "curl" => Ok(Json(ApiResponse::success(Package {
            name: "curl".to_string(),
            version: "7.85.0-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 500,
            installed: true,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            conflicts: vec![],
        }))),
        "nano" => Ok(Json(ApiResponse::success(Package {
            name: "nano".to_string(),
            version: "6.4-1".to_string(),
            description: "Small and friendly text editor".to_string(),
            size: 1024 * 200,
            installed: false,
            category: "Utilities".to_string(),
            dependencies: vec!["libc".to_string()],
            conflicts: vec![],
        }))),
        _ => Err(ApiError::NotFound(format!("Package {} not found", name))),
    }
}

/// Install package
pub async fn install_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully installed package: {}", name),
        affected_packages: vec![name],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Remove package
pub async fn remove_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully removed package: {}", name),
        affected_packages: vec![name],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Install multiple packages
pub async fn install_packages(
    State(state): State<Arc<AppState>>,
    Json(request): Json<PackageOperationRequest>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully installed {} packages", request.packages.len()),
        affected_packages: request.packages,
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Remove multiple packages
pub async fn remove_packages(
    State(state): State<Arc<AppState>>,
    Json(request): Json<PackageOperationRequest>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully removed {} packages", request.packages.len()),
        affected_packages: request.packages,
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Check removal safety for packages
pub async fn check_removal_safety(
    Query(query): Query<RemovalSafetyQuery>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<RemovalSafetyInfo>>, ApiError> {
    // Mock implementation - in real implementation, this would use the dependency resolver
    let packages_to_remove: Vec<String> = query.packages.split(',').map(|s| s.trim().to_string()).collect();

    // Mock data for demonstration
    let mut affected_packages = Vec::new();
    let mut broken_dependencies = Vec::new();
    let mut safe_to_remove = true;

    // Simulate dependency checking
    for package in &packages_to_remove {
        match package.as_str() {
            "curl" => {
                // Simulate that some packages depend on curl
                affected_packages.push("wget".to_string());
                broken_dependencies.push("wget depends on curl".to_string());
                safe_to_remove = false;
            },
            "libc" => {
                // Simulate that many packages depend on libc
                affected_packages.extend(vec!["curl".to_string(), "nano".to_string(), "wget".to_string()]);
                broken_dependencies.extend(vec![
                    "curl depends on libc".to_string(),
                    "nano depends on libc".to_string(),
                    "wget depends on libc".to_string(),
                ]);
                safe_to_remove = false;
            },
            _ => {
                // Most packages are safe to remove
            }
        }
    }

    let response = RemovalSafetyInfo {
        packages_to_remove,
        affected_packages,
        broken_dependencies,
        safe_to_remove,
    };

    Ok(Json(ApiResponse::success(response)))
}

/// Get installed packages
pub async fn get_installed_packages(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<Package>>>, ApiError> {
    // Mock implementation - return only installed packages
    let installed_packages = vec![
        Package {
            name: "curl".to_string(),
            version: "7.85.0-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 500,
            installed: true,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            conflicts: vec![],
        },
    ];
    Ok(Json(ApiResponse::success(installed_packages)))
}

/// Get repositories
pub async fn get_repositories(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<Repository>>>, ApiError> {
    // Mock implementation
    let repositories = vec![
        Repository {
            name: "openwrt_core".to_string(),
            url: "https://downloads.openwrt.org/releases/23.05.0/targets/ath79/generic/packages".to_string(),
            enabled: true,
            priority: 1,
            description: Some("OpenWrt core packages".to_string()),
        },
        Repository {
            name: "openwrt_packages".to_string(),
            url: "https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/packages".to_string(),
            enabled: true,
            priority: 2,
            description: Some("OpenWrt additional packages".to_string()),
        },
    ];
    Ok(Json(ApiResponse::success(repositories)))
}

/// Update package lists
pub async fn update_package_lists(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: "Package lists updated successfully".to_string(),
        affected_packages: vec![],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Get available package updates
pub async fn get_available_updates(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<PackageUpdate>>>, ApiError> {
    // Mock implementation with sample updates
    let updates = vec![
        PackageUpdate {
            name: "curl".to_string(),
            current_version: "7.85.0-1".to_string(),
            available_version: "7.88.1-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            size: 1024 * 520,
            category: "Network".to_string(),
            dependencies: vec!["libc".to_string(), "libcurl".to_string()],
            changelog: Some("Security fixes and performance improvements".to_string()),
            security_update: true,
        },
        PackageUpdate {
            name: "nano".to_string(),
            current_version: "6.4-1".to_string(),
            available_version: "7.2-1".to_string(),
            description: "Small and friendly text editor".to_string(),
            size: 1024 * 220,
            category: "Utilities".to_string(),
            dependencies: vec!["libc".to_string()],
            changelog: Some("New features and bug fixes".to_string()),
            security_update: false,
        },
    ];
    Ok(Json(ApiResponse::success(updates)))
}

/// Upgrade a single package
pub async fn upgrade_package(
    Path(name): Path<String>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully upgraded package: {}", name),
        affected_packages: vec![name],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Upgrade multiple packages
pub async fn upgrade_packages(
    State(state): State<Arc<AppState>>,
    Json(request): Json<UpdateOperationRequest>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: format!("Successfully upgraded {} packages", request.packages.len()),
        affected_packages: request.packages,
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}

/// Upgrade all packages
pub async fn upgrade_all_packages(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<PackageOperationResponse>>, ApiError> {
    // Mock implementation
    let response = PackageOperationResponse {
        success: true,
        message: "Successfully upgraded all packages".to_string(),
        affected_packages: vec!["curl".to_string(), "nano".to_string()],
        errors: vec![],
    };
    Ok(Json(ApiResponse::success(response)))
}
