//! Process monitoring API endpoints

use axum::{
    extract::{State, Query},
    response::J<PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use crate::{AppState, api::{ApiResponse, ApiError}};
use luci_system_monitor::{SystemMonitor, ProcessInfo, ProcessStats, ProcessTreeNode, ProcessState};

/// Query parameters for process listing
#[derive(Debug, Deserialize)]
pub struct ProcessQuery {
    /// Limit number of results
    pub limit: Option<usize>,
    /// Sort by field (cpu, memory, name, pid)
    pub sort_by: Option<String>,
    /// Sort order (asc, desc)
    pub sort_order: Option<String>,
    /// Filter by process name
    pub name_filter: Option<String>,
    /// Filter by process state
    pub state_filter: Option<String>,
}

/// Process information response (re-export from system monitor)
pub use luci_system_monitor::ProcessInfo as ProcessInfoResponse;

/// Process statistics response (re-export from system monitor)
pub use luci_system_monitor::ProcessStats as ProcessStatsResponse;

/// Process tree response (re-export from system monitor)
pub use luci_system_monitor::ProcessTreeNode as ProcessTreeResponse;

/// Get all processes
pub async fn get_processes(
    State(state): State<Arc<AppState>>,
    Query(query): Query<ProcessQuery>,
) -> Result<Json<ApiResponse<Vec<ProcessInfoResponse>>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;
    
    let mut processes = monitor.process_monitor().get_all_processes()
        .map_err(|e| ApiError::Internal(format!("Failed to get processes: {}", e)))?;
    
    // Apply name filter
    if let Some(name_filter) = &query.name_filter {
        processes.retain(|p| p.name.to_lowercase().contains(&name_filter.to_lowercase()));
    }
    
    // Apply state filter
    if let Some(state_filter) = &query.state_filter {
        if let Ok(state) = parse_process_state(state_filter) {
            processes.retain(|p| p.state == state);
        }
    }
    
    // Sort processes
    let sort_by = query.sort_by.as_deref().unwrap_or("cpu");
    let sort_desc = query.sort_order.as_deref().unwrap_or("desc") == "desc";
    
    match sort_by {
        "cpu" => {
            processes.sort_by(|a, b| {
                if sort_desc {
                    b.cpu_percent.partial_cmp(&a.cpu_percent).unwrap_or(std::cmp::Ordering::Equal)
                } else {
                    a.cpu_percent.partial_cmp(&b.cpu_percent).unwrap_or(std::cmp::Ordering::Equal)
                }
            });
        },
        "memory" => {
            processes.sort_by(|a, b| {
                if sort_desc {
                    b.memory_rss.cmp(&a.memory_rss)
                } else {
                    a.memory_rss.cmp(&b.memory_rss)
                }
            });
        },
        "name" => {
            processes.sort_by(|a, b| {
                if sort_desc {
                    b.name.cmp(&a.name)
                } else {
                    a.name.cmp(&b.name)
                }
            });
        },
        "pid" => {
            processes.sort_by(|a, b| {
                if sort_desc {
                    b.pid.cmp(&a.pid)
                } else {
                    a.pid.cmp(&b.pid)
                }
            });
        },
        _ => {
            // Default to CPU sorting
            processes.sort_by(|a, b| {
                b.cpu_percent.partial_cmp(&a.cpu_percent).unwrap_or(std::cmp::Ordering::Equal)
            });
        }
    }
    
    // Apply limit
    if let Some(limit) = query.limit {
        processes.truncate(limit);
    }
    
    Ok(Json(ApiResponse::success(processes)))
}

/// Get process by PID
pub async fn get_process(
    State(state): State<Arc<AppState>>,
    axum::extract::Path(pid): axum::extract::Path<u32>,
) -> Result<Json<ApiResponse<ProcessInfoResponse>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;
    
    let process = monitor.process_monitor().get_process(pid)
        .map_err(|e| match e {
            luci_system_monitor::MonitorError::ProcessNotFound(_) => ApiError::NotFound("Process not found".to_string()),
            _ => ApiError::Internal(format!("Failed to get process: {}", e)),
        })?;
    
    Ok(Json(ApiResponse::success(process)))
}

/// Get process statistics
pub async fn get_process_stats(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<ProcessStatsResponse>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;
    
    let stats = monitor.process_monitor().get_process_stats()
        .map_err(|e| ApiError::Internal(format!("Failed to get process stats: {}", e)))?;
    
    Ok(Json(ApiResponse::success(stats)))
}

/// Get process tree
pub async fn get_process_tree(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<ProcessTreeResponse>>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;
    
    let tree = monitor.process_monitor().get_process_tree()
        .map_err(|e| ApiError::Internal(format!("Failed to get process tree: {}", e)))?;
    
    Ok(Json(ApiResponse::success(tree)))
}

/// Get top CPU processes
pub async fn get_top_cpu_processes(
    State(state): State<Arc<AppState>>,
    Query(query): Query<ProcessQuery>,
) -> Result<Json<ApiResponse<Vec<ProcessInfoResponse>>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;
    
    let limit = query.limit.unwrap_or(10);
    let processes = monitor.process_monitor().get_top_cpu_processes(limit)
        .map_err(|e| ApiError::Internal(format!("Failed to get top CPU processes: {}", e)))?;
    
    Ok(Json(ApiResponse::success(processes)))
}

/// Get top memory processes
pub async fn get_top_memory_processes(
    State(state): State<Arc<AppState>>,
    Query(query): Query<ProcessQuery>,
) -> Result<Json<ApiResponse<Vec<ProcessInfoResponse>>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;
    
    let limit = query.limit.unwrap_or(10);
    let processes = monitor.process_monitor().get_top_memory_processes(limit)
        .map_err(|e| ApiError::Internal(format!("Failed to get top memory processes: {}", e)))?;
    
    Ok(Json(ApiResponse::success(processes)))
}

/// Kill process by PID
pub async fn kill_process(
    State(state): State<Arc<AppState>>,
    axum::extract::Path(pid): axum::extract::Path<u32>,
    Json(payload): Json<KillProcessRequest>,
) -> Result<Json<ApiResponse<()>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;
    
    monitor.process_monitor().kill_process(pid, payload.signal)
        .map_err(|e| match e {
            luci_system_monitor::MonitorError::ProcessNotFound(_) => ApiError::NotFound("Process not found".to_string()),
            luci_system_monitor::MonitorError::PermissionDenied => ApiError::Forbidden("Permission denied".to_string()),
            _ => ApiError::Internal(format!("Failed to kill process: {}", e)),
        })?;
    
    Ok(Json(ApiResponse::success(())))
}

/// Kill process request
#[derive(Debug, Deserialize)]
pub struct KillProcessRequest {
    /// Signal to send (default: 15 for SIGTERM)
    pub signal: i32,
}

/// Parse process state from string
fn parse_process_state(state_str: &str) -> Result<ProcessState, ()> {
    match state_str.to_lowercase().as_str() {
        "running" | "r" => Ok(ProcessState::Running),
        "sleeping" | "s" => Ok(ProcessState::Sleeping),
        "waiting" | "d" => Ok(ProcessState::Waiting),
        "zombie" | "z" => Ok(ProcessState::Zombie),
        "stopped" | "t" => Ok(ProcessState::Stopped),
        "tracing" | "tracingstop" => Ok(ProcessState::TracingStop),
        "dead" | "x" => Ok(ProcessState::Dead),
        _ => Err(()),
    }
}
