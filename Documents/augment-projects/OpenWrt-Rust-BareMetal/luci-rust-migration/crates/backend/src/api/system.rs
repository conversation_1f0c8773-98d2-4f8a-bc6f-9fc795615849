//! System information and status API endpoints

use axum::{
    extract::State,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::{sync::Arc, collections::HashMap};
use crate::{AppState, api::{ApiResponse, ApiError}};
use luci_system_monitor::{SystemMonitor, MonitorResult};

/// System information response
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemInfo {
    pub hostname: String,
    pub kernel: String,
    pub architecture: String,
    pub cpu_model: String,
    pub cpu_cores: u32,
    pub memory_total: u64,
    pub memory_available: u64,
    pub storage_total: u64,
    pub storage_available: u64,
    pub uptime: u64,
    pub load_average: [f32; 3],
    pub openwrt_version: Option<String>,
    pub board_name: Option<String>,
}

/// System status response
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemStatus {
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub storage_usage: f32,
    pub network_interfaces: Vec<NetworkInterfaceStatus>,
    pub running_processes: u32,
    pub active_connections: u32,
    pub temperature: Option<f32>,
}

/// Network interface status
#[derive(Debug, Serialize, Deserialize)]
pub struct NetworkInterfaceStatus {
    pub name: String,
    pub status: String,
    pub ip_address: Option<String>,
    pub rx_bytes: u64,
    pub tx_bytes: u64,
    pub rx_packets: u64,
    pub tx_packets: u64,
}

/// Uptime information
#[derive(Debug, Serialize, Deserialize)]
pub struct UptimeInfo {
    pub uptime_seconds: u64,
    pub uptime_formatted: String,
    pub boot_time: chrono::DateTime<chrono::Utc>,
    pub load_average: [f32; 3],
}

/// Get system information
pub async fn get_system_info(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<SystemInfo>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let info = collect_system_info(&mut monitor).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect system info: {}", e)))?;

    Ok(Json(ApiResponse::success(info)))
}

/// Get system status
pub async fn get_system_status(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<SystemStatus>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let status = collect_system_status(&mut monitor).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect system status: {}", e)))?;

    Ok(Json(ApiResponse::success(status)))
}

/// Get uptime information
pub async fn get_uptime(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<UptimeInfo>>, ApiError> {
    let mut monitor = SystemMonitor::new()
        .map_err(|e| ApiError::Internal(format!("Failed to create system monitor: {}", e)))?;

    let uptime = collect_uptime_info(&mut monitor).await
        .map_err(|e| ApiError::Internal(format!("Failed to collect uptime info: {}", e)))?;

    Ok(Json(ApiResponse::success(uptime)))
}

/// Collect system information from the system
async fn collect_system_info(monitor: &mut SystemMonitor) -> anyhow::Result<SystemInfo> {
    // Get system info from monitor
    let system_info = monitor.get_system_info()
        .map_err(|e| anyhow::anyhow!("Failed to get system info: {}", e))?;

    // Get storage information
    let storage_usage = monitor.storage_monitor().get_all_usage()
        .map_err(|e| anyhow::anyhow!("Failed to get storage info: {}", e))?;

    let (storage_total, storage_available) = if let Some(root_storage) = storage_usage.first() {
        (root_storage.total, root_storage.available)
    } else {
        (0, 0)
    };

    // Get OpenWrt version if available
    let openwrt_version = tokio::fs::read_to_string("/etc/openwrt_version")
        .await
        .ok()
        .map(|s| s.trim().to_string());

    // Get board name if available
    let board_name = tokio::fs::read_to_string("/tmp/sysinfo/board_name")
        .await
        .ok()
        .map(|s| s.trim().to_string());

    Ok(SystemInfo {
        hostname: system_info.hostname,
        kernel: system_info.kernel_version,
        architecture: system_info.architecture,
        cpu_model: system_info.cpu_model,
        cpu_cores: system_info.cpu_cores,
        memory_total: system_info.total_memory,
        memory_available: system_info.total_memory, // Will be updated with actual available memory
        storage_total,
        storage_available,
        uptime: system_info.uptime.uptime_seconds,
        load_average: [
            system_info.load_average.one_minute as f32,
            system_info.load_average.five_minutes as f32,
            system_info.load_average.fifteen_minutes as f32,
        ],
        openwrt_version,
        board_name,
    })
}

/// Collect system status information
async fn collect_system_status(monitor: &mut SystemMonitor) -> anyhow::Result<SystemStatus> {
    // Get current metrics from monitor
    let metrics = monitor.get_current_metrics()
        .map_err(|e| anyhow::anyhow!("Failed to get system metrics: {}", e))?;

    // Convert network stats to our API format
    let network_interfaces = metrics.network_stats.into_iter().map(|stats| {
        NetworkInterfaceStatus {
            name: stats.interface.clone(),
            status: if stats.is_up { "up".to_string() } else { "down".to_string() },
            ip_address: None, // TODO: Get IP address from interface
            rx_bytes: stats.rx_bytes,
            tx_bytes: stats.tx_bytes,
            rx_packets: stats.rx_packets,
            tx_packets: stats.tx_packets,
        }
    }).collect();

    // Calculate storage usage percentage
    let storage_usage = if let Some(storage) = metrics.storage_usage.first() {
        if storage.total > 0 {
            ((storage.used as f32 / storage.total as f32) * 100.0)
        } else {
            0.0
        }
    } else {
        0.0
    };

    // Get temperature if available (placeholder for now)
    let temperature = get_temperature().await;

    Ok(SystemStatus {
        cpu_usage: metrics.cpu_usage.total_usage as f32,
        memory_usage: (metrics.memory_usage.used as f32 / metrics.memory_usage.total as f32) * 100.0,
        storage_usage,
        network_interfaces,
        running_processes: metrics.process_count,
        active_connections: 0, // TODO: Implement connection counting
        temperature,
    })
}

/// Collect uptime information
async fn collect_uptime_info(monitor: &mut SystemMonitor) -> anyhow::Result<UptimeInfo> {
    let system_info = monitor.get_system_info()
        .map_err(|e| anyhow::anyhow!("Failed to get system info: {}", e))?;

    let uptime_seconds = system_info.uptime.uptime_seconds;
    let uptime_formatted = format_uptime(uptime_seconds);
    let boot_time = chrono::DateTime::from(system_info.uptime.boot_time);
    let load_average = [
        system_info.load_average.one_minute as f32,
        system_info.load_average.five_minutes as f32,
        system_info.load_average.fifteen_minutes as f32,
    ];

    Ok(UptimeInfo {
        uptime_seconds,
        uptime_formatted,
        boot_time,
        load_average,
    })
}



/// Get system temperature
async fn get_temperature() -> Option<f32> {
    // This is a simplified implementation
    // In a real implementation, you'd read from thermal sensors
    None
}

/// Format uptime seconds into human-readable string
fn format_uptime(seconds: u64) -> String {
    let days = seconds / 86400;
    let hours = (seconds % 86400) / 3600;
    let minutes = (seconds % 3600) / 60;
    let secs = seconds % 60;
    
    if days > 0 {
        format!("{}d {}h {}m {}s", days, hours, minutes, secs)
    } else if hours > 0 {
        format!("{}h {}m {}s", hours, minutes, secs)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, secs)
    } else {
        format!("{}s", secs)
    }
}
