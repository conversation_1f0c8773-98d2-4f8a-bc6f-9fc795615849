//! LuCI Backend - HTTP Server and API Implementation
//!
//! This crate provides the HTTP server implementation using Axum with Leptos SSR
//! for the OpenWrt LuCI web interface migration.

use axum::{
    extract::{Path, Query, State},
    http::{StatusCode, HeaderMap},
    response::{Html, IntoResponse, Response},
    routing::{get, post, put, delete},
    Json, Router,
};
// Leptos integration will be added later
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, net::SocketAddr, sync::Arc};
use tokio::net::TcpListener;
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    services::ServeDir,
    trace::TraceLayer,
};
use tracing::{info, error, debug, warn};
use anyhow::{Result, Context};
use luci_auth_system::{AuthManager, AuthConfig};

pub mod config;
pub mod api;
pub mod middleware;
pub mod uhttpd;

pub use config::*;
pub use api::*;

/// Application state shared across handlers
pub struct AppState {
    pub config: ServerConfig,
    pub version: String,
    pub start_time: std::time::Instant,
    pub auth_manager: AuthManager,
}

impl AppState {
    pub fn new(config: ServerConfig) -> Result<Self> {
        // Create authentication configuration
        let auth_config = AuthConfig {
            jwt_secret: config.jwt_secret.clone().unwrap_or_else(|| {
                warn!("No JWT secret configured, using default (insecure for production)");
                "default-jwt-secret-change-in-production".to_string()
            }),
            session_timeout: config.session_timeout.unwrap_or(3600), // 1 hour default
            max_sessions_per_user: config.max_sessions_per_user.unwrap_or(5),
            enable_cleanup: true,
            cleanup_interval: 300, // 5 minutes
        };

        // Create authentication manager
        let auth_manager = AuthManager::new(auth_config)
            .context("Failed to create authentication manager")?;

        Ok(Self {
            config,
            version: env!("CARGO_PKG_VERSION").to_string(),
            start_time: std::time::Instant::now(),
            auth_manager,
        })
    }
}

/// HTTP server implementation
pub struct LuciServer {
    config: ServerConfig,
    app_state: Arc<AppState>,
}

impl LuciServer {
    /// Create a new server instance
    pub fn new(config: ServerConfig) -> Result<Self> {
        let app_state = Arc::new(AppState::new(config.clone())?);

        Ok(Self {
            config,
            app_state,
        })
    }

    /// Build the Axum router with all routes
    pub fn build_router(&self) -> Router {
        // Build the main router without Leptos for now
        Router::new()
            // API routes
            .nest("/api", self.build_api_routes())
            // Static file serving
            .nest_service("/assets", ServeDir::new("static/assets"))
            .nest_service("/css", ServeDir::new("static/css"))
            .nest_service("/js", ServeDir::new("static/js"))
            // Health check
            .route("/health", get(health_check))
            // Root route
            .route("/", get(serve_index))
            // Add middleware
            .layer(
                ServiceBuilder::new()
                    .layer(TraceLayer::new_for_http())
                    .layer(CorsLayer::permissive())
                    .layer(middleware::request_id::RequestIdLayer::new())
                    .layer(middleware::auth::AuthLayer::new(self.config.auth_required))
            )
            // Add CSRF middleware after auth middleware
            .layer(middleware::csrf::CsrfLayer::new(self.app_state.clone()))
            .with_state(self.app_state.clone())
    }

    /// Build API routes
    fn build_api_routes(&self) -> Router<Arc<AppState>> {
        Router::new()
            // System information
            .route("/system/info", get(api::system::get_system_info))
            .route("/system/status", get(api::system::get_system_status))
            .route("/system/uptime", get(api::system::get_uptime))
            // Device information
            .route("/device/info", get(api::device::get_device_info))
            // System logs
            .route("/logs", get(api::logs::get_logs))

            // Process monitoring
            .route("/processes", get(api::processes::get_processes))
            .route("/processes/stats", get(api::processes::get_process_stats))
            .route("/processes/tree", get(api::processes::get_process_tree))
            .route("/processes/top/cpu", get(api::processes::get_top_cpu_processes))
            .route("/processes/top/memory", get(api::processes::get_top_memory_processes))
            .route("/processes/:pid", get(api::processes::get_process))
            .route("/processes/:pid/kill", post(api::processes::kill_process))

            // Network configuration and monitoring
            .route("/network/stats", get(api::network::get_network_stats))
            .route("/network/interfaces", get(api::network::get_interfaces))
            .route("/network/interfaces/:name", get(api::network::get_interface))
            .route("/network/interfaces/:name", put(api::network::update_interface))

            // Package management
            .route("/packages", get(api::packages::list_packages))
            .route("/packages/search", get(api::packages::search_packages))
            .route("/packages/:name", get(api::packages::get_package))
            .route("/packages/:name/install", post(api::packages::install_package))
            .route("/packages/:name/remove", delete(api::packages::remove_package))
            .route("/packages/removal-safety", get(api::packages::check_removal_safety))
            .route("/packages/updates", get(api::packages::get_available_updates))
            .route("/packages/:name/upgrade", post(api::packages::upgrade_package))
            .route("/packages/upgrade", post(api::packages::upgrade_packages))
            .route("/packages/upgrade-all", post(api::packages::upgrade_all_packages))
            .route("/packages/update-lists", post(api::packages::update_package_lists))

            // UCI configuration
            .route("/uci/:config", get(api::uci::get_config))
            .route("/uci/:config", put(api::uci::set_config))
            .route("/uci/:config/:section", get(api::uci::get_section))
            .route("/uci/:config/:section", put(api::uci::set_section))

            // Authentication
            .route("/auth/login", post(api::auth::login))
            .route("/auth/logout", post(api::auth::logout))
            .route("/auth/status", get(api::auth::get_auth_status))
            .route("/auth/refresh", post(api::auth::refresh_token))
            .route("/auth/change-password", post(api::auth::change_password))
            .route("/auth/validate-password", post(api::auth::validate_password))
            .route("/auth/password-policy", get(api::auth::get_password_policy))

    }

    /// Start the HTTP server
    pub async fn start(&self) -> Result<()> {
        let addr = SocketAddr::new(
            self.config.bind_address.parse()
                .context("Invalid bind address")?,
            self.config.port
        );

        info!("Starting LuCI server on {}", addr);
        info!("Server mode: {:?}", self.config.mode);
        info!("Static files: {}", self.config.static_dir);

        let app = self.build_router();
        let listener = TcpListener::bind(addr).await
            .context("Failed to bind to address")?;

        info!("Server listening on {}", addr);

        axum::serve(listener, app)
            .await
            .context("Server error")?;

        Ok(())
    }
}

/// Serve the main CSS file
async fn serve_main_css() -> impl IntoResponse {
    match tokio::fs::read_to_string("target/site/main.css").await {
        Ok(css) => (
            StatusCode::OK,
            [("content-type", "text/css")],
            css
        ).into_response(),
        Err(_) => (
            StatusCode::NOT_FOUND,
            "CSS file not found"
        ).into_response(),
    }
}

/// Health check endpoint
async fn health_check(State(state): State<Arc<AppState>>) -> impl IntoResponse {
    let uptime = state.start_time.elapsed().as_secs();

    Json(serde_json::json!({
        "status": "healthy",
        "version": state.version,
        "uptime_seconds": uptime,
        "mode": format!("{:?}", state.config.mode)
    }))
}

/// Serve the main index page
async fn serve_index() -> impl IntoResponse {
    axum::response::Html(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenWrt LuCI - Rust Edition</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
        .header { background-color: #2563eb; color: white; padding: 1rem; }
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1.5rem; }
        .card h2 { margin-top: 0; color: #1f2937; }
        .card a { color: #2563eb; text-decoration: none; }
        .card a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="header">
        <h1>OpenWrt LuCI - Rust Edition</h1>
    </div>
    <div class="container">
        <div class="grid">
            <div class="card">
                <h2>System Status</h2>
                <p>System monitoring and information</p>
                <a href="/api/system/info">View System Info API</a>
            </div>
            <div class="card">
                <h2>Network</h2>
                <p>Network configuration and monitoring</p>
                <a href="/api/network/interfaces">View Network API</a>
            </div>
            <div class="card">
                <h2>Packages</h2>
                <p>Package management</p>
                <a href="/api/packages/search">View Packages API</a>
            </div>
            <div class="card">
                <h2>Health Check</h2>
                <p>Server health and status</p>
                <a href="/health">View Health Check</a>
            </div>
        </div>
    </div>
</body>
</html>
    "#)
}
