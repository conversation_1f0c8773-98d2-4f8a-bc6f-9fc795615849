//! CSRF protection middleware
//!
//! Provides Cross-Site Request Forgery protection for state-changing operations.
//! Validates CSRF tokens for POST, PUT, DELETE, and PATCH requests.

use axum::{
    extract::Request,
    http::{HeaderMap, Method, StatusCode},
    response::{IntoResponse, Response},
};
use tower::{Layer, Service};
use std::sync::Arc;
use luci_auth_system::Session;
use crate::AppState;

/// CSRF middleware layer
#[derive(Clone)]
pub struct CsrfLayer {
    app_state: Arc<AppState>,
}

impl CsrfLayer {
    pub fn new(app_state: Arc<AppState>) -> Self {
        Self { app_state }
    }
}

impl<S> Layer<S> for CsrfLayer {
    type Service = CsrfService<S>;

    fn layer(&self, inner: S) -> Self::Service {
        CsrfService {
            inner,
            app_state: self.app_state.clone(),
        }
    }
}

/// CSRF service
#[derive(Clone)]
pub struct CsrfService<S> {
    inner: S,
    app_state: Arc<AppState>,
}

impl<S> Service<Request> for CsrfService<S>
where
    S: Service<Request, Response = Response> + Clone + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = std::pin::Pin<Box<dyn std::future::Future<Output = Result<Self::Response, Self::Error>> + Send>>;

    fn poll_ready(&mut self, cx: &mut std::task::Context<'_>) -> std::task::Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request) -> Self::Future {
        let method = request.method().clone();
        let path = request.uri().path().to_string();

        // Skip CSRF protection for safe methods and certain paths
        if is_safe_method(&method) || should_skip_csrf(&path) {
            let mut inner = self.inner.clone();
            return Box::pin(async move {
                inner.call(request).await
            });
        }

        // Extract session from request extensions (set by auth middleware)
        let session = request.extensions().get::<Session>().cloned();

        if let Some(session) = session {
            // Extract CSRF token from headers or form data
            let csrf_token = extract_csrf_token(&request);

            if let Some(token) = csrf_token {
                if session.validate_csrf_token(&token) {
                    // CSRF token is valid, proceed with request
                    let mut inner = self.inner.clone();
                    return Box::pin(async move {
                        inner.call(request).await
                    });
                }
            }

            // CSRF token is missing or invalid
            tracing::warn!(
                "CSRF token validation failed for {} {} from session {}",
                method,
                path,
                session.id
            );

            return Box::pin(async move {
                Ok(StatusCode::FORBIDDEN.into_response())
            });
        }

        // No session found, but auth middleware should have handled this
        // This shouldn't happen if auth middleware is properly configured
        tracing::error!("CSRF middleware called without session for protected endpoint");
        Box::pin(async move {
            Ok(StatusCode::UNAUTHORIZED.into_response())
        })
    }
}

/// Check if HTTP method is considered safe (doesn't modify state)
fn is_safe_method(method: &Method) -> bool {
    matches!(method, &Method::GET | &Method::HEAD | &Method::OPTIONS)
}

/// Check if path should skip CSRF protection
fn should_skip_csrf(path: &str) -> bool {
    // Skip CSRF for certain paths that don't need protection
    path.starts_with("/health")
        || path.starts_with("/api/auth/login")
        || path.starts_with("/api/auth/status")
        || path.starts_with("/api/auth/csrf-token") // CSRF token endpoint itself
        || path.starts_with("/assets")
        || path.starts_with("/pkg")
        || path.ends_with(".css")
        || path.ends_with(".js")
        || path == "/"
}

/// Extract CSRF token from request
fn extract_csrf_token(request: &Request) -> Option<String> {
    // Try to get token from X-CSRF-Token header first
    if let Some(header_value) = request.headers().get("x-csrf-token") {
        if let Ok(token) = header_value.to_str() {
            return Some(token.to_string());
        }
    }

    // Try to get token from X-Requested-With header (alternative approach)
    if let Some(header_value) = request.headers().get("x-requested-with") {
        if header_value == "XMLHttpRequest" {
            // For AJAX requests, also check for token in custom header
            if let Some(token_header) = request.headers().get("x-csrf-token") {
                if let Ok(token) = token_header.to_str() {
                    return Some(token.to_string());
                }
            }
        }
    }

    // TODO: For form submissions, we could also extract from form data
    // This would require parsing the request body, which is more complex
    // For now, we'll rely on header-based tokens for API calls
    
    None
}

/// CSRF token response
#[derive(serde::Serialize)]
pub struct CsrfTokenResponse {
    pub csrf_token: String,
}



#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_safe_method() {
        assert!(is_safe_method(&Method::GET));
        assert!(is_safe_method(&Method::HEAD));
        assert!(is_safe_method(&Method::OPTIONS));
        assert!(!is_safe_method(&Method::POST));
        assert!(!is_safe_method(&Method::PUT));
        assert!(!is_safe_method(&Method::DELETE));
        assert!(!is_safe_method(&Method::PATCH));
    }

    #[test]
    fn test_should_skip_csrf() {
        assert!(should_skip_csrf("/health"));
        assert!(should_skip_csrf("/api/auth/login"));
        assert!(should_skip_csrf("/api/auth/status"));
        assert!(should_skip_csrf("/api/auth/csrf-token"));
        assert!(should_skip_csrf("/assets/style.css"));
        assert!(should_skip_csrf("/pkg/something"));
        assert!(should_skip_csrf("/"));
        
        assert!(!should_skip_csrf("/api/network/interfaces/eth0"));
        assert!(!should_skip_csrf("/api/packages/install"));
        assert!(!should_skip_csrf("/api/uci/network"));
    }
}
