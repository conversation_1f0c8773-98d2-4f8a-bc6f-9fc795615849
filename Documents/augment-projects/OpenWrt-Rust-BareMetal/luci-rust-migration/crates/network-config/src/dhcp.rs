//! DHCP Configuration Management
//!
//! This module provides DHCP server configuration management for OpenWrt systems.

use crate::{NetworkError, NetworkResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::{Ipv4Addr, Ipv6Addr};
use luci_uci_bindings::ConfigManager;

/// DHCP lease time units
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LeaseTimeUnit {
    Minutes,
    Hours,
    Days,
    Infinite,
}

/// DHCP lease time
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LeaseTime {
    pub value: u32,
    pub unit: LeaseTimeUnit,
}

impl std::fmt::Display for LeaseTime {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self.unit {
            LeaseTimeUnit::Minutes => write!(f, "{}m", self.value),
            LeaseTimeUnit::Hours => write!(f, "{}h", self.value),
            LeaseTimeUnit::Days => write!(f, "{}d", self.value),
            LeaseTimeUnit::Infinite => write!(f, "infinite"),
        }
    }
}

impl Default for LeaseTime {
    fn default() -> Self {
        Self {
            value: 12,
            unit: LeaseTimeUnit::Hours,
        }
    }
}

/// DHCP static lease
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StaticLease {
    pub name: String,
    pub mac: String,
    pub ip: Ipv4Addr,
    pub hostname: Option<String>,
}

/// DHCP configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DhcpConfig {
    pub interface: String,
    pub start: u32,
    pub limit: u32,
    pub leasetime: LeaseTime,
    pub dhcpv6: Option<String>,
    pub ra: Option<String>,
    pub ra_management: Option<u32>,
    pub ra_preference: Option<String>,
    pub dns: Vec<Ipv4Addr>,
    pub domain: Option<String>,
    pub local: Option<String>,
    pub logqueries: bool,
    pub static_leases: Vec<StaticLease>,
}

impl Default for DhcpConfig {
    fn default() -> Self {
        Self {
            interface: String::new(),
            start: 100,
            limit: 150,
            leasetime: LeaseTime::default(),
            dhcpv6: None,
            ra: None,
            ra_management: None,
            ra_preference: None,
            dns: vec!["*******".parse().unwrap(), "*******".parse().unwrap()],
            domain: None,
            local: None,
            logqueries: false,
            static_leases: Vec::new(),
        }
    }
}

/// DHCP lease information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DhcpLease {
    pub mac: String,
    pub ip: Ipv4Addr,
    pub hostname: Option<String>,
    pub expires: u64,
    pub client_id: Option<String>,
}

/// DHCP manager
pub struct DhcpManager {
    config_manager: ConfigManager,
    configs: HashMap<String, DhcpConfig>,
    active_leases: Vec<DhcpLease>,
}

impl DhcpManager {
    /// Create a new DHCP manager
    pub fn new() -> NetworkResult<Self> {
        Ok(Self {
            config_manager: ConfigManager::new()?,
            configs: HashMap::new(),
            active_leases: Vec::new(),
        })
    }

    /// Load DHCP configuration for an interface
    pub fn load_config(&mut self, interface: &str) -> NetworkResult<DhcpConfig> {
        let uci_config = self.config_manager.get_dhcp_config(interface)?;
        
        let leasetime = Self::parse_lease_time(&uci_config.leasetime);
        
        let config = DhcpConfig {
            interface: interface.to_string(),
            start: uci_config.start,
            limit: uci_config.limit,
            leasetime,
            dhcpv6: uci_config.dhcpv6,
            ra: uci_config.ra,
            ra_management: None, // TODO: Get from UCI
            ra_preference: None, // TODO: Get from UCI
            dns: vec![], // TODO: Parse from UCI
            domain: None, // TODO: Get from UCI
            local: None, // TODO: Get from UCI
            logqueries: false, // TODO: Get from UCI
            static_leases: Vec::new(), // TODO: Load from UCI
        };

        self.configs.insert(interface.to_string(), config.clone());
        Ok(config)
    }

    /// Save DHCP configuration
    pub fn save_config(&mut self, config: &DhcpConfig) -> NetworkResult<()> {
        let uci_config = luci_uci_bindings::DhcpConfig {
            interface: config.interface.clone(),
            start: config.start,
            limit: config.limit,
            leasetime: config.leasetime.to_string(),
            dhcpv6: config.dhcpv6.clone(),
            ra: config.ra.clone(),
        };

        self.config_manager.set_dhcp_config(&uci_config)?;
        self.configs.insert(config.interface.clone(), config.clone());
        
        Ok(())
    }

    /// Get DHCP configuration for an interface
    pub fn get_config(&self, interface: &str) -> Option<&DhcpConfig> {
        self.configs.get(interface)
    }

    /// Get all DHCP configurations
    pub fn get_all_configs(&self) -> Vec<&DhcpConfig> {
        self.configs.values().collect()
    }

    /// Add static lease
    pub fn add_static_lease(&mut self, interface: &str, lease: StaticLease) -> NetworkResult<()> {
        if let Some(config) = self.configs.get_mut(interface) {
            // Check for duplicate MAC or IP
            if config.static_leases.iter().any(|l| l.mac == lease.mac) {
                return Err(NetworkError::InvalidConfig("MAC address already has a static lease".to_string()));
            }
            if config.static_leases.iter().any(|l| l.ip == lease.ip) {
                return Err(NetworkError::InvalidConfig("IP address already assigned".to_string()));
            }

            config.static_leases.push(lease);
            let config_clone = config.clone();
            self.save_config(&config_clone)?;
            Ok(())
        } else {
            Err(NetworkError::InvalidConfig(format!("DHCP not configured for interface {}", interface)))
        }
    }

    /// Remove static lease
    pub fn remove_static_lease(&mut self, interface: &str, mac: &str) -> NetworkResult<()> {
        if let Some(config) = self.configs.get_mut(interface) {
            config.static_leases.retain(|lease| lease.mac != mac);
            let config_clone = config.clone();
            self.save_config(&config_clone)?;
            Ok(())
        } else {
            Err(NetworkError::InvalidConfig(format!("DHCP not configured for interface {}", interface)))
        }
    }

    /// Get active DHCP leases
    pub fn get_active_leases(&mut self) -> NetworkResult<&Vec<DhcpLease>> {
        self.load_active_leases()?;
        Ok(&self.active_leases)
    }

    /// Load active leases from system
    fn load_active_leases(&mut self) -> NetworkResult<()> {
        // Mock implementation - in real system would read from /var/dhcp.leases
        self.active_leases = vec![
            DhcpLease {
                mac: "aa:bb:cc:dd:ee:ff".to_string(),
                ip: "*************".parse().unwrap(),
                hostname: Some("laptop".to_string()),
                expires: 1234567890,
                client_id: None,
            },
            DhcpLease {
                mac: "11:22:33:44:55:66".to_string(),
                ip: "*************".parse().unwrap(),
                hostname: Some("phone".to_string()),
                expires: 1234567891,
                client_id: None,
            },
        ];
        
        Ok(())
    }

    /// Parse lease time string
    fn parse_lease_time(time_str: &str) -> LeaseTime {
        if time_str == "infinite" {
            return LeaseTime {
                value: 0,
                unit: LeaseTimeUnit::Infinite,
            };
        }

        let (value_str, unit) = if time_str.ends_with('m') {
            (&time_str[..time_str.len()-1], LeaseTimeUnit::Minutes)
        } else if time_str.ends_with('h') {
            (&time_str[..time_str.len()-1], LeaseTimeUnit::Hours)
        } else if time_str.ends_with('d') {
            (&time_str[..time_str.len()-1], LeaseTimeUnit::Days)
        } else {
            // Default to hours if no unit specified
            (time_str, LeaseTimeUnit::Hours)
        };

        let value = value_str.parse().unwrap_or(12);
        
        LeaseTime { value, unit }
    }

    /// Validate DHCP configuration
    pub fn validate_config(config: &DhcpConfig) -> NetworkResult<()> {
        // Validate start and limit
        if config.start == 0 {
            return Err(NetworkError::InvalidConfig("DHCP start address cannot be 0".to_string()));
        }
        
        if config.limit == 0 {
            return Err(NetworkError::InvalidConfig("DHCP limit cannot be 0".to_string()));
        }

        if config.start + config.limit > 254 {
            return Err(NetworkError::InvalidConfig("DHCP range exceeds available addresses".to_string()));
        }

        // Validate static leases
        for lease in &config.static_leases {
            if !Self::is_valid_mac(&lease.mac) {
                return Err(NetworkError::InvalidConfig(format!("Invalid MAC address: {}", lease.mac)));
            }
        }

        Ok(())
    }

    /// Check if MAC address format is valid
    fn is_valid_mac(mac: &str) -> bool {
        let parts: Vec<&str> = mac.split(':').collect();
        if parts.len() != 6 {
            return false;
        }

        for part in parts {
            if part.len() != 2 {
                return false;
            }
            if !part.chars().all(|c| c.is_ascii_hexdigit()) {
                return false;
            }
        }

        true
    }

    /// Restart DHCP service
    pub fn restart_service(&self) -> NetworkResult<()> {
        // In real implementation, would restart dnsmasq service
        log::info!("Restarting DHCP service");
        Ok(())
    }
}

impl Default for DhcpManager {
    fn default() -> Self {
        Self::new().expect("Failed to create DhcpManager")
    }
}
