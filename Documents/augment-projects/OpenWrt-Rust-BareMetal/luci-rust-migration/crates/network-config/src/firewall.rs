//! Firewall Configuration Management
//!
//! This module provides firewall configuration management for OpenWrt systems.

use crate::{NetworkError, NetworkResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr};

/// Firewall policy actions
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FirewallPolicy {
    Accept,
    Reject,
    Drop,
}

impl std::fmt::Display for FirewallPolicy {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            FirewallPolicy::Accept => write!(f, "ACCEPT"),
            FirewallPolicy::Reject => write!(f, "REJECT"),
            FirewallPolicy::Drop => write!(f, "DROP"),
        }
    }
}

/// Firewall rule targets
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum FirewallTarget {
    Accept,
    Reject,
    Drop,
    Mark,
    NoTrack,
}

impl std::fmt::Display for FirewallTarget {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            FirewallTarget::Accept => write!(f, "ACCEPT"),
            FirewallTarget::Reject => write!(f, "REJECT"),
            FirewallTarget::Drop => write!(f, "DROP"),
            FirewallTarget::Mark => write!(f, "MARK"),
            FirewallTarget::NoTrack => write!(f, "NOTRACK"),
        }
    }
}

/// Network protocols
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum Protocol {
    TCP,
    UDP,
    ICMP,
    All,
}

impl std::fmt::Display for Protocol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Protocol::TCP => write!(f, "tcp"),
            Protocol::UDP => write!(f, "udp"),
            Protocol::ICMP => write!(f, "icmp"),
            Protocol::All => write!(f, "all"),
        }
    }
}

/// Firewall zone configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FirewallZone {
    pub name: String,
    pub network: Vec<String>,
    pub input: FirewallPolicy,
    pub output: FirewallPolicy,
    pub forward: FirewallPolicy,
    pub masq: bool,
    pub mtu_fix: bool,
    pub log: bool,
    pub log_limit: Option<String>,
}

impl Default for FirewallZone {
    fn default() -> Self {
        Self {
            name: String::new(),
            network: Vec::new(),
            input: FirewallPolicy::Accept,
            output: FirewallPolicy::Accept,
            forward: FirewallPolicy::Reject,
            masq: false,
            mtu_fix: false,
            log: false,
            log_limit: None,
        }
    }
}

/// Firewall rule configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FirewallRule {
    pub name: String,
    pub src: Option<String>,
    pub dest: Option<String>,
    pub src_ip: Option<IpAddr>,
    pub dest_ip: Option<IpAddr>,
    pub src_port: Option<String>,
    pub dest_port: Option<String>,
    pub proto: Option<Protocol>,
    pub target: FirewallTarget,
    pub enabled: bool,
    pub family: Option<String>,
}

impl Default for FirewallRule {
    fn default() -> Self {
        Self {
            name: String::new(),
            src: None,
            dest: None,
            src_ip: None,
            dest_ip: None,
            src_port: None,
            dest_port: None,
            proto: None,
            target: FirewallTarget::Accept,
            enabled: true,
            family: None,
        }
    }
}

/// Port forwarding rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortForwardRule {
    pub name: String,
    pub src: String,
    pub dest: String,
    pub src_dport: String,
    pub dest_ip: Ipv4Addr,
    pub dest_port: Option<String>,
    pub proto: Protocol,
    pub enabled: bool,
}

/// Firewall forwarding rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ForwardingRule {
    pub src: String,
    pub dest: String,
}

/// Firewall manager
pub struct FirewallManager {
    zones: HashMap<String, FirewallZone>,
    rules: Vec<FirewallRule>,
    port_forwards: Vec<PortForwardRule>,
    forwardings: Vec<ForwardingRule>,
}

impl FirewallManager {
    /// Create a new firewall manager
    pub fn new() -> Self {
        let mut manager = Self {
            zones: HashMap::new(),
            rules: Vec::new(),
            port_forwards: Vec::new(),
            forwardings: Vec::new(),
        };
        
        manager.setup_default_zones();
        manager
    }

    /// Setup default firewall zones
    fn setup_default_zones(&mut self) {
        // LAN zone
        let lan_zone = FirewallZone {
            name: "lan".to_string(),
            network: vec!["lan".to_string()],
            input: FirewallPolicy::Accept,
            output: FirewallPolicy::Accept,
            forward: FirewallPolicy::Accept,
            masq: false,
            mtu_fix: false,
            log: false,
            log_limit: None,
        };
        self.zones.insert("lan".to_string(), lan_zone);

        // WAN zone
        let wan_zone = FirewallZone {
            name: "wan".to_string(),
            network: vec!["wan".to_string(), "wan6".to_string()],
            input: FirewallPolicy::Reject,
            output: FirewallPolicy::Accept,
            forward: FirewallPolicy::Reject,
            masq: true,
            mtu_fix: true,
            log: false,
            log_limit: None,
        };
        self.zones.insert("wan".to_string(), wan_zone);

        // Default forwarding from LAN to WAN
        self.forwardings.push(ForwardingRule {
            src: "lan".to_string(),
            dest: "wan".to_string(),
        });
    }

    /// Add a firewall zone
    pub fn add_zone(&mut self, zone: FirewallZone) -> NetworkResult<()> {
        if self.zones.contains_key(&zone.name) {
            return Err(NetworkError::InvalidConfig(format!("Zone {} already exists", zone.name)));
        }
        
        self.zones.insert(zone.name.clone(), zone);
        Ok(())
    }

    /// Remove a firewall zone
    pub fn remove_zone(&mut self, name: &str) -> NetworkResult<()> {
        if !self.zones.contains_key(name) {
            return Err(NetworkError::InvalidConfig(format!("Zone {} does not exist", name)));
        }
        
        self.zones.remove(name);
        
        // Remove related rules and forwardings
        self.rules.retain(|rule| rule.src.as_ref() != Some(&name.to_string()) && rule.dest.as_ref() != Some(&name.to_string()));
        self.forwardings.retain(|fwd| fwd.src != name && fwd.dest != name);
        
        Ok(())
    }

    /// Get a firewall zone
    pub fn get_zone(&self, name: &str) -> Option<&FirewallZone> {
        self.zones.get(name)
    }

    /// Get all firewall zones
    pub fn get_zones(&self) -> Vec<&FirewallZone> {
        self.zones.values().collect()
    }

    /// Update a firewall zone
    pub fn update_zone(&mut self, zone: FirewallZone) -> NetworkResult<()> {
        if !self.zones.contains_key(&zone.name) {
            return Err(NetworkError::InvalidConfig(format!("Zone {} does not exist", zone.name)));
        }
        
        self.zones.insert(zone.name.clone(), zone);
        Ok(())
    }

    /// Add a firewall rule
    pub fn add_rule(&mut self, rule: FirewallRule) -> NetworkResult<()> {
        // Validate rule
        if let Some(ref src) = rule.src {
            if !self.zones.contains_key(src) {
                return Err(NetworkError::InvalidConfig(format!("Source zone {} does not exist", src)));
            }
        }
        
        if let Some(ref dest) = rule.dest {
            if !self.zones.contains_key(dest) {
                return Err(NetworkError::InvalidConfig(format!("Destination zone {} does not exist", dest)));
            }
        }
        
        self.rules.push(rule);
        Ok(())
    }

    /// Remove a firewall rule
    pub fn remove_rule(&mut self, name: &str) -> NetworkResult<()> {
        let initial_len = self.rules.len();
        self.rules.retain(|rule| rule.name != name);
        
        if self.rules.len() == initial_len {
            return Err(NetworkError::InvalidConfig(format!("Rule {} does not exist", name)));
        }
        
        Ok(())
    }

    /// Get all firewall rules
    pub fn get_rules(&self) -> &Vec<FirewallRule> {
        &self.rules
    }

    /// Add a port forwarding rule
    pub fn add_port_forward(&mut self, forward: PortForwardRule) -> NetworkResult<()> {
        // Validate zones
        if !self.zones.contains_key(&forward.src) {
            return Err(NetworkError::InvalidConfig(format!("Source zone {} does not exist", forward.src)));
        }
        
        if !self.zones.contains_key(&forward.dest) {
            return Err(NetworkError::InvalidConfig(format!("Destination zone {} does not exist", forward.dest)));
        }
        
        self.port_forwards.push(forward);
        Ok(())
    }

    /// Remove a port forwarding rule
    pub fn remove_port_forward(&mut self, name: &str) -> NetworkResult<()> {
        let initial_len = self.port_forwards.len();
        self.port_forwards.retain(|fwd| fwd.name != name);
        
        if self.port_forwards.len() == initial_len {
            return Err(NetworkError::InvalidConfig(format!("Port forward {} does not exist", name)));
        }
        
        Ok(())
    }

    /// Get all port forwarding rules
    pub fn get_port_forwards(&self) -> &Vec<PortForwardRule> {
        &self.port_forwards
    }

    /// Add a forwarding rule
    pub fn add_forwarding(&mut self, forwarding: ForwardingRule) -> NetworkResult<()> {
        // Validate zones
        if !self.zones.contains_key(&forwarding.src) {
            return Err(NetworkError::InvalidConfig(format!("Source zone {} does not exist", forwarding.src)));
        }
        
        if !self.zones.contains_key(&forwarding.dest) {
            return Err(NetworkError::InvalidConfig(format!("Destination zone {} does not exist", forwarding.dest)));
        }
        
        self.forwardings.push(forwarding);
        Ok(())
    }

    /// Get all forwarding rules
    pub fn get_forwardings(&self) -> &Vec<ForwardingRule> {
        &self.forwardings
    }

    /// Apply firewall configuration
    pub fn apply_config(&self) -> NetworkResult<()> {
        // In real implementation, this would generate iptables rules and apply them
        log::info!("Applying firewall configuration");
        Ok(())
    }

    /// Restart firewall service
    pub fn restart_firewall(&self) -> NetworkResult<()> {
        // In real implementation, this would restart the firewall service
        log::info!("Restarting firewall service");
        Ok(())
    }
}

impl Default for FirewallManager {
    fn default() -> Self {
        Self::new()
    }
}
