//! Network Interface Management
//!
//! This module provides detailed network interface management functionality
//! including configuration, status monitoring, and control operations.

use crate::{NetworkError, NetworkResult, NetworkInterface, NetworkProtocol, InterfaceStatus};
use serde::{Deserialize, Serialize};
use std::net::{IpAddr, Ipv4Addr};
use std::collections::HashMap;

/// Interface configuration builder
pub struct InterfaceBuilder {
    interface: NetworkInterface,
}

impl InterfaceBuilder {
    /// Create a new interface builder
    pub fn new(name: &str) -> Self {
        Self {
            interface: NetworkInterface {
                name: name.to_string(),
                ..Default::default()
            },
        }
    }

    /// Set the protocol
    pub fn protocol(mut self, protocol: NetworkProtocol) -> Self {
        self.interface.protocol = protocol;
        self
    }

    /// Set static IP configuration
    pub fn static_ip(mut self, ip: Ipv4Addr, netmask: Ipv4Addr, gateway: Option<Ipv4Addr>) -> Self {
        self.interface.protocol = NetworkProtocol::Static;
        self.interface.ipaddr = Some(ip);
        self.interface.netmask = Some(netmask);
        self.interface.gateway = gateway;
        self
    }

    /// Set DHCP configuration
    pub fn dhcp(mut self) -> Self {
        self.interface.protocol = NetworkProtocol::Dhcp;
        self.interface.ipaddr = None;
        self.interface.netmask = None;
        self.interface.gateway = None;
        self
    }

    /// Set DNS servers
    pub fn dns(mut self, dns_servers: Vec<IpAddr>) -> Self {
        self.interface.dns = dns_servers;
        self
    }

    /// Set MTU
    pub fn mtu(mut self, mtu: u32) -> Self {
        self.interface.mtu = Some(mtu);
        self
    }

    /// Set MAC address
    pub fn mac(mut self, mac: &str) -> Self {
        self.interface.mac = Some(mac.to_string());
        self
    }

    /// Enable or disable the interface
    pub fn enabled(mut self, enabled: bool) -> Self {
        self.interface.enabled = enabled;
        self
    }

    /// Build the interface configuration
    pub fn build(self) -> NetworkInterface {
        self.interface
    }
}

/// Interface validation
pub struct InterfaceValidator;

impl InterfaceValidator {
    /// Validate interface configuration
    pub fn validate(interface: &NetworkInterface) -> NetworkResult<()> {
        // Validate interface name
        if interface.name.is_empty() {
            return Err(NetworkError::InvalidConfig("Interface name cannot be empty".to_string()));
        }

        // Validate protocol-specific configuration
        match interface.protocol {
            NetworkProtocol::Static => {
                if interface.ipaddr.is_none() {
                    return Err(NetworkError::InvalidConfig("Static interface requires IP address".to_string()));
                }
                if interface.netmask.is_none() {
                    return Err(NetworkError::InvalidConfig("Static interface requires netmask".to_string()));
                }
            },
            NetworkProtocol::Dhcp => {
                // DHCP interfaces don't require static configuration
            },
            NetworkProtocol::Pppoe => {
                // PPPoE validation would go here
            },
            _ => {
                // Other protocols
            }
        }

        // Validate MTU
        if let Some(mtu) = interface.mtu {
            if mtu < 68 || mtu > 9000 {
                return Err(NetworkError::InvalidConfig("MTU must be between 68 and 9000".to_string()));
            }
        }

        // Validate MAC address format
        if let Some(ref mac) = interface.mac {
            if !Self::is_valid_mac(mac) {
                return Err(NetworkError::InvalidConfig("Invalid MAC address format".to_string()));
            }
        }

        Ok(())
    }

    /// Check if MAC address format is valid
    fn is_valid_mac(mac: &str) -> bool {
        let parts: Vec<&str> = mac.split(':').collect();
        if parts.len() != 6 {
            return false;
        }

        for part in parts {
            if part.len() != 2 {
                return false;
            }
            if !part.chars().all(|c| c.is_ascii_hexdigit()) {
                return false;
            }
        }

        true
    }
}

/// Interface statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InterfaceStats {
    pub rx_bytes: u64,
    pub tx_bytes: u64,
    pub rx_packets: u64,
    pub tx_packets: u64,
    pub rx_errors: u64,
    pub tx_errors: u64,
    pub rx_dropped: u64,
    pub tx_dropped: u64,
}

/// Interface monitor for real-time statistics
pub struct InterfaceMonitor {
    interfaces: HashMap<String, InterfaceStats>,
}

impl InterfaceMonitor {
    /// Create a new interface monitor
    pub fn new() -> Self {
        Self {
            interfaces: HashMap::new(),
        }
    }

    /// Update statistics for an interface
    pub fn update_stats(&mut self, name: &str) -> NetworkResult<()> {
        // In real implementation, this would read from /proc/net/dev
        let stats = self.read_interface_stats(name)?;
        self.interfaces.insert(name.to_string(), stats);
        Ok(())
    }

    /// Get statistics for an interface
    pub fn get_stats(&self, name: &str) -> Option<&InterfaceStats> {
        self.interfaces.get(name)
    }

    /// Get all interface statistics
    pub fn get_all_stats(&self) -> &HashMap<String, InterfaceStats> {
        &self.interfaces
    }

    /// Read interface statistics from system (mock implementation)
    fn read_interface_stats(&self, name: &str) -> NetworkResult<InterfaceStats> {
        // Mock implementation - in real system would read from /proc/net/dev
        match name {
            "lo" => Ok(InterfaceStats {
                rx_bytes: 1024,
                tx_bytes: 1024,
                rx_packets: 10,
                tx_packets: 10,
                rx_errors: 0,
                tx_errors: 0,
                rx_dropped: 0,
                tx_dropped: 0,
            }),
            "eth0" => Ok(InterfaceStats {
                rx_bytes: 1048576,
                tx_bytes: 524288,
                rx_packets: 1000,
                tx_packets: 800,
                rx_errors: 0,
                tx_errors: 0,
                rx_dropped: 0,
                tx_dropped: 0,
            }),
            _ => Err(NetworkError::InterfaceNotFound(name.to_string())),
        }
    }
}

impl Default for InterfaceMonitor {
    fn default() -> Self {
        Self::new()
    }
}

/// Common interface configurations
pub struct CommonInterfaces;

impl CommonInterfaces {
    /// Create a LAN interface configuration
    pub fn lan() -> NetworkInterface {
        InterfaceBuilder::new("lan")
            .static_ip(
                "***********".parse().unwrap(),
                "*************".parse().unwrap(),
                None
            )
            .dns(vec!["*******".parse().unwrap(), "*******".parse().unwrap()])
            .enabled(true)
            .build()
    }

    /// Create a WAN interface configuration
    pub fn wan() -> NetworkInterface {
        InterfaceBuilder::new("wan")
            .dhcp()
            .enabled(true)
            .build()
    }

    /// Create a loopback interface configuration
    pub fn loopback() -> NetworkInterface {
        InterfaceBuilder::new("lo")
            .static_ip(
                "127.0.0.1".parse().unwrap(),
                "*********".parse().unwrap(),
                None
            )
            .enabled(true)
            .build()
    }
}

/// Interface control operations
pub struct InterfaceController;

impl InterfaceController {
    /// Bring interface up
    pub fn bring_up(name: &str) -> NetworkResult<()> {
        // In real implementation, would execute: ip link set <name> up
        log::info!("Bringing interface {} up", name);
        Ok(())
    }

    /// Bring interface down
    pub fn bring_down(name: &str) -> NetworkResult<()> {
        // In real implementation, would execute: ip link set <name> down
        log::info!("Bringing interface {} down", name);
        Ok(())
    }

    /// Restart interface
    pub fn restart(name: &str) -> NetworkResult<()> {
        Self::bring_down(name)?;
        // In real implementation, would add a small delay
        Self::bring_up(name)?;
        Ok(())
    }

    /// Flush interface configuration
    pub fn flush(name: &str) -> NetworkResult<()> {
        // In real implementation, would execute: ip addr flush dev <name>
        log::info!("Flushing interface {} configuration", name);
        Ok(())
    }
}
