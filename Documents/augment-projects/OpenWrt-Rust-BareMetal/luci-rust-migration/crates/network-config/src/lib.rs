//! Network Configuration Management
//!
//! This crate provides network configuration management for OpenWrt systems,
//! including interface management, WiFi configuration, DHCP settings, and firewall rules.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr};
use thiserror::Error;
use luci_shared_types::*;
use luci_uci_bindings::{ConfigManager, UciError};

pub mod interface;
pub mod wifi;
pub mod dhcp;
pub mod firewall;
pub mod routing;

pub use interface::*;
pub use wifi::*;
pub use dhcp::*;
pub use firewall::*;
pub use routing::*;
pub use firewall::*;
pub use routing::*;

/// Network configuration errors
#[derive(Error, Debug, Clone)]
pub enum NetworkError {
    #[error("UCI error: {0}")]
    UciError(#[from] UciError),
    #[error("Interface not found: {0}")]
    InterfaceNotFound(String),
    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),
    #[error("Network operation failed: {0}")]
    OperationFailed(String),
    #[error("Permission denied")]
    PermissionDenied,
    #[error("Device busy: {0}")]
    DeviceBusy(String),
}

pub type NetworkResult<T> = Result<T, NetworkError>;

/// Network interface status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum InterfaceStatus {
    Up,
    Down,
    Unknown,
}

/// Network protocol types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NetworkProtocol {
    Static,
    Dhcp,
    Pppoe,
    None,
    #[serde(rename = "6in4")]
    SixInFour,
    #[serde(rename = "6to4")]
    SixToFour,
    #[serde(rename = "6rd")]
    SixRd,
}

impl std::fmt::Display for NetworkProtocol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            NetworkProtocol::Static => write!(f, "static"),
            NetworkProtocol::Dhcp => write!(f, "dhcp"),
            NetworkProtocol::Pppoe => write!(f, "pppoe"),
            NetworkProtocol::None => write!(f, "none"),
            NetworkProtocol::SixInFour => write!(f, "6in4"),
            NetworkProtocol::SixToFour => write!(f, "6to4"),
            NetworkProtocol::SixRd => write!(f, "6rd"),
        }
    }
}

/// Network interface configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkInterface {
    pub name: String,
    pub protocol: NetworkProtocol,
    pub enabled: bool,
    pub ipaddr: Option<Ipv4Addr>,
    pub netmask: Option<Ipv4Addr>,
    pub gateway: Option<Ipv4Addr>,
    pub dns: Vec<IpAddr>,
    pub mtu: Option<u32>,
    pub mac: Option<String>,
    pub status: InterfaceStatus,
    pub rx_bytes: u64,
    pub tx_bytes: u64,
    pub rx_packets: u64,
    pub tx_packets: u64,
}

impl Default for NetworkInterface {
    fn default() -> Self {
        Self {
            name: String::new(),
            protocol: NetworkProtocol::Dhcp,
            enabled: true,
            ipaddr: None,
            netmask: None,
            gateway: None,
            dns: Vec::new(),
            mtu: None,
            mac: None,
            status: InterfaceStatus::Unknown,
            rx_bytes: 0,
            tx_bytes: 0,
            rx_packets: 0,
            tx_packets: 0,
        }
    }
}

/// Network configuration manager
pub struct NetworkManager {
    config_manager: ConfigManager,
    interfaces: HashMap<String, NetworkInterface>,
}

impl NetworkManager {
    /// Create a new network manager
    pub fn new() -> NetworkResult<Self> {
        Ok(Self {
            config_manager: ConfigManager::new()?,
            interfaces: HashMap::new(),
        })
    }

    /// Load all network interfaces
    pub fn load_interfaces(&mut self) -> NetworkResult<()> {
        let interface_names = self.config_manager.list_network_interfaces()?;

        for name in interface_names {
            let interface = self.load_interface(&name)?;
            self.interfaces.insert(name, interface);
        }

        Ok(())
    }

    /// Load a specific network interface
    pub fn load_interface(&mut self, name: &str) -> NetworkResult<NetworkInterface> {
        let uci_interface = self.config_manager.get_network_interface(name)?;

        let protocol = match uci_interface.proto.as_str() {
            "static" => NetworkProtocol::Static,
            "dhcp" => NetworkProtocol::Dhcp,
            "pppoe" => NetworkProtocol::Pppoe,
            "none" => NetworkProtocol::None,
            "6in4" => NetworkProtocol::SixInFour,
            "6to4" => NetworkProtocol::SixToFour,
            "6rd" => NetworkProtocol::SixRd,
            _ => NetworkProtocol::Dhcp,
        };

        let ipaddr = uci_interface.ipaddr
            .and_then(|addr| addr.parse().ok());

        let netmask = uci_interface.netmask
            .and_then(|mask| mask.parse().ok());

        let gateway = uci_interface.gateway
            .and_then(|gw| gw.parse().ok());

        let dns = uci_interface.dns.iter()
            .filter_map(|addr| addr.parse().ok())
            .collect();

        // Get interface statistics (mock implementation)
        let (status, rx_bytes, tx_bytes, rx_packets, tx_packets) = self.get_interface_stats(name);

        Ok(NetworkInterface {
            name: name.to_string(),
            protocol,
            enabled: uci_interface.enabled,
            ipaddr,
            netmask,
            gateway,
            dns,
            mtu: uci_interface.mtu,
            mac: uci_interface.mac,
            status,
            rx_bytes,
            tx_bytes,
            rx_packets,
            tx_packets,
        })
    }

    /// Save network interface configuration
    pub fn save_interface(&mut self, interface: &NetworkInterface) -> NetworkResult<()> {
        let uci_interface = luci_uci_bindings::NetworkInterface {
            name: interface.name.clone(),
            proto: interface.protocol.to_string(),
            ipaddr: interface.ipaddr.map(|addr| addr.to_string()),
            netmask: interface.netmask.map(|mask| mask.to_string()),
            gateway: interface.gateway.map(|gw| gw.to_string()),
            dns: interface.dns.iter().map(|addr| addr.to_string()).collect(),
            enabled: interface.enabled,
            mtu: interface.mtu,
            mac: interface.mac.clone(),
        };

        self.config_manager.set_network_interface(&uci_interface)?;
        self.interfaces.insert(interface.name.clone(), interface.clone());

        Ok(())
    }

    /// Get all network interfaces
    pub fn get_interfaces(&self) -> Vec<&NetworkInterface> {
        self.interfaces.values().collect()
    }

    /// Get a specific network interface
    pub fn get_interface(&self, name: &str) -> Option<&NetworkInterface> {
        self.interfaces.get(name)
    }

    /// Enable a network interface
    pub fn enable_interface(&mut self, name: &str) -> NetworkResult<()> {
        if let Some(interface) = self.interfaces.get_mut(name) {
            interface.enabled = true;
            let interface_clone = interface.clone();
            self.save_interface(&interface_clone)?;
            // In real implementation, would call system commands to bring interface up
            Ok(())
        } else {
            Err(NetworkError::InterfaceNotFound(name.to_string()))
        }
    }

    /// Disable a network interface
    pub fn disable_interface(&mut self, name: &str) -> NetworkResult<()> {
        if let Some(interface) = self.interfaces.get_mut(name) {
            interface.enabled = false;
            let interface_clone = interface.clone();
            self.save_interface(&interface_clone)?;
            // In real implementation, would call system commands to bring interface down
            Ok(())
        } else {
            Err(NetworkError::InterfaceNotFound(name.to_string()))
        }
    }

    /// Restart a network interface
    pub fn restart_interface(&mut self, name: &str) -> NetworkResult<()> {
        self.disable_interface(name)?;
        // In real implementation, would add a delay here
        self.enable_interface(name)?;
        Ok(())
    }

    /// Get interface statistics (mock implementation)
    fn get_interface_stats(&self, name: &str) -> (InterfaceStatus, u64, u64, u64, u64) {
        // In real implementation, this would read from /proc/net/dev or use netlink
        match name {
            "lo" => (InterfaceStatus::Up, 1024, 1024, 10, 10),
            "eth0" => (InterfaceStatus::Up, 1048576, 524288, 1000, 800),
            "wlan0" => (InterfaceStatus::Up, 2097152, 1048576, 2000, 1500),
            _ => (InterfaceStatus::Down, 0, 0, 0, 0),
        }
    }

    /// Commit all network configuration changes
    pub fn commit_changes(&mut self) -> NetworkResult<()> {
        self.config_manager.commit_all()?;
        Ok(())
    }

    /// Scan for available network interfaces
    pub fn scan_interfaces(&mut self) -> NetworkResult<Vec<String>> {
        // In real implementation, this would scan system interfaces
        Ok(vec![
            "lo".to_string(),
            "eth0".to_string(),
            "eth1".to_string(),
            "wlan0".to_string(),
        ])
    }
}

impl Default for NetworkManager {
    fn default() -> Self {
        Self::new().expect("Failed to create NetworkManager")
    }
}
