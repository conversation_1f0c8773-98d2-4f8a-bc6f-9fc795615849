//! Routing Configuration Management
//!
//! This module provides routing table management for OpenWrt systems.

use crate::{NetworkError, NetworkResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};

/// Route types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum RouteType {
    Unicast,
    Local,
    Broadcast,
    Multicast,
    Unreachable,
    Prohibit,
    Blackhole,
    Nat,
}

impl std::fmt::Display for RouteType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RouteType::Unicast => write!(f, "unicast"),
            RouteType::Local => write!(f, "local"),
            RouteType::Broadcast => write!(f, "broadcast"),
            RouteType::Multicast => write!(f, "multicast"),
            RouteType::Unreachable => write!(f, "unreachable"),
            RouteType::Prohibit => write!(f, "prohibit"),
            RouteType::Blackhole => write!(f, "blackhole"),
            RouteType::Nat => write!(f, "nat"),
        }
    }
}

/// Route scope
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum RouteScope {
    Universe,
    Site,
    Link,
    Host,
    Nowhere,
}

impl std::fmt::Display for RouteScope {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RouteScope::Universe => write!(f, "global"),
            RouteScope::Site => write!(f, "site"),
            RouteScope::Link => write!(f, "link"),
            RouteScope::Host => write!(f, "host"),
            RouteScope::Nowhere => write!(f, "nowhere"),
        }
    }
}

/// Route protocol
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum RouteProtocol {
    Unspec,
    Redirect,
    Kernel,
    Boot,
    Static,
    Gated,
    Ra,
    Mrt,
    Zebra,
    Bird,
    Dnrouted,
    Xorp,
    Ntk,
    Dhcp,
}

impl std::fmt::Display for RouteProtocol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RouteProtocol::Unspec => write!(f, "unspec"),
            RouteProtocol::Redirect => write!(f, "redirect"),
            RouteProtocol::Kernel => write!(f, "kernel"),
            RouteProtocol::Boot => write!(f, "boot"),
            RouteProtocol::Static => write!(f, "static"),
            RouteProtocol::Gated => write!(f, "gated"),
            RouteProtocol::Ra => write!(f, "ra"),
            RouteProtocol::Mrt => write!(f, "mrt"),
            RouteProtocol::Zebra => write!(f, "zebra"),
            RouteProtocol::Bird => write!(f, "bird"),
            RouteProtocol::Dnrouted => write!(f, "dnrouted"),
            RouteProtocol::Xorp => write!(f, "xorp"),
            RouteProtocol::Ntk => write!(f, "ntk"),
            RouteProtocol::Dhcp => write!(f, "dhcp"),
        }
    }
}

/// Network route entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Route {
    pub destination: IpAddr,
    pub prefix_len: u8,
    pub gateway: Option<IpAddr>,
    pub interface: Option<String>,
    pub metric: Option<u32>,
    pub route_type: RouteType,
    pub scope: RouteScope,
    pub protocol: RouteProtocol,
    pub table: Option<u32>,
    pub source: Option<IpAddr>,
}

impl Default for Route {
    fn default() -> Self {
        Self {
            destination: IpAddr::V4(Ipv4Addr::new(0, 0, 0, 0)),
            prefix_len: 0,
            gateway: None,
            interface: None,
            metric: None,
            route_type: RouteType::Unicast,
            scope: RouteScope::Universe,
            protocol: RouteProtocol::Static,
            table: None,
            source: None,
        }
    }
}

/// Routing table
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoutingTable {
    pub id: u32,
    pub name: String,
    pub routes: Vec<Route>,
}

/// Routing rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoutingRule {
    pub priority: u32,
    pub from: Option<IpAddr>,
    pub from_prefix_len: Option<u8>,
    pub to: Option<IpAddr>,
    pub to_prefix_len: Option<u8>,
    pub iif: Option<String>,
    pub oif: Option<String>,
    pub table: u32,
    pub action: String,
}

/// Routing manager
pub struct RoutingManager {
    routes: Vec<Route>,
    tables: HashMap<u32, RoutingTable>,
    rules: Vec<RoutingRule>,
}

impl RoutingManager {
    /// Create a new routing manager
    pub fn new() -> Self {
        let mut manager = Self {
            routes: Vec::new(),
            tables: HashMap::new(),
            rules: Vec::new(),
        };
        
        manager.setup_default_tables();
        manager
    }

    /// Setup default routing tables
    fn setup_default_tables(&mut self) {
        // Main table
        let main_table = RoutingTable {
            id: 254,
            name: "main".to_string(),
            routes: Vec::new(),
        };
        self.tables.insert(254, main_table);

        // Local table
        let local_table = RoutingTable {
            id: 255,
            name: "local".to_string(),
            routes: Vec::new(),
        };
        self.tables.insert(255, local_table);
    }

    /// Load routing table from system
    pub fn load_routes(&mut self) -> NetworkResult<()> {
        // Mock implementation - in real system would parse `ip route show`
        self.routes = vec![
            Route {
                destination: IpAddr::V4(Ipv4Addr::new(0, 0, 0, 0)),
                prefix_len: 0,
                gateway: Some(IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1))),
                interface: Some("eth0".to_string()),
                metric: Some(100),
                route_type: RouteType::Unicast,
                scope: RouteScope::Universe,
                protocol: RouteProtocol::Dhcp,
                table: Some(254),
                source: None,
            },
            Route {
                destination: IpAddr::V4(Ipv4Addr::new(192, 168, 1, 0)),
                prefix_len: 24,
                gateway: None,
                interface: Some("eth0".to_string()),
                metric: Some(100),
                route_type: RouteType::Unicast,
                scope: RouteScope::Link,
                protocol: RouteProtocol::Kernel,
                table: Some(254),
                source: None,
            },
        ];
        
        Ok(())
    }

    /// Add a route
    pub fn add_route(&mut self, route: Route) -> NetworkResult<()> {
        // Validate route
        if route.prefix_len > 128 {
            return Err(NetworkError::InvalidConfig("Invalid prefix length".to_string()));
        }

        match route.destination {
            IpAddr::V4(_) => {
                if route.prefix_len > 32 {
                    return Err(NetworkError::InvalidConfig("IPv4 prefix length cannot exceed 32".to_string()));
                }
            },
            IpAddr::V6(_) => {
                if route.prefix_len > 128 {
                    return Err(NetworkError::InvalidConfig("IPv6 prefix length cannot exceed 128".to_string()));
                }
            },
        }

        self.routes.push(route);
        Ok(())
    }

    /// Remove a route
    pub fn remove_route(&mut self, destination: IpAddr, prefix_len: u8) -> NetworkResult<()> {
        let initial_len = self.routes.len();
        self.routes.retain(|route| {
            !(route.destination == destination && route.prefix_len == prefix_len)
        });
        
        if self.routes.len() == initial_len {
            return Err(NetworkError::InvalidConfig("Route not found".to_string()));
        }
        
        Ok(())
    }

    /// Get all routes
    pub fn get_routes(&self) -> &Vec<Route> {
        &self.routes
    }

    /// Get routes for a specific interface
    pub fn get_routes_for_interface(&self, interface: &str) -> Vec<&Route> {
        self.routes.iter()
            .filter(|route| route.interface.as_ref() == Some(&interface.to_string()))
            .collect()
    }

    /// Get default route
    pub fn get_default_route(&self) -> Option<&Route> {
        self.routes.iter()
            .find(|route| {
                match route.destination {
                    IpAddr::V4(addr) => addr.is_unspecified() && route.prefix_len == 0,
                    IpAddr::V6(addr) => addr.is_unspecified() && route.prefix_len == 0,
                }
            })
    }

    /// Set default route
    pub fn set_default_route(&mut self, gateway: IpAddr, interface: Option<String>) -> NetworkResult<()> {
        // Remove existing default route
        self.routes.retain(|route| {
            !(match route.destination {
                IpAddr::V4(addr) => addr.is_unspecified() && route.prefix_len == 0,
                IpAddr::V6(addr) => addr.is_unspecified() && route.prefix_len == 0,
            })
        });

        // Add new default route
        let default_route = Route {
            destination: match gateway {
                IpAddr::V4(_) => IpAddr::V4(Ipv4Addr::new(0, 0, 0, 0)),
                IpAddr::V6(_) => IpAddr::V6(Ipv6Addr::new(0, 0, 0, 0, 0, 0, 0, 0)),
            },
            prefix_len: 0,
            gateway: Some(gateway),
            interface,
            metric: Some(100),
            route_type: RouteType::Unicast,
            scope: RouteScope::Universe,
            protocol: RouteProtocol::Static,
            table: Some(254),
            source: None,
        };

        self.add_route(default_route)?;
        Ok(())
    }

    /// Add routing table
    pub fn add_table(&mut self, table: RoutingTable) -> NetworkResult<()> {
        if self.tables.contains_key(&table.id) {
            return Err(NetworkError::InvalidConfig(format!("Table {} already exists", table.id)));
        }
        
        self.tables.insert(table.id, table);
        Ok(())
    }

    /// Get routing table
    pub fn get_table(&self, id: u32) -> Option<&RoutingTable> {
        self.tables.get(&id)
    }

    /// Get all routing tables
    pub fn get_tables(&self) -> Vec<&RoutingTable> {
        self.tables.values().collect()
    }

    /// Add routing rule
    pub fn add_rule(&mut self, rule: RoutingRule) -> NetworkResult<()> {
        // Validate table exists
        if !self.tables.contains_key(&rule.table) {
            return Err(NetworkError::InvalidConfig(format!("Table {} does not exist", rule.table)));
        }
        
        self.rules.push(rule);
        Ok(())
    }

    /// Get all routing rules
    pub fn get_rules(&self) -> &Vec<RoutingRule> {
        &self.rules
    }

    /// Apply routing configuration
    pub fn apply_config(&self) -> NetworkResult<()> {
        // In real implementation, this would apply routes using netlink or ip commands
        log::info!("Applying routing configuration");
        Ok(())
    }

    /// Flush routing table
    pub fn flush_table(&mut self, table_id: u32) -> NetworkResult<()> {
        if let Some(table) = self.tables.get_mut(&table_id) {
            table.routes.clear();
            Ok(())
        } else {
            Err(NetworkError::InvalidConfig(format!("Table {} does not exist", table_id)))
        }
    }
}

impl Default for RoutingManager {
    fn default() -> Self {
        Self::new()
    }
}
