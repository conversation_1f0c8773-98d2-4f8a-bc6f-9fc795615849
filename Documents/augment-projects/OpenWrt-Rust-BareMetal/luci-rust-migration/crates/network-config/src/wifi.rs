//! WiFi Configuration Management
//!
//! This module provides WiFi device and interface configuration management
//! for OpenWrt systems.

use crate::{NetworkError, NetworkResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use luci_uci_bindings::ConfigManager;

/// WiFi hardware modes
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum WifiHwMode {
    #[serde(rename = "11a")]
    A,
    #[serde(rename = "11b")]
    B,
    #[serde(rename = "11g")]
    G,
    #[serde(rename = "11n")]
    N,
    #[serde(rename = "11ac")]
    AC,
    #[serde(rename = "11ax")]
    AX,
}

impl std::fmt::Display for WifiHwMode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            WifiHwMode::A => write!(f, "11a"),
            WifiHwMode::B => write!(f, "11b"),
            WifiHwMode::G => write!(f, "11g"),
            WifiHwMode::N => write!(f, "11n"),
            WifiHwMode::AC => write!(f, "11ac"),
            WifiHwMode::AX => write!(f, "11ax"),
        }
    }
}

/// WiFi HT modes
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum WifiHtMode {
    #[serde(rename = "HT20")]
    HT20,
    #[serde(rename = "HT40")]
    HT40,
    #[serde(rename = "VHT20")]
    VHT20,
    #[serde(rename = "VHT40")]
    VHT40,
    #[serde(rename = "VHT80")]
    VHT80,
    #[serde(rename = "VHT160")]
    VHT160,
}

impl std::fmt::Display for WifiHtMode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            WifiHtMode::HT20 => write!(f, "HT20"),
            WifiHtMode::HT40 => write!(f, "HT40"),
            WifiHtMode::VHT20 => write!(f, "VHT20"),
            WifiHtMode::VHT40 => write!(f, "VHT40"),
            WifiHtMode::VHT80 => write!(f, "VHT80"),
            WifiHtMode::VHT160 => write!(f, "VHT160"),
        }
    }
}

/// WiFi interface modes
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum WifiMode {
    #[serde(rename = "ap")]
    AccessPoint,
    #[serde(rename = "sta")]
    Station,
    #[serde(rename = "adhoc")]
    AdHoc,
    #[serde(rename = "monitor")]
    Monitor,
    #[serde(rename = "mesh")]
    Mesh,
}

impl std::fmt::Display for WifiMode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            WifiMode::AccessPoint => write!(f, "ap"),
            WifiMode::Station => write!(f, "sta"),
            WifiMode::AdHoc => write!(f, "adhoc"),
            WifiMode::Monitor => write!(f, "monitor"),
            WifiMode::Mesh => write!(f, "mesh"),
        }
    }
}

/// WiFi encryption types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum WifiEncryption {
    None,
    WEP,
    PSK,
    PSK2,
    #[serde(rename = "psk-mixed")]
    PSKMixed,
    WPA,
    WPA2,
    #[serde(rename = "wpa-mixed")]
    WPAMixed,
    WPA3,
    #[serde(rename = "wpa2/wpa3-mixed")]
    WPA2WPA3Mixed,
}

impl std::fmt::Display for WifiEncryption {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            WifiEncryption::None => write!(f, "none"),
            WifiEncryption::WEP => write!(f, "wep"),
            WifiEncryption::PSK => write!(f, "psk"),
            WifiEncryption::PSK2 => write!(f, "psk2"),
            WifiEncryption::PSKMixed => write!(f, "psk-mixed"),
            WifiEncryption::WPA => write!(f, "wpa"),
            WifiEncryption::WPA2 => write!(f, "wpa2"),
            WifiEncryption::WPAMixed => write!(f, "wpa-mixed"),
            WifiEncryption::WPA3 => write!(f, "wpa3"),
            WifiEncryption::WPA2WPA3Mixed => write!(f, "wpa2/wpa3-mixed"),
        }
    }
}

/// WiFi device configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WifiDevice {
    pub name: String,
    pub device_type: String,
    pub channel: String,
    pub hwmode: WifiHwMode,
    pub htmode: Option<WifiHtMode>,
    pub txpower: Option<u32>,
    pub country: Option<String>,
    pub disabled: bool,
    pub path: Option<String>,
}

/// WiFi interface configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WifiInterface {
    pub device: String,
    pub network: String,
    pub mode: WifiMode,
    pub ssid: String,
    pub encryption: WifiEncryption,
    pub key: Option<String>,
    pub hidden: bool,
    pub disabled: bool,
    pub isolate: bool,
    pub max_clients: Option<u32>,
}

/// WiFi scan result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WifiScanResult {
    pub ssid: String,
    pub bssid: String,
    pub channel: u32,
    pub signal: i32,
    pub encryption: WifiEncryption,
    pub frequency: u32,
}

/// WiFi configuration manager
pub struct WifiManager {
    config_manager: ConfigManager,
    devices: HashMap<String, WifiDevice>,
    interfaces: HashMap<String, WifiInterface>,
}

impl WifiManager {
    /// Create a new WiFi manager
    pub fn new() -> NetworkResult<Self> {
        Ok(Self {
            config_manager: ConfigManager::new()?,
            devices: HashMap::new(),
            interfaces: HashMap::new(),
        })
    }

    /// Load all WiFi devices
    pub fn load_devices(&mut self) -> NetworkResult<()> {
        let device_names = self.config_manager.list_wifi_devices()?;
        
        for name in device_names {
            let device = self.load_device(&name)?;
            self.devices.insert(name, device);
        }
        
        Ok(())
    }

    /// Load a specific WiFi device
    pub fn load_device(&mut self, name: &str) -> NetworkResult<WifiDevice> {
        let uci_device = self.config_manager.get_wifi_device(name)?;
        
        let hwmode = match uci_device.hwmode.as_str() {
            "11a" => WifiHwMode::A,
            "11b" => WifiHwMode::B,
            "11g" => WifiHwMode::G,
            "11n" => WifiHwMode::N,
            "11ac" => WifiHwMode::AC,
            "11ax" => WifiHwMode::AX,
            _ => WifiHwMode::G,
        };

        let htmode = uci_device.htmode.as_ref().and_then(|ht| {
            match ht.as_str() {
                "HT20" => Some(WifiHtMode::HT20),
                "HT40" => Some(WifiHtMode::HT40),
                "VHT20" => Some(WifiHtMode::VHT20),
                "VHT40" => Some(WifiHtMode::VHT40),
                "VHT80" => Some(WifiHtMode::VHT80),
                "VHT160" => Some(WifiHtMode::VHT160),
                _ => None,
            }
        });

        Ok(WifiDevice {
            name: name.to_string(),
            device_type: uci_device.device_type,
            channel: uci_device.channel,
            hwmode,
            htmode,
            txpower: uci_device.txpower,
            country: uci_device.country,
            disabled: uci_device.disabled,
            path: None, // TODO: Get from UCI
        })
    }

    /// Save WiFi device configuration
    pub fn save_device(&mut self, device: &WifiDevice) -> NetworkResult<()> {
        let uci_device = luci_uci_bindings::WifiDevice {
            name: device.name.clone(),
            device_type: device.device_type.clone(),
            channel: device.channel.clone(),
            hwmode: device.hwmode.to_string(),
            htmode: device.htmode.as_ref().map(|ht| ht.to_string()),
            txpower: device.txpower,
            country: device.country.clone(),
            disabled: device.disabled,
        };

        self.config_manager.set_wifi_device(&uci_device)?;
        self.devices.insert(device.name.clone(), device.clone());
        
        Ok(())
    }

    /// Get all WiFi devices
    pub fn get_devices(&self) -> Vec<&WifiDevice> {
        self.devices.values().collect()
    }

    /// Get a specific WiFi device
    pub fn get_device(&self, name: &str) -> Option<&WifiDevice> {
        self.devices.get(name)
    }

    /// Scan for available WiFi networks
    pub fn scan_networks(&self, device: &str) -> NetworkResult<Vec<WifiScanResult>> {
        // Mock implementation - in real system would use iwlist or iw scan
        Ok(vec![
            WifiScanResult {
                ssid: "OpenWrt".to_string(),
                bssid: "00:11:22:33:44:55".to_string(),
                channel: 6,
                signal: -45,
                encryption: WifiEncryption::PSK2,
                frequency: 2437,
            },
            WifiScanResult {
                ssid: "TestNetwork".to_string(),
                bssid: "AA:BB:CC:DD:EE:FF".to_string(),
                channel: 11,
                signal: -60,
                encryption: WifiEncryption::None,
                frequency: 2462,
            },
        ])
    }

    /// Enable WiFi device
    pub fn enable_device(&mut self, name: &str) -> NetworkResult<()> {
        if let Some(device) = self.devices.get_mut(name) {
            device.disabled = false;
            let device_clone = device.clone();
            self.save_device(&device_clone)?;
            Ok(())
        } else {
            Err(NetworkError::InterfaceNotFound(name.to_string()))
        }
    }

    /// Disable WiFi device
    pub fn disable_device(&mut self, name: &str) -> NetworkResult<()> {
        if let Some(device) = self.devices.get_mut(name) {
            device.disabled = true;
            let device_clone = device.clone();
            self.save_device(&device_clone)?;
            Ok(())
        } else {
            Err(NetworkError::InterfaceNotFound(name.to_string()))
        }
    }

    /// Get available channels for a device
    pub fn get_available_channels(&self, device: &str) -> NetworkResult<Vec<u32>> {
        // Mock implementation - in real system would query device capabilities
        let device_info = self.get_device(device)
            .ok_or_else(|| NetworkError::InterfaceNotFound(device.to_string()))?;

        match device_info.hwmode {
            WifiHwMode::A => Ok(vec![36, 40, 44, 48, 149, 153, 157, 161]),
            WifiHwMode::B | WifiHwMode::G | WifiHwMode::N => Ok(vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]),
            WifiHwMode::AC | WifiHwMode::AX => Ok(vec![36, 40, 44, 48, 149, 153, 157, 161]),
        }
    }
}

impl Default for WifiManager {
    fn default() -> Self {
        Self::new().expect("Failed to create WifiManager")
    }
}
