//! OpenWrt Package Manager (opkg) Integration
//!
//! This crate provides Rust bindings for the OpenWrt opkg package manager,
//! enabling direct command execution and output parsing.

use std::collections::HashMap;
use std::process::Command;
use std::str::FromStr;
use serde::{Deserialize, Serialize};

/// Result type for opkg operations
pub type OpkgResult<T> = Result<T, OpkgError>;

/// Errors that can occur during opkg operations
#[derive(Debug, thiserror::Error)]
pub enum OpkgError {
    #[error("Command execution failed: {0}")]
    CommandFailed(String),

    #[error("Package not found: {0}")]
    PackageNotFound(String),

    #[error("Parse error: {0}")]
    ParseError(String),

    #[error("Installation failed: {0}")]
    InstallationFailed(String),

    #[error("Removal failed: {0}")]
    RemovalFailed(String),

    #[error("Repository error: {0}")]
    RepositoryError(String),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}

/// Package status as reported by opkg
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PackageStatus {
    /// Package is installed
    Installed,
    /// Package is not installed but available
    NotInstalled,
    /// Package is installed but can be upgraded
    Upgradable,
    /// Package is held (won't be upgraded)
    Hold,
    /// Package is unpacked but not configured
    Unpacked,
}

impl FromStr for PackageStatus {
    type Err = OpkgError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.trim().to_lowercase().as_str() {
            "install user installed" | "installed" => Ok(PackageStatus::Installed),
            "not-installed" | "available" => Ok(PackageStatus::NotInstalled),
            "upgradable" => Ok(PackageStatus::Upgradable),
            "hold" => Ok(PackageStatus::Hold),
            "unpacked" => Ok(PackageStatus::Unpacked),
            _ => Err(OpkgError::ParseError(format!("Unknown package status: {}", s))),
        }
    }
}

/// Package priority levels
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PackagePriority {
    Essential,
    Required,
    Important,
    Standard,
    Optional,
    Extra,
}

impl FromStr for PackagePriority {
    type Err = OpkgError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.trim().to_lowercase().as_str() {
            "essential" => Ok(PackagePriority::Essential),
            "required" => Ok(PackagePriority::Required),
            "important" => Ok(PackagePriority::Important),
            "standard" => Ok(PackagePriority::Standard),
            "optional" => Ok(PackagePriority::Optional),
            "extra" => Ok(PackagePriority::Extra),
            _ => Ok(PackagePriority::Optional), // Default fallback
        }
    }
}

/// Comprehensive package information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageInfo {
    pub name: String,
    pub version: String,
    pub description: String,
    pub section: String,
    pub category: String,
    pub repository: String,
    pub maintainer: String,
    pub architecture: String,
    pub installed_size: u64,
    pub download_size: u64,
    pub status: PackageStatus,
    pub priority: PackagePriority,
    pub depends: Vec<String>,
    pub conflicts: Vec<String>,
    pub provides: Vec<String>,
    pub replaces: Vec<String>,
    pub homepage: Option<String>,
    pub source: Option<String>,
    pub filename: Option<String>,
    pub md5sum: Option<String>,
    pub sha256sum: Option<String>,
}

/// Package search criteria
#[derive(Debug, Clone, Default)]
pub struct PackageSearchCriteria {
    pub name_pattern: Option<String>,
    pub description_pattern: Option<String>,
    pub section: Option<String>,
    pub status: Option<PackageStatus>,
    pub architecture: Option<String>,
}

/// Package update information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageUpdate {
    pub name: String,
    pub current_version: String,
    pub available_version: String,
    pub is_security_update: bool,
    pub changelog: Option<String>,
    pub dependencies: Vec<String>,
    pub download_size: u64,
}

/// Main opkg interface
pub struct OpkgInterface {
    opkg_path: String,
    verbose: bool,
}

impl OpkgInterface {
    /// Create a new opkg interface
    pub fn new() -> OpkgResult<Self> {
        let opkg_path = Self::find_opkg_binary()?;
        Ok(Self {
            opkg_path,
            verbose: false,
        })
    }

    /// Create a new opkg interface with custom binary path
    pub fn with_path<P: AsRef<str>>(path: P) -> Self {
        Self {
            opkg_path: path.as_ref().to_string(),
            verbose: false,
        }
    }

    /// Enable verbose output
    pub fn verbose(mut self, verbose: bool) -> Self {
        self.verbose = verbose;
        self
    }

    /// Find opkg binary in common locations
    fn find_opkg_binary() -> OpkgResult<String> {
        let paths = ["/bin/opkg", "/usr/bin/opkg", "/sbin/opkg"];

        for path in &paths {
            if std::path::Path::new(path).exists() {
                return Ok(path.to_string());
            }
        }

        // Check if opkg is in PATH
        if let Ok(output) = Command::new("which").arg("opkg").output() {
            if output.status.success() {
                let path = String::from_utf8_lossy(&output.stdout).trim().to_string();
                if !path.is_empty() {
                    return Ok(path);
                }
            }
        }

        // Fallback for development/testing
        Ok("opkg".to_string())
    }

    /// Execute opkg command with arguments
    fn execute_command(&self, args: &[&str]) -> OpkgResult<String> {
        let mut cmd = Command::new(&self.opkg_path);
        cmd.args(args);

        if self.verbose {
            cmd.arg("-V").arg("2");
        }

        let output = cmd.output()
            .map_err(|e| OpkgError::CommandFailed(format!("Failed to execute opkg: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(OpkgError::CommandFailed(format!("opkg command failed: {}", stderr)));
        }

        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    }

    /// Update package lists from repositories
    pub fn update(&self) -> OpkgResult<()> {
        self.execute_command(&["update"])?;
        Ok(())
    }

    /// Install a package
    pub fn install(&self, package_name: &str) -> OpkgResult<()> {
        self.execute_command(&["install", package_name])?;
        Ok(())
    }

    /// Install multiple packages
    pub fn install_multiple(&self, package_names: &[&str]) -> OpkgResult<()> {
        let mut args = vec!["install"];
        args.extend(package_names);
        self.execute_command(&args)?;
        Ok(())
    }

    /// Remove a package
    pub fn remove(&self, package_name: &str) -> OpkgResult<()> {
        self.execute_command(&["remove", package_name])?;
        Ok(())
    }

    /// Remove multiple packages
    pub fn remove_multiple(&self, package_names: &[&str]) -> OpkgResult<()> {
        let mut args = vec!["remove"];
        args.extend(package_names);
        self.execute_command(&args)?;
        Ok(())
    }

    /// Upgrade a specific package
    pub fn upgrade(&self, package_name: &str) -> OpkgResult<()> {
        self.execute_command(&["upgrade", package_name])?;
        Ok(())
    }

    /// Upgrade multiple packages
    pub fn upgrade_multiple(&self, package_names: &[&str]) -> OpkgResult<()> {
        let mut args = vec!["upgrade"];
        args.extend(package_names);
        self.execute_command(&args)?;
        Ok(())
    }

    /// List all available packages
    pub fn list_all(&self) -> OpkgResult<Vec<PackageInfo>> {
        let output = self.execute_command(&["list"])?;
        self.parse_package_list(&output)
    }

    /// List installed packages
    pub fn list_installed(&self) -> OpkgResult<Vec<PackageInfo>> {
        let output = self.execute_command(&["list-installed"])?;
        self.parse_package_list(&output)
    }

    /// List upgradable packages
    pub fn list_upgradable(&self) -> OpkgResult<Vec<PackageInfo>> {
        let output = self.execute_command(&["list-upgradable"])?;
        self.parse_package_list(&output)
    }

    /// Get detailed information about a package
    pub fn info(&self, package_name: &str) -> OpkgResult<PackageInfo> {
        let output = self.execute_command(&["info", package_name])?;
        self.parse_package_info(&output)
    }

    /// Search for packages matching a pattern
    pub fn search(&self, pattern: &str) -> OpkgResult<Vec<PackageInfo>> {
        let output = self.execute_command(&["search", pattern])?;
        self.parse_package_list(&output)
    }

    /// Search packages with advanced criteria
    pub fn search_advanced(&self, criteria: &PackageSearchCriteria) -> OpkgResult<Vec<PackageInfo>> {
        let all_packages = self.list_all()?;

        let filtered: Vec<PackageInfo> = all_packages.into_iter()
            .filter(|pkg| {
                // Filter by name pattern
                if let Some(ref pattern) = criteria.name_pattern {
                    if !pkg.name.contains(pattern) {
                        return false;
                    }
                }

                // Filter by description pattern
                if let Some(ref pattern) = criteria.description_pattern {
                    if !pkg.description.to_lowercase().contains(&pattern.to_lowercase()) {
                        return false;
                    }
                }

                // Filter by section
                if let Some(ref section) = criteria.section {
                    if pkg.section != *section {
                        return false;
                    }
                }

                // Filter by status
                if let Some(ref status) = criteria.status {
                    if pkg.status != *status {
                        return false;
                    }
                }

                // Filter by architecture
                if let Some(ref arch) = criteria.architecture {
                    if pkg.architecture != *arch {
                        return false;
                    }
                }

                true
            })
            .collect();

        Ok(filtered)
    }

    /// Get list of files installed by a package
    pub fn files(&self, package_name: &str) -> OpkgResult<Vec<String>> {
        let output = self.execute_command(&["files", package_name])?;
        let files: Vec<String> = output
            .lines()
            .skip(1) // Skip the first line which contains package info
            .map(|line| line.trim().to_string())
            .filter(|line| !line.is_empty())
            .collect();

        Ok(files)
    }

    /// Check what packages depend on a given package
    pub fn whatdepends(&self, package_name: &str) -> OpkgResult<Vec<String>> {
        let output = self.execute_command(&["whatdepends", package_name])?;
        let packages: Vec<String> = output
            .lines()
            .skip(1) // Skip the first line which contains the query package
            .map(|line| line.trim().to_string())
            .filter(|line| !line.is_empty())
            .collect();

        Ok(packages)
    }

    /// Check what package provides a specific file
    pub fn whatprovides(&self, file_path: &str) -> OpkgResult<Vec<String>> {
        let output = self.execute_command(&["search", file_path])?;
        let packages: Vec<String> = output
            .lines()
            .map(|line| {
                // Format is usually "package_name - file_path"
                if let Some(dash_pos) = line.find(" - ") {
                    line[..dash_pos].trim().to_string()
                } else {
                    line.trim().to_string()
                }
            })
            .filter(|line| !line.is_empty())
            .collect();

        Ok(packages)
    }

    /// Parse package list output from opkg list commands
    fn parse_package_list(&self, output: &str) -> OpkgResult<Vec<PackageInfo>> {
        let mut packages = Vec::new();

        for line in output.lines() {
            if line.trim().is_empty() {
                continue;
            }

            // Parse line format: "package_name - version - description"
            let parts: Vec<&str> = line.splitn(3, " - ").collect();
            if parts.len() >= 2 {
                let name = parts[0].trim().to_string();
                let version = parts[1].trim().to_string();
                let description = if parts.len() > 2 {
                    parts[2].trim().to_string()
                } else {
                    String::new()
                };

                // Create basic package info - detailed info requires separate info call
                let package = PackageInfo {
                    name: name.clone(),
                    version,
                    description,
                    section: String::new(),
                    category: String::new(),
                    repository: String::new(),
                    maintainer: String::new(),
                    architecture: String::new(),
                    installed_size: 0,
                    download_size: 0,
                    status: PackageStatus::NotInstalled, // Will be determined by command type
                    priority: PackagePriority::Optional,
                    depends: Vec::new(),
                    conflicts: Vec::new(),
                    provides: Vec::new(),
                    replaces: Vec::new(),
                    homepage: None,
                    source: None,
                    filename: None,
                    md5sum: None,
                    sha256sum: None,
                };

                packages.push(package);
            }
        }

        Ok(packages)
    }

    /// Parse detailed package information from opkg info command
    fn parse_package_info(&self, output: &str) -> OpkgResult<PackageInfo> {
        let mut fields = HashMap::new();
        let mut current_field = String::new();
        let mut current_value = String::new();

        for line in output.lines() {
            if line.starts_with(' ') || line.starts_with('\t') {
                // Continuation of previous field
                if !current_value.is_empty() {
                    current_value.push(' ');
                }
                current_value.push_str(line.trim());
            } else if let Some(colon_pos) = line.find(':') {
                // Save previous field if exists
                if !current_field.is_empty() {
                    fields.insert(current_field.clone(), current_value.clone());
                }

                // Start new field
                current_field = line[..colon_pos].trim().to_string();
                current_value = line[colon_pos + 1..].trim().to_string();
            }
        }

        // Save last field
        if !current_field.is_empty() {
            fields.insert(current_field, current_value);
        }

        // Extract required fields
        let name = fields.get("Package")
            .ok_or_else(|| OpkgError::ParseError("Missing Package field".to_string()))?
            .clone();

        let version = fields.get("Version")
            .ok_or_else(|| OpkgError::ParseError("Missing Version field".to_string()))?
            .clone();

        let description = fields.get("Description").cloned().unwrap_or_default();

        // Parse optional fields
        let section = fields.get("Section").cloned().unwrap_or_default();
        let maintainer = fields.get("Maintainer").cloned().unwrap_or_default();
        let architecture = fields.get("Architecture").cloned().unwrap_or_default();
        let source = fields.get("Source").cloned();
        let homepage = fields.get("Homepage").cloned();
        let filename = fields.get("Filename").cloned();
        let md5sum = fields.get("MD5Sum").cloned();
        let sha256sum = fields.get("SHA256sum").cloned();

        // Parse size fields
        let installed_size = fields.get("Installed-Size")
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or(0);

        let download_size = fields.get("Size")
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or(0);

        // Parse status
        let status = fields.get("Status")
            .map(|s| PackageStatus::from_str(s).unwrap_or(PackageStatus::NotInstalled))
            .unwrap_or(PackageStatus::NotInstalled);

        // Parse priority
        let priority = fields.get("Priority")
            .map(|s| PackagePriority::from_str(s).unwrap_or(PackagePriority::Optional))
            .unwrap_or(PackagePriority::Optional);

        // Parse dependency lists
        let depends = Self::parse_dependency_list(fields.get("Depends"));
        let conflicts = Self::parse_dependency_list(fields.get("Conflicts"));
        let provides = Self::parse_dependency_list(fields.get("Provides"));
        let replaces = Self::parse_dependency_list(fields.get("Replaces"));

        Ok(PackageInfo {
            name,
            version,
            description,
            section: section.clone(),
            category: section, // Use section as category fallback
            repository: String::new(), // Not available in info output
            maintainer,
            architecture,
            installed_size,
            download_size,
            status,
            priority,
            depends,
            conflicts,
            provides,
            replaces,
            homepage,
            source,
            filename,
            md5sum,
            sha256sum,
        })
    }

    /// Parse dependency list from comma-separated string
    fn parse_dependency_list(field: Option<&String>) -> Vec<String> {
        field
            .map(|s| {
                s.split(',')
                    .map(|dep| {
                        // Remove version constraints like (>= 1.0)
                        if let Some(paren_pos) = dep.find('(') {
                            dep[..paren_pos].trim().to_string()
                        } else {
                            dep.trim().to_string()
                        }
                    })
                    .filter(|dep| !dep.is_empty())
                    .collect()
            })
            .unwrap_or_default()
    }

    /// Clean package cache
    pub fn clean(&self) -> OpkgResult<()> {
        self.execute_command(&["clean"])?;
        Ok(())
    }

    /// Get opkg configuration information
    pub fn get_config(&self) -> OpkgResult<OpkgConfig> {
        // Try to read configuration from /etc/opkg.conf
        let config_content = std::fs::read_to_string("/etc/opkg.conf")
            .unwrap_or_else(|_| String::new());

        let mut config = OpkgConfig::default();

        for line in config_content.lines() {
            let line = line.trim();
            if line.starts_with("dest ") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 3 && parts[1] == "root" {
                    config.dest_root = parts[2].to_string();
                }
            } else if line.starts_with("lists_dir ") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    config.lists_dir = parts[1].to_string();
                }
            }
        }

        // Try to read repository configuration
        if let Ok(distfeeds_content) = std::fs::read_to_string("/etc/opkg/distfeeds.conf") {
            config.repositories = distfeeds_content
                .lines()
                .filter(|line| !line.trim().is_empty() && !line.trim().starts_with('#'))
                .map(|line| line.to_string())
                .collect();
        }

        Ok(config)
    }
}

/// OPKG configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpkgConfig {
    pub dest_root: String,
    pub conf_file: String,
    pub cache_dir: String,
    pub lists_dir: String,
    pub lock_file: String,
    pub tmp_dir: String,
    pub repositories: Vec<String>,
}

impl Default for OpkgConfig {
    fn default() -> Self {
        Self {
            dest_root: "/".to_string(),
            conf_file: "/etc/opkg.conf".to_string(),
            cache_dir: "/var/opkg-lists".to_string(),
            lists_dir: "/var/opkg-lists".to_string(),
            lock_file: "/var/lock/opkg.lock".to_string(),
            tmp_dir: "/tmp".to_string(),
            repositories: Vec::new(),
        }
    }
}

impl Default for OpkgInterface {
    fn default() -> Self {
        Self::new().expect("Failed to create OpkgInterface")
    }
}
