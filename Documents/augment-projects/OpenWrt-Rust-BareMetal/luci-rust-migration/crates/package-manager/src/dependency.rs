//! Dependency Resolution
//!
//! This module provides dependency resolution and conflict detection for package management.

use crate::{PackageError, PackageResult, PackageInfo, PackageStatus};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet, VecDeque};

/// Dependency relationship type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DependencyType {
    /// Package depends on another package
    Depends,
    /// Package conflicts with another package
    Conflicts,
    /// Package provides functionality of another package
    Provides,
    /// Package replaces another package
    Replaces,
    /// Package recommends another package (optional)
    Recommends,
    /// Package suggests another package (optional)
    Suggests,
}

/// Dependency information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyInfo {
    pub package: String,
    pub dependencies: Vec<String>,
    pub conflicts: Vec<String>,
    pub missing_dependencies: Vec<String>,
    pub conflicting_packages: Vec<String>,
    pub install_order: Vec<String>,
    pub total_download_size: u64,
    pub total_installed_size: u64,
}

/// Dependency resolver
pub struct DependencyResolver;

impl DependencyResolver {
    /// Create a new dependency resolver
    pub fn new() -> Self {
        Self
    }

    /// Resolve dependencies for a package
    pub fn resolve_dependencies(
        &self,
        package: &PackageInfo,
        all_packages: &HashMap<String, PackageInfo>,
    ) -> PackageResult<DependencyInfo> {
        let mut visited = HashSet::new();
        let mut dependencies = Vec::new();
        let mut missing_dependencies = Vec::new();
        let mut conflicts = Vec::new();
        let mut conflicting_packages = Vec::new();

        // Resolve dependencies recursively
        self.resolve_recursive(
            &package.name,
            all_packages,
            &mut visited,
            &mut dependencies,
            &mut missing_dependencies,
        )?;

        // Check for conflicts
        self.check_conflicts(
            package,
            all_packages,
            &dependencies,
            &mut conflicts,
            &mut conflicting_packages,
        )?;

        // Calculate install order
        let install_order = self.calculate_install_order(&dependencies, all_packages)?;

        // Calculate sizes
        let (total_download_size, total_installed_size) = 
            self.calculate_sizes(&dependencies, all_packages);

        Ok(DependencyInfo {
            package: package.name.clone(),
            dependencies,
            conflicts,
            missing_dependencies,
            conflicting_packages,
            install_order,
            total_download_size,
            total_installed_size,
        })
    }

    /// Resolve dependencies recursively
    fn resolve_recursive(
        &self,
        package_name: &str,
        all_packages: &HashMap<String, PackageInfo>,
        visited: &mut HashSet<String>,
        dependencies: &mut Vec<String>,
        missing_dependencies: &mut Vec<String>,
    ) -> PackageResult<()> {
        if visited.contains(package_name) {
            return Ok(());
        }

        visited.insert(package_name.to_string());

        if let Some(package) = all_packages.get(package_name) {
            for dep in &package.depends {
                let dep_name = self.parse_dependency(dep);
                
                if !dependencies.contains(&dep_name) && dep_name != package_name {
                    if all_packages.contains_key(&dep_name) {
                        dependencies.push(dep_name.clone());
                        self.resolve_recursive(&dep_name, all_packages, visited, dependencies, missing_dependencies)?;
                    } else {
                        // Check if any package provides this dependency
                        if !self.find_provider(&dep_name, all_packages).is_some() {
                            missing_dependencies.push(dep_name);
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Check for conflicts
    fn check_conflicts(
        &self,
        package: &PackageInfo,
        all_packages: &HashMap<String, PackageInfo>,
        dependencies: &[String],
        conflicts: &mut Vec<String>,
        conflicting_packages: &mut Vec<String>,
    ) -> PackageResult<()> {
        // Check direct conflicts
        for conflict in &package.conflicts {
            let conflict_name = self.parse_dependency(conflict);
            if let Some(conflict_pkg) = all_packages.get(&conflict_name) {
                if conflict_pkg.status == PackageStatus::Installed {
                    conflicts.push(conflict_name.clone());
                    conflicting_packages.push(conflict_pkg.name.clone());
                }
            }
        }

        // Check if any dependency conflicts with installed packages
        for dep_name in dependencies {
            if let Some(dep_pkg) = all_packages.get(dep_name) {
                for conflict in &dep_pkg.conflicts {
                    let conflict_name = self.parse_dependency(conflict);
                    if let Some(conflict_pkg) = all_packages.get(&conflict_name) {
                        if conflict_pkg.status == PackageStatus::Installed {
                            conflicts.push(conflict_name.clone());
                            conflicting_packages.push(conflict_pkg.name.clone());
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Calculate installation order using topological sort
    fn calculate_install_order(
        &self,
        dependencies: &[String],
        all_packages: &HashMap<String, PackageInfo>,
    ) -> PackageResult<Vec<String>> {
        let mut graph: HashMap<String, Vec<String>> = HashMap::new();
        let mut in_degree: HashMap<String, usize> = HashMap::new();

        // Build dependency graph
        for dep_name in dependencies {
            graph.insert(dep_name.clone(), Vec::new());
            in_degree.insert(dep_name.clone(), 0);
        }

        for dep_name in dependencies {
            if let Some(package) = all_packages.get(dep_name) {
                for dep in &package.depends {
                    let dep_dep_name = self.parse_dependency(dep);
                    if dependencies.contains(&dep_dep_name) {
                        graph.get_mut(&dep_dep_name).unwrap().push(dep_name.clone());
                        *in_degree.get_mut(dep_name).unwrap() += 1;
                    }
                }
            }
        }

        // Topological sort
        let mut queue = VecDeque::new();
        let mut result = Vec::new();

        // Find nodes with no incoming edges
        for (node, &degree) in &in_degree {
            if degree == 0 {
                queue.push_back(node.clone());
            }
        }

        while let Some(node) = queue.pop_front() {
            result.push(node.clone());

            if let Some(neighbors) = graph.get(&node) {
                for neighbor in neighbors {
                    let degree = in_degree.get_mut(neighbor).unwrap();
                    *degree -= 1;
                    if *degree == 0 {
                        queue.push_back(neighbor.clone());
                    }
                }
            }
        }

        // Check for circular dependencies
        if result.len() != dependencies.len() {
            return Err(PackageError::DependencyConflict(
                "Circular dependency detected".to_string()
            ));
        }

        Ok(result)
    }

    /// Calculate total download and installed sizes
    fn calculate_sizes(
        &self,
        dependencies: &[String],
        all_packages: &HashMap<String, PackageInfo>,
    ) -> (u64, u64) {
        let mut download_size = 0;
        let mut installed_size = 0;

        for dep_name in dependencies {
            if let Some(package) = all_packages.get(dep_name) {
                if package.status != PackageStatus::Installed {
                    download_size += package.download_size;
                    installed_size += package.installed_size;
                }
            }
        }

        (download_size, installed_size)
    }

    /// Parse dependency string to extract package name
    fn parse_dependency(&self, dep: &str) -> String {
        // Handle version constraints like "package (>= 1.0)"
        if let Some(pos) = dep.find(' ') {
            dep[..pos].to_string()
        } else {
            dep.to_string()
        }
    }

    /// Find a package that provides the given functionality
    fn find_provider(&self, name: &str, all_packages: &HashMap<String, PackageInfo>) -> Option<String> {
        for (pkg_name, package) in all_packages {
            if package.provides.contains(&name.to_string()) {
                return Some(pkg_name.clone());
            }
        }
        None
    }

    /// Check if packages can be safely removed
    pub fn check_removal_safety(
        &self,
        packages_to_remove: &[String],
        all_packages: &HashMap<String, PackageInfo>,
    ) -> PackageResult<RemovalInfo> {
        let mut affected_packages = Vec::new();
        let mut broken_dependencies = Vec::new();

        for pkg_name in packages_to_remove {
            // Find packages that depend on this package
            for (name, package) in all_packages {
                if package.status == PackageStatus::Installed && !packages_to_remove.contains(name) {
                    for dep in &package.depends {
                        let dep_name = self.parse_dependency(dep);
                        if dep_name == *pkg_name {
                            affected_packages.push(name.clone());
                            broken_dependencies.push(format!("{} depends on {}", name, pkg_name));
                        }
                    }
                }
            }
        }

        let safe_to_remove = broken_dependencies.is_empty();
        Ok(RemovalInfo {
            packages_to_remove: packages_to_remove.to_vec(),
            affected_packages,
            broken_dependencies,
            safe_to_remove,
        })
    }

    /// Find packages that can be auto-removed
    pub fn find_auto_removable(
        &self,
        all_packages: &HashMap<String, PackageInfo>,
    ) -> PackageResult<Vec<String>> {
        let mut auto_removable = Vec::new();
        let installed_packages: Vec<&PackageInfo> = all_packages.values()
            .filter(|p| p.status == PackageStatus::Installed)
            .collect();

        for package in &installed_packages {
            // Check if this package is a dependency of any other installed package
            let mut is_needed = false;
            
            for other_package in &installed_packages {
                if other_package.name != package.name {
                    for dep in &other_package.depends {
                        let dep_name = self.parse_dependency(dep);
                        if dep_name == package.name {
                            is_needed = true;
                            break;
                        }
                    }
                    if is_needed {
                        break;
                    }
                }
            }

            // If not needed and not essential, it can be auto-removed
            if !is_needed && package.priority != crate::PackagePriority::Essential {
                auto_removable.push(package.name.clone());
            }
        }

        Ok(auto_removable)
    }
}

/// Package removal information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemovalInfo {
    pub packages_to_remove: Vec<String>,
    pub affected_packages: Vec<String>,
    pub broken_dependencies: Vec<String>,
    pub safe_to_remove: bool,
}

impl Default for DependencyResolver {
    fn default() -> Self {
        Self::new()
    }
}
