//! OPKG Interface
//!
//! This module provides a Rust interface to the OpenWrt Package Manager (opkg).

use crate::{PackageError, PackageResult, PackageInfo, PackageStatus, PackagePriority};
use std::process::{Command, Stdio};
use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};

/// OPKG command interface
pub struct OpkgInterface {
    opkg_path: String,
}

impl OpkgInterface {
    /// Create a new OPKG interface
    pub fn new() -> PackageResult<Self> {
        let opkg_path = Self::find_opkg_binary()?;
        Ok(Self { opkg_path })
    }

    /// Find opkg binary path
    fn find_opkg_binary() -> PackageResult<String> {
        // Try common locations for opkg
        let paths = ["/bin/opkg", "/usr/bin/opkg", "/sbin/opkg"];
        
        for path in &paths {
            if std::path::Path::new(path).exists() {
                return Ok(path.to_string());
            }
        }

        // Mock path for development
        Ok("opkg".to_string())
    }

    /// Update package lists
    pub fn update(&self) -> PackageResult<()> {
        let output = Command::new(&self.opkg_path)
            .arg("update")
            .output()
            .map_err(|e| PackageError::CommandFailed(format!("Failed to run opkg update: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(PackageError::RepositoryError(format!("Update failed: {}", stderr)));
        }

        Ok(())
    }

    /// List all available packages
    pub fn list_all_packages(&self) -> PackageResult<Vec<PackageInfo>> {
        // Mock implementation - in real system would run `opkg list` and parse output
        Ok(vec![
            PackageInfo {
                name: "base-files".to_string(),
                version: "1467-r23497-6637af95aa".to_string(),
                description: "Base filesystem for OpenWrt".to_string(),
                section: "base".to_string(),
                category: "Base system".to_string(),
                repository: "base".to_string(),
                maintainer: "OpenWrt Developers".to_string(),
                architecture: "mips_24kc".to_string(),
                installed_size: 1024 * 50,
                download_size: 1024 * 20,
                status: PackageStatus::Installed,
                priority: PackagePriority::Essential,
                depends: vec![],
                conflicts: vec![],
                provides: vec![],
                replaces: vec![],
                homepage: Some("https://openwrt.org".to_string()),
                source: None,
                filename: Some("base-files_1467-r23497-6637af95aa_mips_24kc.ipk".to_string()),
                md5sum: Some("d41d8cd98f00b204e9800998ecf8427e".to_string()),
                sha256sum: None,
            },
            PackageInfo {
                name: "dropbear".to_string(),
                version: "2022.83-2".to_string(),
                description: "Small SSH2 client/server".to_string(),
                section: "net".to_string(),
                category: "Network".to_string(),
                repository: "base".to_string(),
                maintainer: "OpenWrt Developers".to_string(),
                architecture: "mips_24kc".to_string(),
                installed_size: 1024 * 120,
                download_size: 1024 * 80,
                status: PackageStatus::Installed,
                priority: PackagePriority::Standard,
                depends: vec!["libc".to_string(), "zlib".to_string()],
                conflicts: vec!["openssh-server".to_string()],
                provides: vec!["ssh-server".to_string()],
                replaces: vec![],
                homepage: Some("https://matt.ucc.asn.au/dropbear/dropbear.html".to_string()),
                source: None,
                filename: Some("dropbear_2022.83-2_mips_24kc.ipk".to_string()),
                md5sum: Some("098f6bcd4621d373cade4e832627b4f6".to_string()),
                sha256sum: None,
            },
            PackageInfo {
                name: "luci".to_string(),
                version: "git-23.093.47327-f98c047".to_string(),
                description: "LuCI - Lua Configuration Interface".to_string(),
                section: "luci".to_string(),
                category: "LuCI".to_string(),
                repository: "luci".to_string(),
                maintainer: "OpenWrt LuCI Team".to_string(),
                architecture: "all".to_string(),
                installed_size: 1024 * 200,
                download_size: 1024 * 150,
                status: PackageStatus::NotInstalled,
                priority: PackagePriority::Optional,
                depends: vec!["luci-base".to_string(), "luci-mod-admin-full".to_string()],
                conflicts: vec![],
                provides: vec![],
                replaces: vec![],
                homepage: Some("https://github.com/openwrt/luci".to_string()),
                source: None,
                filename: Some("luci_git-23.093.47327-f98c047_all.ipk".to_string()),
                md5sum: Some("5d41402abc4b2a76b9719d911017c592".to_string()),
                sha256sum: None,
            },
            PackageInfo {
                name: "wget".to_string(),
                version: "1.21.3-1".to_string(),
                description: "Non-interactive network downloader".to_string(),
                section: "net".to_string(),
                category: "Network".to_string(),
                repository: "packages".to_string(),
                maintainer: "OpenWrt Developers".to_string(),
                architecture: "mips_24kc".to_string(),
                installed_size: 1024 * 300,
                download_size: 1024 * 200,
                status: PackageStatus::Upgradable,
                priority: PackagePriority::Optional,
                depends: vec!["libc".to_string(), "libpcre".to_string(), "zlib".to_string()],
                conflicts: vec![],
                provides: vec![],
                replaces: vec![],
                homepage: Some("https://www.gnu.org/software/wget/".to_string()),
                source: None,
                filename: Some("wget_1.21.3-1_mips_24kc.ipk".to_string()),
                md5sum: Some("ad0234829205b9033196ba818f7a872b".to_string()),
                sha256sum: None,
            },
        ])
    }

    /// List installed packages
    pub fn list_installed(&self) -> PackageResult<Vec<PackageInfo>> {
        let all_packages = self.list_all_packages()?;
        Ok(all_packages.into_iter()
            .filter(|p| p.status == PackageStatus::Installed)
            .collect())
    }

    /// List upgradable packages
    pub fn list_upgradable(&self) -> PackageResult<Vec<PackageInfo>> {
        let all_packages = self.list_all_packages()?;
        Ok(all_packages.into_iter()
            .filter(|p| p.status == PackageStatus::Upgradable)
            .collect())
    }

    /// Install a package
    pub fn install(&self, package_name: &str) -> PackageResult<()> {
        log::info!("Installing package: {}", package_name);
        
        // Mock implementation - in real system would run `opkg install <package>`
        let output = Command::new(&self.opkg_path)
            .arg("install")
            .arg(package_name)
            .output()
            .map_err(|e| PackageError::CommandFailed(format!("Failed to run opkg install: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(PackageError::InstallationFailed(format!("Install failed for {}: {}", package_name, stderr)));
        }

        Ok(())
    }

    /// Remove a package
    pub fn remove(&self, package_name: &str) -> PackageResult<()> {
        log::info!("Removing package: {}", package_name);
        
        // Mock implementation - in real system would run `opkg remove <package>`
        let output = Command::new(&self.opkg_path)
            .arg("remove")
            .arg(package_name)
            .output()
            .map_err(|e| PackageError::CommandFailed(format!("Failed to run opkg remove: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(PackageError::RemovalFailed(format!("Remove failed for {}: {}", package_name, stderr)));
        }

        Ok(())
    }

    /// Upgrade a specific package
    pub fn upgrade(&self, package_name: &str) -> PackageResult<()> {
        log::info!("Upgrading package: {}", package_name);
        
        // Mock implementation - in real system would run `opkg upgrade <package>`
        let output = Command::new(&self.opkg_path)
            .arg("upgrade")
            .arg(package_name)
            .output()
            .map_err(|e| PackageError::CommandFailed(format!("Failed to run opkg upgrade: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(PackageError::InstallationFailed(format!("Upgrade failed for {}: {}", package_name, stderr)));
        }

        Ok(())
    }

    /// Upgrade all packages
    pub fn upgrade_all(&self) -> PackageResult<()> {
        log::info!("Upgrading all packages");
        
        // Mock implementation - in real system would run `opkg list-upgradable` then upgrade each
        let upgradable = self.list_upgradable()?;
        
        for package in upgradable {
            self.upgrade(&package.name)?;
        }

        Ok(())
    }

    /// Get package information
    pub fn info(&self, package_name: &str) -> PackageResult<PackageInfo> {
        // Mock implementation - in real system would run `opkg info <package>`
        let all_packages = self.list_all_packages()?;
        all_packages.into_iter()
            .find(|p| p.name == package_name)
            .ok_or_else(|| PackageError::PackageNotFound(package_name.to_string()))
    }

    /// Search for packages
    pub fn search(&self, pattern: &str) -> PackageResult<Vec<PackageInfo>> {
        let all_packages = self.list_all_packages()?;
        Ok(all_packages.into_iter()
            .filter(|p| p.name.contains(pattern) || p.description.to_lowercase().contains(&pattern.to_lowercase()))
            .collect())
    }

    /// Check if package is installed
    pub fn is_installed(&self, package_name: &str) -> PackageResult<bool> {
        let package = self.info(package_name)?;
        Ok(package.status == PackageStatus::Installed)
    }

    /// Get package status
    pub fn status(&self, package_name: &str) -> PackageResult<PackageStatus> {
        let package = self.info(package_name)?;
        Ok(package.status)
    }

    /// Clean package cache
    pub fn clean(&self) -> PackageResult<()> {
        log::info!("Cleaning package cache");
        
        // Mock implementation - in real system would run `opkg clean`
        let output = Command::new(&self.opkg_path)
            .arg("clean")
            .output()
            .map_err(|e| PackageError::CommandFailed(format!("Failed to run opkg clean: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(PackageError::CommandFailed(format!("Clean failed: {}", stderr)));
        }

        Ok(())
    }

    /// Get opkg configuration
    pub fn get_config(&self) -> PackageResult<OpkgConfig> {
        // Mock implementation - in real system would read /etc/opkg.conf
        Ok(OpkgConfig {
            dest_root: "/".to_string(),
            conf_file: "/etc/opkg.conf".to_string(),
            cache_dir: "/var/opkg-lists".to_string(),
            lists_dir: "/var/opkg-lists".to_string(),
            lock_file: "/var/lock/opkg.lock".to_string(),
            tmp_dir: "/tmp".to_string(),
            repositories: vec![
                "src/gz openwrt_core https://downloads.openwrt.org/releases/23.05.0/targets/ath79/generic/packages".to_string(),
                "src/gz openwrt_base https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/base".to_string(),
                "src/gz openwrt_luci https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/luci".to_string(),
                "src/gz openwrt_packages https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/packages".to_string(),
                "src/gz openwrt_routing https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/routing".to_string(),
            ],
        })
    }
}

/// OPKG configuration
#[derive(Debug, Clone)]
pub struct OpkgConfig {
    pub dest_root: String,
    pub conf_file: String,
    pub cache_dir: String,
    pub lists_dir: String,
    pub lock_file: String,
    pub tmp_dir: String,
    pub repositories: Vec<String>,
}

impl Default for OpkgInterface {
    fn default() -> Self {
        Self::new().expect("Failed to create OpkgInterface")
    }
}
