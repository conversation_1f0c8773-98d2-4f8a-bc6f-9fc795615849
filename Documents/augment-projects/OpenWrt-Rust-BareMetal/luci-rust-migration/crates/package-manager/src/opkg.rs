//! OPKG Interface
//!
//! This module provides a high-level Rust interface to the OpenWrt Package Manager (opkg).

use crate::{PackageError, PackageResult, PackageInfo, PackageStatus, PackagePriority};
use luci_opkg_integration::{OpkgInterface as LowLevelOpkgInterface, OpkgError, PackageSearchCriteria};
use std::collections::HashMap;

/// High-level OPKG command interface
pub struct OpkgInterface {
    inner: LowLevelOpkgInterface,
    cache: HashMap<String, PackageInfo>,
}

impl OpkgInterface {
    /// Create a new OPKG interface
    pub fn new() -> PackageResult<Self> {
        let inner = LowLevelOpkgInterface::new()
            .map_err(|e| PackageError::CommandFailed(e.to_string()))?;

        Ok(Self {
            inner,
            cache: HashMap::new(),
        })
    }

    /// Create a new OPKG interface with verbose output
    pub fn new_verbose() -> PackageResult<Self> {
        let inner = LowLevelOpkgInterface::new()
            .map_err(|e| PackageError::CommandFailed(e.to_string()))?
            .verbose(true);

        Ok(Self {
            inner,
            cache: HashMap::new(),
        })
    }

    /// Convert OpkgError to PackageError
    fn convert_error(err: OpkgError) -> PackageError {
        match err {
            OpkgError::CommandFailed(msg) => PackageError::CommandFailed(msg),
            OpkgError::PackageNotFound(pkg) => PackageError::PackageNotFound(pkg),
            OpkgError::ParseError(msg) => PackageError::ParseError(msg),
            OpkgError::InstallationFailed(msg) => PackageError::InstallationFailed(msg),
            OpkgError::RemovalFailed(msg) => PackageError::RemovalFailed(msg),
            OpkgError::RepositoryError(msg) => PackageError::RepositoryError(msg),
            OpkgError::IoError(e) => PackageError::CommandFailed(e.to_string()),
        }
    }

    /// Convert low-level PackageInfo to high-level PackageInfo
    fn convert_package_info(pkg: luci_opkg_integration::PackageInfo) -> PackageInfo {
        PackageInfo {
            name: pkg.name,
            version: pkg.version,
            description: pkg.description,
            section: pkg.section,
            category: pkg.category,
            repository: pkg.repository,
            maintainer: pkg.maintainer,
            architecture: pkg.architecture,
            installed_size: pkg.installed_size,
            download_size: pkg.download_size,
            status: Self::convert_status(pkg.status),
            priority: Self::convert_priority(pkg.priority),
            depends: pkg.depends,
            conflicts: pkg.conflicts,
            provides: pkg.provides,
            replaces: pkg.replaces,
            homepage: pkg.homepage,
            source: pkg.source,
            filename: pkg.filename,
            md5sum: pkg.md5sum,
            sha256sum: pkg.sha256sum,
        }
    }

    /// Convert low-level PackageStatus to high-level PackageStatus
    fn convert_status(status: luci_opkg_integration::PackageStatus) -> PackageStatus {
        match status {
            luci_opkg_integration::PackageStatus::Installed => PackageStatus::Installed,
            luci_opkg_integration::PackageStatus::NotInstalled => PackageStatus::NotInstalled,
            luci_opkg_integration::PackageStatus::Upgradable => PackageStatus::Upgradable,
            luci_opkg_integration::PackageStatus::Hold => PackageStatus::Installed, // Map hold to installed
            luci_opkg_integration::PackageStatus::Unpacked => PackageStatus::NotInstalled, // Map unpacked to not installed
        }
    }

    /// Convert low-level PackagePriority to high-level PackagePriority
    fn convert_priority(priority: luci_opkg_integration::PackagePriority) -> PackagePriority {
        match priority {
            luci_opkg_integration::PackagePriority::Essential => PackagePriority::Essential,
            luci_opkg_integration::PackagePriority::Required => PackagePriority::Required,
            luci_opkg_integration::PackagePriority::Important => PackagePriority::Important,
            luci_opkg_integration::PackagePriority::Standard => PackagePriority::Standard,
            luci_opkg_integration::PackagePriority::Optional => PackagePriority::Optional,
            luci_opkg_integration::PackagePriority::Extra => PackagePriority::Extra,
        }
    }

    /// Update package lists
    pub fn update(&mut self) -> PackageResult<()> {
        self.inner.update()
            .map_err(Self::convert_error)?;

        // Clear cache after update
        self.cache.clear();

        Ok(())
    }

    /// List all available packages
    pub fn list_all_packages(&self) -> PackageResult<Vec<PackageInfo>> {
        let packages = self.inner.list_all()
            .map_err(Self::convert_error)?;

        Ok(packages.into_iter()
            .map(Self::convert_package_info)
            .collect())
    }

    /// List installed packages
    pub fn list_installed(&self) -> PackageResult<Vec<PackageInfo>> {
        let packages = self.inner.list_installed()
            .map_err(Self::convert_error)?;

        Ok(packages.into_iter()
            .map(Self::convert_package_info)
            .collect())
    }

    /// List upgradable packages
    pub fn list_upgradable(&self) -> PackageResult<Vec<PackageInfo>> {
        let packages = self.inner.list_upgradable()
            .map_err(Self::convert_error)?;

        Ok(packages.into_iter()
            .map(Self::convert_package_info)
            .collect())
    }

    /// Install a package
    pub fn install(&mut self, package_name: &str) -> PackageResult<()> {
        log::info!("Installing package: {}", package_name);

        self.inner.install(package_name)
            .map_err(Self::convert_error)?;

        // Clear cache entry for this package
        self.cache.remove(package_name);

        Ok(())
    }

    /// Install multiple packages
    pub fn install_multiple(&mut self, package_names: &[&str]) -> PackageResult<()> {
        log::info!("Installing packages: {:?}", package_names);

        self.inner.install_multiple(package_names)
            .map_err(Self::convert_error)?;

        // Clear cache entries for these packages
        for name in package_names {
            self.cache.remove(*name);
        }

        Ok(())
    }

    /// Remove a package
    pub fn remove(&mut self, package_name: &str) -> PackageResult<()> {
        log::info!("Removing package: {}", package_name);

        self.inner.remove(package_name)
            .map_err(Self::convert_error)?;

        // Clear cache entry for this package
        self.cache.remove(package_name);

        Ok(())
    }

    /// Remove multiple packages
    pub fn remove_multiple(&mut self, package_names: &[&str]) -> PackageResult<()> {
        log::info!("Removing packages: {:?}", package_names);

        self.inner.remove_multiple(package_names)
            .map_err(Self::convert_error)?;

        // Clear cache entries for these packages
        for name in package_names {
            self.cache.remove(*name);
        }

        Ok(())
    }

    /// Upgrade a specific package
    pub fn upgrade(&mut self, package_name: &str) -> PackageResult<()> {
        log::info!("Upgrading package: {}", package_name);

        self.inner.upgrade(package_name)
            .map_err(Self::convert_error)?;

        // Clear cache entry for this package
        self.cache.remove(package_name);

        Ok(())
    }

    /// Upgrade multiple packages
    pub fn upgrade_multiple(&mut self, package_names: &[&str]) -> PackageResult<()> {
        log::info!("Upgrading packages: {:?}", package_names);

        self.inner.upgrade_multiple(package_names)
            .map_err(Self::convert_error)?;

        // Clear cache entries for these packages
        for name in package_names {
            self.cache.remove(*name);
        }

        Ok(())
    }

    /// Upgrade all packages
    pub fn upgrade_all(&mut self) -> PackageResult<()> {
        log::info!("Upgrading all packages");

        let upgradable = self.list_upgradable()?;
        let package_names: Vec<&str> = upgradable.iter().map(|p| p.name.as_str()).collect();

        if !package_names.is_empty() {
            self.upgrade_multiple(&package_names)?;
        }

        Ok(())
    }

    /// Get package information (with caching)
    pub fn info(&mut self, package_name: &str) -> PackageResult<PackageInfo> {
        // Check cache first
        if let Some(cached_info) = self.cache.get(package_name) {
            return Ok(cached_info.clone());
        }

        // Fetch from opkg
        let package_info = self.inner.info(package_name)
            .map_err(Self::convert_error)?;

        let converted_info = Self::convert_package_info(package_info);

        // Cache the result
        self.cache.insert(package_name.to_string(), converted_info.clone());

        Ok(converted_info)
    }

    /// Search for packages
    pub fn search(&self, pattern: &str) -> PackageResult<Vec<PackageInfo>> {
        let packages = self.inner.search(pattern)
            .map_err(Self::convert_error)?;

        Ok(packages.into_iter()
            .map(Self::convert_package_info)
            .collect())
    }

    /// Advanced search with criteria
    pub fn search_advanced(&self, criteria: &PackageSearchCriteria) -> PackageResult<Vec<PackageInfo>> {
        let packages = self.inner.search_advanced(criteria)
            .map_err(Self::convert_error)?;

        Ok(packages.into_iter()
            .map(Self::convert_package_info)
            .collect())
    }

    /// Check if package is installed
    pub fn is_installed(&mut self, package_name: &str) -> PackageResult<bool> {
        let package = self.info(package_name)?;
        Ok(package.status == PackageStatus::Installed)
    }

    /// Get package status
    pub fn status(&mut self, package_name: &str) -> PackageResult<PackageStatus> {
        let package = self.info(package_name)?;
        Ok(package.status)
    }

    /// Get list of files installed by a package
    pub fn files(&self, package_name: &str) -> PackageResult<Vec<String>> {
        self.inner.files(package_name)
            .map_err(Self::convert_error)
    }

    /// Check what packages depend on a given package
    pub fn whatdepends(&self, package_name: &str) -> PackageResult<Vec<String>> {
        self.inner.whatdepends(package_name)
            .map_err(Self::convert_error)
    }

    /// Check what package provides a specific file
    pub fn whatprovides(&self, file_path: &str) -> PackageResult<Vec<String>> {
        self.inner.whatprovides(file_path)
            .map_err(Self::convert_error)
    }

    /// Clean package cache
    pub fn clean(&mut self) -> PackageResult<()> {
        log::info!("Cleaning package cache");

        self.inner.clean()
            .map_err(Self::convert_error)?;

        // Clear our internal cache as well
        self.cache.clear();

        Ok(())
    }

    /// Get opkg configuration
    pub fn get_config(&self) -> PackageResult<OpkgConfig> {
        let config = self.inner.get_config()
            .map_err(Self::convert_error)?;

        Ok(OpkgConfig {
            dest_root: config.dest_root,
            conf_file: config.conf_file,
            cache_dir: config.cache_dir,
            lists_dir: config.lists_dir,
            lock_file: config.lock_file,
            tmp_dir: config.tmp_dir,
            repositories: config.repositories,
        })
    }

    /// Clear internal package cache
    pub fn clear_cache(&mut self) {
        self.cache.clear();
    }

    /// Get cache statistics
    pub fn cache_stats(&self) -> (usize, usize) {
        (self.cache.len(), self.cache.capacity())
    }
}

/// OPKG configuration
#[derive(Debug, Clone)]
pub struct OpkgConfig {
    pub dest_root: String,
    pub conf_file: String,
    pub cache_dir: String,
    pub lists_dir: String,
    pub lock_file: String,
    pub tmp_dir: String,
    pub repositories: Vec<String>,
}

impl Default for OpkgInterface {
    fn default() -> Self {
        Self::new().expect("Failed to create OpkgInterface")
    }
}
