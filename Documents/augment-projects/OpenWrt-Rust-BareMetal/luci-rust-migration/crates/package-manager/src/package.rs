//! Package Information and Utilities
//!
//! This module provides package information structures and utility functions.

use crate::{PackageError, PackageResult, PackageInfo, PackageStatus, PackagePriority};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Package category information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageCategory {
    pub name: String,
    pub description: String,
    pub icon: Option<String>,
    pub package_count: usize,
}

/// Package section information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageSection {
    pub name: String,
    pub description: String,
    pub category: String,
    pub package_count: usize,
}

/// Package installation options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstallOptions {
    pub force_depends: bool,
    pub force_reinstall: bool,
    pub force_downgrade: bool,
    pub force_space: bool,
    pub no_deps: bool,
    pub download_only: bool,
}

impl Default for InstallOptions {
    fn default() -> Self {
        Self {
            force_depends: false,
            force_reinstall: false,
            force_downgrade: false,
            force_space: false,
            no_deps: false,
            download_only: false,
        }
    }
}

/// Package removal options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemovalOptions {
    pub force_depends: bool,
    pub force_removal_of_essential: bool,
    pub autoremove: bool,
    pub remove_config: bool,
}

impl Default for RemovalOptions {
    fn default() -> Self {
        Self {
            force_depends: false,
            force_removal_of_essential: false,
            autoremove: false,
            remove_config: false,
        }
    }
}

/// Package utilities
pub struct PackageUtils;

impl PackageUtils {
    /// Parse package name and version from string
    pub fn parse_package_string(package_str: &str) -> PackageResult<(String, Option<String>)> {
        if let Some(pos) = package_str.find('=') {
            let name = package_str[..pos].to_string();
            let version = package_str[pos + 1..].to_string();
            Ok((name, Some(version)))
        } else {
            Ok((package_str.to_string(), None))
        }
    }

    /// Format package size in human readable format
    pub fn format_size(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        if unit_index == 0 {
            format!("{} {}", bytes, UNITS[unit_index])
        } else {
            format!("{:.1} {}", size, UNITS[unit_index])
        }
    }

    /// Get package categories with counts
    pub fn get_categories_with_counts(packages: &[PackageInfo]) -> Vec<PackageCategory> {
        let mut category_counts: HashMap<String, usize> = HashMap::new();
        
        for package in packages {
            *category_counts.entry(package.category.clone()).or_insert(0) += 1;
        }

        let mut categories = Vec::new();
        for (name, count) in category_counts {
            categories.push(PackageCategory {
                name: name.clone(),
                description: Self::get_category_description(&name),
                icon: Self::get_category_icon(&name),
                package_count: count,
            });
        }

        categories.sort_by(|a, b| a.name.cmp(&b.name));
        categories
    }

    /// Get package sections with counts
    pub fn get_sections_with_counts(packages: &[PackageInfo]) -> Vec<PackageSection> {
        let mut section_counts: HashMap<String, usize> = HashMap::new();
        let mut section_categories: HashMap<String, String> = HashMap::new();
        
        for package in packages {
            *section_counts.entry(package.section.clone()).or_insert(0) += 1;
            section_categories.insert(package.section.clone(), package.category.clone());
        }

        let mut sections = Vec::new();
        for (name, count) in section_counts {
            let category = section_categories.get(&name).cloned().unwrap_or_default();
            sections.push(PackageSection {
                name: name.clone(),
                description: Self::get_section_description(&name),
                category,
                package_count: count,
            });
        }

        sections.sort_by(|a, b| a.name.cmp(&b.name));
        sections
    }

    /// Get category description
    fn get_category_description(category: &str) -> String {
        match category {
            "Base system" => "Essential system components and utilities".to_string(),
            "Network" => "Network tools and services".to_string(),
            "LuCI" => "Web interface components".to_string(),
            "Kernel modules" => "Linux kernel modules and drivers".to_string(),
            "Libraries" => "Shared libraries and runtime components".to_string(),
            "Utilities" => "System utilities and tools".to_string(),
            "Languages" => "Programming languages and interpreters".to_string(),
            "Multimedia" => "Audio and video applications".to_string(),
            "Development" => "Development tools and compilers".to_string(),
            _ => format!("{} packages", category),
        }
    }

    /// Get category icon
    fn get_category_icon(category: &str) -> Option<String> {
        match category {
            "Base system" => Some("system".to_string()),
            "Network" => Some("network".to_string()),
            "LuCI" => Some("web".to_string()),
            "Kernel modules" => Some("chip".to_string()),
            "Libraries" => Some("library".to_string()),
            "Utilities" => Some("tools".to_string()),
            "Languages" => Some("code".to_string()),
            "Multimedia" => Some("media".to_string()),
            "Development" => Some("development".to_string()),
            _ => None,
        }
    }

    /// Get section description
    fn get_section_description(section: &str) -> String {
        match section {
            "base" => "Base system files and utilities".to_string(),
            "net" => "Network applications and services".to_string(),
            "luci" => "LuCI web interface modules".to_string(),
            "kernel" => "Kernel modules and drivers".to_string(),
            "libs" => "Shared libraries".to_string(),
            "utils" => "System utilities".to_string(),
            "lang" => "Programming languages".to_string(),
            "multimedia" => "Multimedia applications".to_string(),
            "devel" => "Development tools".to_string(),
            "admin" => "System administration tools".to_string(),
            "mail" => "Mail servers and clients".to_string(),
            "web" => "Web servers and applications".to_string(),
            "database" => "Database systems".to_string(),
            "security" => "Security tools and services".to_string(),
            _ => format!("{} section", section),
        }
    }

    /// Check if package is essential
    pub fn is_essential(package: &PackageInfo) -> bool {
        package.priority == PackagePriority::Essential ||
        package.name.starts_with("base-") ||
        package.name == "busybox" ||
        package.name == "kernel" ||
        package.name == "libc"
    }

    /// Check if package is removable
    pub fn is_removable(package: &PackageInfo) -> bool {
        !Self::is_essential(package) && package.status == PackageStatus::Installed
    }

    /// Get package dependencies recursively
    pub fn get_recursive_dependencies(
        package: &PackageInfo,
        all_packages: &HashMap<String, PackageInfo>,
        visited: &mut std::collections::HashSet<String>,
    ) -> Vec<String> {
        let mut dependencies = Vec::new();
        
        if visited.contains(&package.name) {
            return dependencies;
        }
        
        visited.insert(package.name.clone());
        
        for dep in &package.depends {
            let dep_name = Self::parse_package_string(dep).unwrap_or_default().0;
            
            if let Some(dep_package) = all_packages.get(&dep_name) {
                dependencies.push(dep_name.clone());
                let sub_deps = Self::get_recursive_dependencies(dep_package, all_packages, visited);
                dependencies.extend(sub_deps);
            }
        }
        
        dependencies
    }

    /// Get packages that depend on this package
    pub fn get_reverse_dependencies(
        package_name: &str,
        all_packages: &HashMap<String, PackageInfo>,
    ) -> Vec<String> {
        let mut reverse_deps = Vec::new();
        
        for (name, pkg) in all_packages {
            for dep in &pkg.depends {
                let dep_name = Self::parse_package_string(dep).unwrap_or_default().0;
                if dep_name == package_name {
                    reverse_deps.push(name.clone());
                    break;
                }
            }
        }
        
        reverse_deps
    }

    /// Calculate total download size for packages
    pub fn calculate_download_size(packages: &[PackageInfo]) -> u64 {
        packages.iter().map(|p| p.download_size).sum()
    }

    /// Calculate total installed size for packages
    pub fn calculate_installed_size(packages: &[PackageInfo]) -> u64 {
        packages.iter().map(|p| p.installed_size).sum()
    }

    /// Filter packages by status
    pub fn filter_by_status(packages: &[PackageInfo], status: PackageStatus) -> Vec<PackageInfo> {
        packages.iter()
            .filter(|p| p.status == status)
            .cloned()
            .collect()
    }

    /// Sort packages by name
    pub fn sort_by_name(packages: &mut [PackageInfo]) {
        packages.sort_by(|a, b| a.name.cmp(&b.name));
    }

    /// Sort packages by size
    pub fn sort_by_size(packages: &mut [PackageInfo]) {
        packages.sort_by(|a, b| b.installed_size.cmp(&a.installed_size));
    }

    /// Sort packages by category
    pub fn sort_by_category(packages: &mut [PackageInfo]) {
        packages.sort_by(|a, b| {
            a.category.cmp(&b.category)
                .then_with(|| a.name.cmp(&b.name))
        });
    }

    /// Validate package name
    pub fn validate_package_name(name: &str) -> PackageResult<()> {
        if name.is_empty() {
            return Err(PackageError::ParseError("Package name cannot be empty".to_string()));
        }

        if name.len() > 255 {
            return Err(PackageError::ParseError("Package name too long".to_string()));
        }

        // Check for valid characters (alphanumeric, dash, underscore, dot, plus)
        if !name.chars().all(|c| c.is_alphanumeric() || c == '-' || c == '_' || c == '.' || c == '+') {
            return Err(PackageError::ParseError("Invalid characters in package name".to_string()));
        }

        Ok(())
    }

    /// Create package summary
    pub fn create_summary(package: &PackageInfo) -> String {
        format!(
            "{} ({})\n{}\nSize: {} installed, {} download\nStatus: {}",
            package.name,
            package.version,
            package.description,
            Self::format_size(package.installed_size),
            Self::format_size(package.download_size),
            package.status
        )
    }
}
