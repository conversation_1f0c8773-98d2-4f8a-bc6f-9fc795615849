//! Package Repository Management
//!
//! This module provides repository management functionality for OpenWrt package sources.

use crate::{PackageError, PackageResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Repository type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum RepositoryType {
    /// Source repository with gzipped package list
    SourceGz,
    /// Source repository with uncompressed package list
    Source,
    /// Destination directory
    Dest,
}

impl std::fmt::Display for RepositoryType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RepositoryType::SourceGz => write!(f, "src/gz"),
            RepositoryType::Source => write!(f, "src"),
            RepositoryType::Dest => write!(f, "dest"),
        }
    }
}

/// Repository information
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Repository {
    pub name: String,
    pub repo_type: RepositoryType,
    pub url: String,
    pub enabled: bool,
    pub priority: u32,
    pub description: Option<String>,
    pub architecture: Option<String>,
    pub gpg_key: Option<String>,
    pub last_update: Option<std::time::SystemTime>,
}

/// Repository manager
pub struct RepositoryManager {
    repositories: Vec<Repository>,
    config_file: String,
}

impl RepositoryManager {
    /// Create a new repository manager
    pub fn new() -> PackageResult<Self> {
        let config_file = "/etc/opkg.conf".to_string();
        let mut manager = Self {
            repositories: Vec::new(),
            config_file,
        };
        
        manager.load_repositories()?;
        Ok(manager)
    }

    /// Load repositories from configuration
    pub fn load_repositories(&mut self) -> PackageResult<()> {
        // Mock implementation - in real system would read from /etc/opkg.conf
        self.repositories = vec![
            Repository {
                name: "openwrt_core".to_string(),
                repo_type: RepositoryType::SourceGz,
                url: "https://downloads.openwrt.org/releases/23.05.0/targets/ath79/generic/packages".to_string(),
                enabled: true,
                priority: 1,
                description: Some("OpenWrt core packages".to_string()),
                architecture: Some("mips_24kc".to_string()),
                gpg_key: None,
                last_update: None,
            },
            Repository {
                name: "openwrt_base".to_string(),
                repo_type: RepositoryType::SourceGz,
                url: "https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/base".to_string(),
                enabled: true,
                priority: 2,
                description: Some("OpenWrt base system packages".to_string()),
                architecture: Some("mips_24kc".to_string()),
                gpg_key: None,
                last_update: None,
            },
            Repository {
                name: "openwrt_luci".to_string(),
                repo_type: RepositoryType::SourceGz,
                url: "https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/luci".to_string(),
                enabled: true,
                priority: 3,
                description: Some("OpenWrt LuCI web interface packages".to_string()),
                architecture: Some("mips_24kc".to_string()),
                gpg_key: None,
                last_update: None,
            },
            Repository {
                name: "openwrt_packages".to_string(),
                repo_type: RepositoryType::SourceGz,
                url: "https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/packages".to_string(),
                enabled: true,
                priority: 4,
                description: Some("OpenWrt additional packages".to_string()),
                architecture: Some("mips_24kc".to_string()),
                gpg_key: None,
                last_update: None,
            },
            Repository {
                name: "openwrt_routing".to_string(),
                repo_type: RepositoryType::SourceGz,
                url: "https://downloads.openwrt.org/releases/23.05.0/packages/mips_24kc/routing".to_string(),
                enabled: true,
                priority: 5,
                description: Some("OpenWrt routing packages".to_string()),
                architecture: Some("mips_24kc".to_string()),
                gpg_key: None,
                last_update: None,
            },
        ];
        
        Ok(())
    }

    /// Save repositories to configuration
    pub fn save_repositories(&self) -> PackageResult<()> {
        // Mock implementation - in real system would write to /etc/opkg.conf
        log::info!("Saving {} repositories to {}", self.repositories.len(), self.config_file);
        Ok(())
    }

    /// Get all repositories
    pub fn get_repositories(&self) -> &Vec<Repository> {
        &self.repositories
    }

    /// Get enabled repositories
    pub fn get_enabled_repositories(&self) -> Vec<&Repository> {
        self.repositories.iter()
            .filter(|repo| repo.enabled)
            .collect()
    }

    /// Get repository by name
    pub fn get_repository(&self, name: &str) -> Option<&Repository> {
        self.repositories.iter()
            .find(|repo| repo.name == name)
    }

    /// Add repository
    pub fn add_repository(&mut self, repository: Repository) -> PackageResult<()> {
        // Check if repository with same name already exists
        if self.repositories.iter().any(|repo| repo.name == repository.name) {
            return Err(PackageError::RepositoryError(
                format!("Repository '{}' already exists", repository.name)
            ));
        }

        self.repositories.push(repository);
        self.save_repositories()?;
        Ok(())
    }

    /// Remove repository
    pub fn remove_repository(&mut self, name: &str) -> PackageResult<()> {
        let initial_len = self.repositories.len();
        self.repositories.retain(|repo| repo.name != name);
        
        if self.repositories.len() == initial_len {
            return Err(PackageError::RepositoryError(
                format!("Repository '{}' not found", name)
            ));
        }

        self.save_repositories()?;
        Ok(())
    }

    /// Enable repository
    pub fn enable_repository(&mut self, name: &str) -> PackageResult<()> {
        if let Some(repo) = self.repositories.iter_mut().find(|repo| repo.name == name) {
            repo.enabled = true;
            self.save_repositories()?;
            Ok(())
        } else {
            Err(PackageError::RepositoryError(
                format!("Repository '{}' not found", name)
            ))
        }
    }

    /// Disable repository
    pub fn disable_repository(&mut self, name: &str) -> PackageResult<()> {
        if let Some(repo) = self.repositories.iter_mut().find(|repo| repo.name == name) {
            repo.enabled = false;
            self.save_repositories()?;
            Ok(())
        } else {
            Err(PackageError::RepositoryError(
                format!("Repository '{}' not found", name)
            ))
        }
    }

    /// Update repository priority
    pub fn set_repository_priority(&mut self, name: &str, priority: u32) -> PackageResult<()> {
        if let Some(repo) = self.repositories.iter_mut().find(|repo| repo.name == name) {
            repo.priority = priority;
            self.save_repositories()?;
            Ok(())
        } else {
            Err(PackageError::RepositoryError(
                format!("Repository '{}' not found", name)
            ))
        }
    }

    /// Sort repositories by priority
    pub fn sort_by_priority(&mut self) {
        self.repositories.sort_by_key(|repo| repo.priority);
    }

    /// Validate repository URL
    pub fn validate_url(url: &str) -> PackageResult<()> {
        if url.is_empty() {
            return Err(PackageError::RepositoryError("URL cannot be empty".to_string()));
        }

        if !url.starts_with("http://") && !url.starts_with("https://") && !url.starts_with("file://") {
            return Err(PackageError::RepositoryError("Invalid URL scheme".to_string()));
        }

        Ok(())
    }

    /// Test repository connectivity
    pub fn test_repository(&self, name: &str) -> PackageResult<bool> {
        let repo = self.get_repository(name)
            .ok_or_else(|| PackageError::RepositoryError(format!("Repository '{}' not found", name)))?;

        // Mock implementation - in real system would try to fetch package list
        log::info!("Testing connectivity to repository: {} ({})", repo.name, repo.url);
        
        // Simulate network test
        if repo.url.contains("downloads.openwrt.org") {
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Get repository statistics
    pub fn get_repository_stats(&self) -> RepositoryStats {
        let total = self.repositories.len();
        let enabled = self.repositories.iter().filter(|repo| repo.enabled).count();
        let disabled = total - enabled;

        let mut by_type = HashMap::new();
        for repo in &self.repositories {
            *by_type.entry(repo.repo_type.clone()).or_insert(0) += 1;
        }

        RepositoryStats {
            total_repositories: total,
            enabled_repositories: enabled,
            disabled_repositories: disabled,
            by_type,
        }
    }

    /// Create default OpenWrt repositories
    pub fn create_default_repositories(architecture: &str, version: &str) -> Vec<Repository> {
        vec![
            Repository {
                name: "openwrt_core".to_string(),
                repo_type: RepositoryType::SourceGz,
                url: format!("https://downloads.openwrt.org/releases/{}/targets/ath79/generic/packages", version),
                enabled: true,
                priority: 1,
                description: Some("OpenWrt core packages".to_string()),
                architecture: Some(architecture.to_string()),
                gpg_key: None,
                last_update: None,
            },
            Repository {
                name: "openwrt_base".to_string(),
                repo_type: RepositoryType::SourceGz,
                url: format!("https://downloads.openwrt.org/releases/{}/packages/{}/base", version, architecture),
                enabled: true,
                priority: 2,
                description: Some("OpenWrt base system packages".to_string()),
                architecture: Some(architecture.to_string()),
                gpg_key: None,
                last_update: None,
            },
            Repository {
                name: "openwrt_luci".to_string(),
                repo_type: RepositoryType::SourceGz,
                url: format!("https://downloads.openwrt.org/releases/{}/packages/{}/luci", version, architecture),
                enabled: true,
                priority: 3,
                description: Some("OpenWrt LuCI web interface packages".to_string()),
                architecture: Some(architecture.to_string()),
                gpg_key: None,
                last_update: None,
            },
            Repository {
                name: "openwrt_packages".to_string(),
                repo_type: RepositoryType::SourceGz,
                url: format!("https://downloads.openwrt.org/releases/{}/packages/{}/packages", version, architecture),
                enabled: true,
                priority: 4,
                description: Some("OpenWrt additional packages".to_string()),
                architecture: Some(architecture.to_string()),
                gpg_key: None,
                last_update: None,
            },
        ]
    }

    /// Export repository configuration
    pub fn export_config(&self) -> String {
        let mut config = String::new();
        
        for repo in &self.repositories {
            if repo.enabled {
                config.push_str(&format!("{} {} {}\n", repo.repo_type, repo.name, repo.url));
            }
        }
        
        config
    }

    /// Import repository configuration
    pub fn import_config(&mut self, config: &str) -> PackageResult<usize> {
        let mut imported = 0;
        
        for line in config.lines() {
            let line = line.trim();
            if line.is_empty() || line.starts_with('#') {
                continue;
            }
            
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 3 {
                let repo_type = match parts[0] {
                    "src/gz" => RepositoryType::SourceGz,
                    "src" => RepositoryType::Source,
                    "dest" => RepositoryType::Dest,
                    _ => continue,
                };
                
                let repository = Repository {
                    name: parts[1].to_string(),
                    repo_type,
                    url: parts[2].to_string(),
                    enabled: true,
                    priority: (imported + 1) as u32,
                    description: None,
                    architecture: None,
                    gpg_key: None,
                    last_update: None,
                };
                
                if self.add_repository(repository).is_ok() {
                    imported += 1;
                }
            }
        }
        
        Ok(imported)
    }
}

/// Repository statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RepositoryStats {
    pub total_repositories: usize,
    pub enabled_repositories: usize,
    pub disabled_repositories: usize,
    pub by_type: HashMap<RepositoryType, usize>,
}

impl Default for RepositoryManager {
    fn default() -> Self {
        Self::new().expect("Failed to create RepositoryManager")
    }
}
