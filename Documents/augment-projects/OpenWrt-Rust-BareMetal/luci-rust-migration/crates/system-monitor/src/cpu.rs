//! CPU Monitoring
//!
//! This module provides CPU usage monitoring and information gathering.

use crate::{MonitorError, MonitorResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant};

/// CPU information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuInfo {
    pub model: String,
    pub cores: u32,
    pub threads: u32,
    pub frequency: u64, // MHz
    pub cache_size: Option<u64>, // KB
    pub flags: Vec<String>,
    pub bogomips: Option<f64>,
}

/// CPU usage statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuUsage {
    pub overall: f64,
    pub per_core: Vec<f64>,
    pub user: f64,
    pub system: f64,
    pub idle: f64,
    pub iowait: f64,
    pub irq: f64,
    pub softirq: f64,
    pub steal: f64,
    pub guest: f64,
}

/// CPU statistics from /proc/stat
#[derive(Debug, <PERSON>lone)]
struct CpuStats {
    user: u64,
    nice: u64,
    system: u64,
    idle: u64,
    iowait: u64,
    irq: u64,
    softirq: u64,
    steal: u64,
    guest: u64,
    guest_nice: u64,
}

impl CpuStats {
    fn total(&self) -> u64 {
        self.user + self.nice + self.system + self.idle + self.iowait + 
        self.irq + self.softirq + self.steal + self.guest + self.guest_nice
    }

    fn active(&self) -> u64 {
        self.total() - self.idle - self.iowait
    }
}

/// CPU temperature information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuTemperature {
    pub current: f64, // Celsius
    pub critical: Option<f64>,
    pub max: Option<f64>,
}

/// CPU frequency information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuFrequency {
    pub current: u64, // MHz
    pub min: u64,
    pub max: u64,
    pub governor: String,
}

/// CPU monitor
pub struct CpuMonitor {
    previous_stats: Option<HashMap<String, CpuStats>>,
    last_update: Option<Instant>,
}

impl CpuMonitor {
    /// Create a new CPU monitor
    pub fn new() -> MonitorResult<Self> {
        Ok(Self {
            previous_stats: None,
            last_update: None,
        })
    }

    /// Get CPU information
    pub fn get_cpu_info(&self) -> MonitorResult<CpuInfo> {
        // Mock implementation - in real system would read from /proc/cpuinfo
        Ok(CpuInfo {
            model: "MIPS 24Kc V7.4".to_string(),
            cores: 1,
            threads: 1,
            frequency: 580,
            cache_size: Some(32),
            flags: vec!["mips16".to_string(), "mips32r2".to_string()],
            bogomips: Some(385.84),
        })
    }

    /// Get current CPU usage
    pub fn get_usage(&mut self) -> MonitorResult<CpuUsage> {
        let current_stats = self.read_cpu_stats()?;
        let now = Instant::now();

        let usage = if let (Some(prev_stats), Some(last_update)) = (&self.previous_stats, self.last_update) {
            self.calculate_usage(prev_stats, &current_stats)?
        } else {
            // First measurement, return zero usage
            CpuUsage {
                overall: 0.0,
                per_core: vec![0.0],
                user: 0.0,
                system: 0.0,
                idle: 100.0,
                iowait: 0.0,
                irq: 0.0,
                softirq: 0.0,
                steal: 0.0,
                guest: 0.0,
            }
        };

        self.previous_stats = Some(current_stats);
        self.last_update = Some(now);

        Ok(usage)
    }

    /// Get CPU temperature
    pub fn get_temperature(&self) -> MonitorResult<CpuTemperature> {
        // Mock implementation - in real system would read from thermal zones
        Ok(CpuTemperature {
            current: 45.0,
            critical: Some(85.0),
            max: Some(70.0),
        })
    }

    /// Get CPU frequency information
    pub fn get_frequency(&self) -> MonitorResult<CpuFrequency> {
        // Mock implementation - in real system would read from cpufreq
        Ok(CpuFrequency {
            current: 580,
            min: 200,
            max: 580,
            governor: "ondemand".to_string(),
        })
    }

    /// Read CPU statistics from /proc/stat
    fn read_cpu_stats(&self) -> MonitorResult<HashMap<String, CpuStats>> {
        // Mock implementation - in real system would read from /proc/stat
        let mut stats = HashMap::new();
        
        // Overall CPU stats
        stats.insert("cpu".to_string(), CpuStats {
            user: 12345,
            nice: 0,
            system: 2345,
            idle: 98765,
            iowait: 123,
            irq: 12,
            softirq: 34,
            steal: 0,
            guest: 0,
            guest_nice: 0,
        });

        // Per-core stats (single core in this mock)
        stats.insert("cpu0".to_string(), CpuStats {
            user: 12345,
            nice: 0,
            system: 2345,
            idle: 98765,
            iowait: 123,
            irq: 12,
            softirq: 34,
            steal: 0,
            guest: 0,
            guest_nice: 0,
        });

        Ok(stats)
    }

    /// Calculate CPU usage from statistics
    fn calculate_usage(&self, prev_stats: &HashMap<String, CpuStats>, current_stats: &HashMap<String, CpuStats>) -> MonitorResult<CpuUsage> {
        let prev_cpu = prev_stats.get("cpu").ok_or_else(|| MonitorError::InvalidFormat("Missing CPU stats".to_string()))?;
        let current_cpu = current_stats.get("cpu").ok_or_else(|| MonitorError::InvalidFormat("Missing CPU stats".to_string()))?;

        let total_diff = current_cpu.total() - prev_cpu.total();
        let active_diff = current_cpu.active() - prev_cpu.active();

        if total_diff == 0 {
            return Ok(CpuUsage {
                overall: 0.0,
                per_core: vec![0.0],
                user: 0.0,
                system: 0.0,
                idle: 100.0,
                iowait: 0.0,
                irq: 0.0,
                softirq: 0.0,
                steal: 0.0,
                guest: 0.0,
            });
        }

        let overall = (active_diff as f64 / total_diff as f64) * 100.0;
        let user = ((current_cpu.user - prev_cpu.user) as f64 / total_diff as f64) * 100.0;
        let system = ((current_cpu.system - prev_cpu.system) as f64 / total_diff as f64) * 100.0;
        let idle = ((current_cpu.idle - prev_cpu.idle) as f64 / total_diff as f64) * 100.0;
        let iowait = ((current_cpu.iowait - prev_cpu.iowait) as f64 / total_diff as f64) * 100.0;
        let irq = ((current_cpu.irq - prev_cpu.irq) as f64 / total_diff as f64) * 100.0;
        let softirq = ((current_cpu.softirq - prev_cpu.softirq) as f64 / total_diff as f64) * 100.0;
        let steal = ((current_cpu.steal - prev_cpu.steal) as f64 / total_diff as f64) * 100.0;
        let guest = ((current_cpu.guest - prev_cpu.guest) as f64 / total_diff as f64) * 100.0;

        // Calculate per-core usage
        let mut per_core = Vec::new();
        for i in 0..1 { // Single core in mock
            let core_name = format!("cpu{}", i);
            if let (Some(prev_core), Some(current_core)) = (prev_stats.get(&core_name), current_stats.get(&core_name)) {
                let core_total_diff = current_core.total() - prev_core.total();
                let core_active_diff = current_core.active() - prev_core.active();
                let core_usage = if core_total_diff > 0 {
                    (core_active_diff as f64 / core_total_diff as f64) * 100.0
                } else {
                    0.0
                };
                per_core.push(core_usage);
            } else {
                per_core.push(0.0);
            }
        }

        Ok(CpuUsage {
            overall,
            per_core,
            user,
            system,
            idle,
            iowait,
            irq,
            softirq,
            steal,
            guest,
        })
    }

    /// Get CPU load average
    pub fn get_load_average(&self) -> MonitorResult<(f64, f64, f64)> {
        // Mock implementation - in real system would read from /proc/loadavg
        Ok((0.5, 0.3, 0.2))
    }

    /// Get number of CPU cores
    pub fn get_core_count(&self) -> MonitorResult<u32> {
        let cpu_info = self.get_cpu_info()?;
        Ok(cpu_info.cores)
    }

    /// Check if CPU supports specific feature
    pub fn has_feature(&self, feature: &str) -> MonitorResult<bool> {
        let cpu_info = self.get_cpu_info()?;
        Ok(cpu_info.flags.contains(&feature.to_string()))
    }
}

impl Default for CpuMonitor {
    fn default() -> Self {
        Self::new().expect("Failed to create CpuMonitor")
    }
}
