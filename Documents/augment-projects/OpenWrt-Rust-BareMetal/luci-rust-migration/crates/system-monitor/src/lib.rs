//! System Monitoring for OpenWrt
//!
//! This crate provides comprehensive system monitoring functionality for OpenWrt systems,
//! including CPU, memory, storage, network, and process monitoring.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use thiserror::Error;
use luci_shared_types::*;

pub mod cpu;
pub mod memory;
pub mod storage;
pub mod network;
pub mod process;
pub mod system;
pub mod logs;

pub use cpu::*;
pub use memory::*;
pub use storage::*;
pub use network::*;
pub use process::*;
pub use system::*;
pub use logs::*;

/// System monitoring errors
#[derive(Error, Debug, Clone)]
pub enum MonitorError {
    #[error("Failed to read system information: {0}")]
    SystemReadError(String),
    #[error("Process not found: {0}")]
    ProcessNotFound(u32),
    #[error("Permission denied")]
    PermissionDenied,
    #[error("Invalid data format: {0}")]
    InvalidFormat(String),
    #[error("I/O error: {0}")]
    IoError(String),
    #[error("Parse error: {0}")]
    ParseError(String),
}

pub type MonitorResult<T> = Result<T, MonitorError>;

/// System load averages
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoadAverage {
    pub one_minute: f64,
    pub five_minutes: f64,
    pub fifteen_minutes: f64,
    pub running_processes: u32,
    pub total_processes: u32,
}

/// System uptime information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Uptime {
    pub uptime_seconds: u64,
    pub idle_seconds: u64,
    pub boot_time: SystemTime,
}

/// System information summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    pub hostname: String,
    pub kernel_version: String,
    pub architecture: String,
    pub cpu_model: String,
    pub cpu_cores: u32,
    pub total_memory: u64,
    pub uptime: Uptime,
    pub load_average: LoadAverage,
}

/// Real-time system metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemMetrics {
    pub timestamp: SystemTime,
    pub cpu_usage: CpuUsage,
    pub memory_usage: MemoryUsage,
    pub storage_usage: Vec<StorageUsage>,
    pub network_stats: Vec<NetworkInterfaceStats>,
    pub load_average: LoadAverage,
    pub process_count: u32,
}

/// System monitor manager
pub struct SystemMonitor {
    cpu_monitor: CpuMonitor,
    memory_monitor: MemoryMonitor,
    storage_monitor: StorageMonitor,
    network_monitor: NetworkMonitor,
    process_monitor: ProcessMonitor,
    log_monitor: LogMonitor,
    metrics_history: Vec<SystemMetrics>,
    max_history_size: usize,
}

impl SystemMonitor {
    /// Create a new system monitor
    pub fn new() -> MonitorResult<Self> {
        Ok(Self {
            cpu_monitor: CpuMonitor::new()?,
            memory_monitor: MemoryMonitor::new()?,
            storage_monitor: StorageMonitor::new()?,
            network_monitor: NetworkMonitor::new()?,
            process_monitor: ProcessMonitor::new()?,
            log_monitor: LogMonitor::new()?,
            metrics_history: Vec::new(),
            max_history_size: 1000, // Keep last 1000 measurements
        })
    }

    /// Get current system information
    pub fn get_system_info(&mut self) -> MonitorResult<SystemInfo> {
        let hostname = self.get_hostname()?;
        let kernel_version = self.get_kernel_version()?;
        let architecture = self.get_architecture()?;
        let cpu_info = self.cpu_monitor.get_cpu_info()?;
        let memory_info = self.memory_monitor.get_memory_info()?;
        let uptime = self.get_uptime()?;
        let load_average = self.get_load_average()?;

        Ok(SystemInfo {
            hostname,
            kernel_version,
            architecture,
            cpu_model: cpu_info.model,
            cpu_cores: cpu_info.cores,
            total_memory: memory_info.total,
            uptime,
            load_average,
        })
    }

    /// Get current system metrics
    pub fn get_current_metrics(&mut self) -> MonitorResult<SystemMetrics> {
        let timestamp = SystemTime::now();
        let cpu_usage = self.cpu_monitor.get_usage()?;
        let memory_usage = self.memory_monitor.get_usage()?;
        let storage_usage = self.storage_monitor.get_all_usage()?;
        let network_stats = self.network_monitor.get_all_stats()?;
        let load_average = self.get_load_average()?;
        let process_count = self.process_monitor.get_process_count()?;

        let metrics = SystemMetrics {
            timestamp,
            cpu_usage,
            memory_usage,
            storage_usage,
            network_stats,
            load_average,
            process_count,
        };

        // Add to history
        self.metrics_history.push(metrics.clone());
        if self.metrics_history.len() > self.max_history_size {
            self.metrics_history.remove(0);
        }

        Ok(metrics)
    }

    /// Get metrics history
    pub fn get_metrics_history(&self, duration: Duration) -> Vec<&SystemMetrics> {
        let cutoff_time = SystemTime::now() - duration;

        self.metrics_history.iter()
            .filter(|metrics| metrics.timestamp >= cutoff_time)
            .collect()
    }

    /// Get hostname
    fn get_hostname(&self) -> MonitorResult<String> {
        // Mock implementation - in real system would read from /proc/sys/kernel/hostname
        Ok("OpenWrt".to_string())
    }

    /// Get kernel version
    fn get_kernel_version(&self) -> MonitorResult<String> {
        // Mock implementation - in real system would read from /proc/version
        Ok("Linux 5.15.0".to_string())
    }

    /// Get system architecture
    fn get_architecture(&self) -> MonitorResult<String> {
        // Mock implementation - in real system would use uname
        Ok("mips".to_string())
    }

    /// Get system uptime
    fn get_uptime(&self) -> MonitorResult<Uptime> {
        // Mock implementation - in real system would read from /proc/uptime
        let uptime_seconds = 86400; // 1 day
        let idle_seconds = 43200; // 12 hours
        let boot_time = SystemTime::now() - Duration::from_secs(uptime_seconds);

        Ok(Uptime {
            uptime_seconds,
            idle_seconds,
            boot_time,
        })
    }

    /// Get load average
    fn get_load_average(&self) -> MonitorResult<LoadAverage> {
        // Mock implementation - in real system would read from /proc/loadavg
        Ok(LoadAverage {
            one_minute: 0.5,
            five_minutes: 0.3,
            fifteen_minutes: 0.2,
            running_processes: 2,
            total_processes: 45,
        })
    }

    /// Start continuous monitoring
    pub fn start_monitoring(&mut self, interval: Duration) -> MonitorResult<()> {
        // In real implementation, this would start a background thread
        // that collects metrics at regular intervals
        log::info!("Starting system monitoring with interval: {:?}", interval);
        Ok(())
    }

    /// Stop continuous monitoring
    pub fn stop_monitoring(&mut self) -> MonitorResult<()> {
        log::info!("Stopping system monitoring");
        Ok(())
    }

    /// Get CPU monitor
    pub fn cpu_monitor(&mut self) -> &mut CpuMonitor {
        &mut self.cpu_monitor
    }

    /// Get memory monitor
    pub fn memory_monitor(&mut self) -> &mut MemoryMonitor {
        &mut self.memory_monitor
    }

    /// Get storage monitor
    pub fn storage_monitor(&mut self) -> &mut StorageMonitor {
        &mut self.storage_monitor
    }

    /// Get network monitor
    pub fn network_monitor(&mut self) -> &mut NetworkMonitor {
        &mut self.network_monitor
    }

    /// Get process monitor
    pub fn process_monitor(&mut self) -> &mut ProcessMonitor {
        &mut self.process_monitor
    }

    /// Get log monitor
    pub fn log_monitor(&mut self) -> &mut LogMonitor {
        &mut self.log_monitor
    }

    /// Clear metrics history
    pub fn clear_history(&mut self) {
        self.metrics_history.clear();
    }

    /// Set maximum history size
    pub fn set_max_history_size(&mut self, size: usize) {
        self.max_history_size = size;
        if self.metrics_history.len() > size {
            let excess = self.metrics_history.len() - size;
            self.metrics_history.drain(0..excess);
        }
    }
}

impl Default for SystemMonitor {
    fn default() -> Self {
        Self::new().expect("Failed to create SystemMonitor")
    }
}
