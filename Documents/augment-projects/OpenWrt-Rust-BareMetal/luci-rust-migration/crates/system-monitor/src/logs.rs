//! Log Monitoring
//!
//! This module provides system log monitoring and analysis functionality.

use crate::{MonitorError, MonitorResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

/// Log level
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum LogLevel {
    Emergency = 0,
    Alert = 1,
    Critical = 2,
    Error = 3,
    Warning = 4,
    Notice = 5,
    Info = 6,
    Debug = 7,
}

impl std::fmt::Display for LogLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            LogLevel::Emergency => write!(f, "EMERG"),
            LogLevel::Alert => write!(f, "ALERT"),
            LogLevel::Critical => write!(f, "CRIT"),
            LogLevel::Error => write!(f, "ERR"),
            LogLevel::Warning => write!(f, "WARNING"),
            LogLevel::Notice => write!(f, "NOTICE"),
            LogLevel::Info => write!(f, "INFO"),
            LogLevel::Debug => write!(f, "DEBUG"),
        }
    }
}

impl From<u8> for LogLevel {
    fn from(level: u8) -> Self {
        match level {
            0 => LogLevel::Emergency,
            1 => LogLevel::Alert,
            2 => LogLevel::Critical,
            3 => LogLevel::Error,
            4 => LogLevel::Warning,
            5 => LogLevel::Notice,
            6 => LogLevel::Info,
            7 => LogLevel::Debug,
            _ => LogLevel::Info,
        }
    }
}

/// Log facility
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum LogFacility {
    Kernel,
    User,
    Mail,
    Daemon,
    Auth,
    Syslog,
    Lpr,
    News,
    Uucp,
    Cron,
    Authpriv,
    Ftp,
    Local0,
    Local1,
    Local2,
    Local3,
    Local4,
    Local5,
    Local6,
    Local7,
}

impl std::fmt::Display for LogFacility {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            LogFacility::Kernel => write!(f, "kern"),
            LogFacility::User => write!(f, "user"),
            LogFacility::Mail => write!(f, "mail"),
            LogFacility::Daemon => write!(f, "daemon"),
            LogFacility::Auth => write!(f, "auth"),
            LogFacility::Syslog => write!(f, "syslog"),
            LogFacility::Lpr => write!(f, "lpr"),
            LogFacility::News => write!(f, "news"),
            LogFacility::Uucp => write!(f, "uucp"),
            LogFacility::Cron => write!(f, "cron"),
            LogFacility::Authpriv => write!(f, "authpriv"),
            LogFacility::Ftp => write!(f, "ftp"),
            LogFacility::Local0 => write!(f, "local0"),
            LogFacility::Local1 => write!(f, "local1"),
            LogFacility::Local2 => write!(f, "local2"),
            LogFacility::Local3 => write!(f, "local3"),
            LogFacility::Local4 => write!(f, "local4"),
            LogFacility::Local5 => write!(f, "local5"),
            LogFacility::Local6 => write!(f, "local6"),
            LogFacility::Local7 => write!(f, "local7"),
        }
    }
}

/// Log entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub timestamp: SystemTime,
    pub facility: LogFacility,
    pub level: LogLevel,
    pub hostname: String,
    pub process: String,
    pub pid: Option<u32>,
    pub message: String,
}

/// Log filter criteria
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogFilter {
    pub facility: Option<LogFacility>,
    pub level: Option<LogLevel>,
    pub process: Option<String>,
    pub message_contains: Option<String>,
    pub since: Option<SystemTime>,
    pub until: Option<SystemTime>,
}

impl Default for LogFilter {
    fn default() -> Self {
        Self {
            facility: None,
            level: None,
            process: None,
            message_contains: None,
            since: None,
            until: None,
        }
    }
}

/// Log statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogStats {
    pub total_entries: usize,
    pub by_level: HashMap<LogLevel, usize>,
    pub by_facility: HashMap<LogFacility, usize>,
    pub by_process: HashMap<String, usize>,
    pub error_count: usize,
    pub warning_count: usize,
}

/// Log monitor
pub struct LogMonitor {
    log_entries: Vec<LogEntry>,
    max_entries: usize,
}

impl LogMonitor {
    /// Create a new log monitor
    pub fn new() -> MonitorResult<Self> {
        Ok(Self {
            log_entries: Vec::new(),
            max_entries: 10000, // Keep last 10000 log entries
        })
    }

    /// Load system logs
    pub fn load_logs(&mut self) -> MonitorResult<()> {
        // Mock implementation - in real system would read from syslog, dmesg, logread
        let mock_logs = vec![
            LogEntry {
                timestamp: SystemTime::now() - std::time::Duration::from_secs(3600),
                facility: LogFacility::Kernel,
                level: LogLevel::Info,
                hostname: "OpenWrt".to_string(),
                process: "kernel".to_string(),
                pid: None,
                message: "System boot completed".to_string(),
            },
            LogEntry {
                timestamp: SystemTime::now() - std::time::Duration::from_secs(3000),
                facility: LogFacility::Daemon,
                level: LogLevel::Info,
                hostname: "OpenWrt".to_string(),
                process: "dropbear".to_string(),
                pid: Some(1234),
                message: "SSH connection from *************".to_string(),
            },
            LogEntry {
                timestamp: SystemTime::now() - std::time::Duration::from_secs(2400),
                facility: LogFacility::Daemon,
                level: LogLevel::Warning,
                hostname: "OpenWrt".to_string(),
                process: "dnsmasq".to_string(),
                pid: Some(5678),
                message: "DNS query timeout for example.com".to_string(),
            },
            LogEntry {
                timestamp: SystemTime::now() - std::time::Duration::from_secs(1800),
                facility: LogFacility::Auth,
                level: LogLevel::Error,
                hostname: "OpenWrt".to_string(),
                process: "dropbear".to_string(),
                pid: Some(1234),
                message: "Failed login attempt from *************".to_string(),
            },
            LogEntry {
                timestamp: SystemTime::now() - std::time::Duration::from_secs(1200),
                facility: LogFacility::Daemon,
                level: LogLevel::Info,
                hostname: "OpenWrt".to_string(),
                process: "uhttpd".to_string(),
                pid: Some(9012),
                message: "HTTP request: GET /cgi-bin/luci".to_string(),
            },
        ];

        self.log_entries = mock_logs;
        Ok(())
    }

    /// Get all log entries
    pub fn get_logs(&mut self) -> MonitorResult<&Vec<LogEntry>> {
        self.load_logs()?;
        Ok(&self.log_entries)
    }

    /// Get filtered log entries
    pub fn get_filtered_logs(&mut self, filter: &LogFilter) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;
        
        let filtered: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| self.matches_filter(entry, filter))
            .cloned()
            .collect();

        Ok(filtered)
    }

    /// Check if log entry matches filter
    fn matches_filter(&self, entry: &LogEntry, filter: &LogFilter) -> bool {
        if let Some(ref facility) = filter.facility {
            if entry.facility != *facility {
                return false;
            }
        }

        if let Some(ref level) = filter.level {
            if entry.level > *level {
                return false;
            }
        }

        if let Some(ref process) = filter.process {
            if !entry.process.contains(process) {
                return false;
            }
        }

        if let Some(ref message_contains) = filter.message_contains {
            if !entry.message.to_lowercase().contains(&message_contains.to_lowercase()) {
                return false;
            }
        }

        if let Some(since) = filter.since {
            if entry.timestamp < since {
                return false;
            }
        }

        if let Some(until) = filter.until {
            if entry.timestamp > until {
                return false;
            }
        }

        true
    }

    /// Get log statistics
    pub fn get_log_stats(&mut self) -> MonitorResult<LogStats> {
        self.load_logs()?;
        
        let mut stats = LogStats {
            total_entries: self.log_entries.len(),
            by_level: HashMap::new(),
            by_facility: HashMap::new(),
            by_process: HashMap::new(),
            error_count: 0,
            warning_count: 0,
        };

        for entry in &self.log_entries {
            // Count by level
            *stats.by_level.entry(entry.level.clone()).or_insert(0) += 1;
            
            // Count by facility
            *stats.by_facility.entry(entry.facility.clone()).or_insert(0) += 1;
            
            // Count by process
            *stats.by_process.entry(entry.process.clone()).or_insert(0) += 1;

            // Count errors and warnings
            match entry.level {
                LogLevel::Emergency | LogLevel::Alert | LogLevel::Critical | LogLevel::Error => {
                    stats.error_count += 1;
                }
                LogLevel::Warning => {
                    stats.warning_count += 1;
                }
                _ => {}
            }
        }

        Ok(stats)
    }

    /// Get recent errors
    pub fn get_recent_errors(&mut self, limit: usize) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;
        
        let mut errors: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| matches!(entry.level, LogLevel::Emergency | LogLevel::Alert | LogLevel::Critical | LogLevel::Error))
            .cloned()
            .collect();

        errors.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        errors.truncate(limit);
        
        Ok(errors)
    }

    /// Get recent warnings
    pub fn get_recent_warnings(&mut self, limit: usize) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;
        
        let mut warnings: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| entry.level == LogLevel::Warning)
            .cloned()
            .collect();

        warnings.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        warnings.truncate(limit);
        
        Ok(warnings)
    }

    /// Search logs
    pub fn search_logs(&mut self, query: &str, limit: usize) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;
        
        let query_lower = query.to_lowercase();
        let mut results: Vec<LogEntry> = self.log_entries.iter()
            .filter(|entry| {
                entry.message.to_lowercase().contains(&query_lower) ||
                entry.process.to_lowercase().contains(&query_lower)
            })
            .cloned()
            .collect();

        results.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        results.truncate(limit);
        
        Ok(results)
    }

    /// Format log entry
    pub fn format_log_entry(entry: &LogEntry) -> String {
        let timestamp = entry.timestamp
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        let pid_str = entry.pid
            .map(|p| format!("[{}]", p))
            .unwrap_or_default();

        format!(
            "{} {} {}.{}: {}{}: {}",
            timestamp,
            entry.hostname,
            entry.facility,
            entry.level,
            entry.process,
            pid_str,
            entry.message
        )
    }

    /// Get log tail (most recent entries)
    pub fn get_log_tail(&mut self, lines: usize) -> MonitorResult<Vec<LogEntry>> {
        self.load_logs()?;
        
        let mut entries = self.log_entries.clone();
        entries.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        entries.truncate(lines);
        
        Ok(entries)
    }

    /// Clear old log entries
    pub fn clear_old_logs(&mut self, keep_duration: std::time::Duration) -> MonitorResult<usize> {
        let cutoff_time = SystemTime::now() - keep_duration;
        let initial_count = self.log_entries.len();
        
        self.log_entries.retain(|entry| entry.timestamp >= cutoff_time);
        
        Ok(initial_count - self.log_entries.len())
    }

    /// Set maximum log entries to keep
    pub fn set_max_entries(&mut self, max_entries: usize) {
        self.max_entries = max_entries;
        if self.log_entries.len() > max_entries {
            let excess = self.log_entries.len() - max_entries;
            self.log_entries.drain(0..excess);
        }
    }

    /// Export logs to string
    pub fn export_logs(&mut self, filter: Option<&LogFilter>) -> MonitorResult<String> {
        let entries = if let Some(filter) = filter {
            self.get_filtered_logs(filter)?
        } else {
            self.get_logs()?.clone()
        };

        let mut output = String::new();
        for entry in entries {
            output.push_str(&Self::format_log_entry(&entry));
            output.push('\n');
        }

        Ok(output)
    }
}

impl Default for LogMonitor {
    fn default() -> Self {
        Self::new().expect("Failed to create LogMonitor")
    }
}
