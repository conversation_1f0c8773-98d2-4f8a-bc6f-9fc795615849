//! Memory Monitoring
//!
//! This module provides memory usage monitoring and information gathering.

use crate::{Monitor<PERSON>rro<PERSON>, MonitorResult};
use serde::{Deserialize, Serialize};

/// Memory information
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MemoryInfo {
    pub total: u64,        // Total memory in bytes
    pub available: u64,    // Available memory in bytes
    pub used: u64,         // Used memory in bytes
    pub free: u64,         // Free memory in bytes
    pub buffers: u64,      // Buffer memory in bytes
    pub cached: u64,       // Cached memory in bytes
    pub shared: u64,       // Shared memory in bytes
    pub slab: u64,         // Slab memory in bytes
}

/// Memory usage statistics
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MemoryUsage {
    pub total: u64,
    pub used: u64,
    pub free: u64,
    pub available: u64,
    pub usage_percent: f64,
    pub buffers: u64,
    pub cached: u64,
    pub shared: u64,
    pub swap_total: u64,
    pub swap_used: u64,
    pub swap_free: u64,
    pub swap_usage_percent: f64,
}

/// Memory statistics from /proc/meminfo
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
struct MemInfoStats {
    mem_total: u64,
    mem_free: u64,
    mem_available: u64,
    buffers: u64,
    cached: u64,
    s_reclaimable: u64,
    shmem: u64,
    swap_total: u64,
    swap_free: u64,
}

/// Memory monitor
pub struct MemoryMonitor;

impl MemoryMonitor {
    /// Create a new memory monitor
    pub fn new() -> MonitorResult<Self> {
        Ok(Self)
    }

    /// Get memory information
    pub fn get_memory_info(&self) -> MonitorResult<MemoryInfo> {
        let stats = self.read_meminfo()?;
        
        let used = stats.mem_total - stats.mem_free - stats.buffers - stats.cached;
        
        Ok(MemoryInfo {
            total: stats.mem_total,
            available: stats.mem_available,
            used,
            free: stats.mem_free,
            buffers: stats.buffers,
            cached: stats.cached,
            shared: stats.shmem,
            slab: stats.s_reclaimable,
        })
    }

    /// Get current memory usage
    pub fn get_usage(&self) -> MonitorResult<MemoryUsage> {
        let stats = self.read_meminfo()?;
        
        let used = stats.mem_total - stats.mem_free - stats.buffers - stats.cached;
        let usage_percent = if stats.mem_total > 0 {
            (used as f64 / stats.mem_total as f64) * 100.0
        } else {
            0.0
        };

        let swap_used = stats.swap_total - stats.swap_free;
        let swap_usage_percent = if stats.swap_total > 0 {
            (swap_used as f64 / stats.swap_total as f64) * 100.0
        } else {
            0.0
        };

        Ok(MemoryUsage {
            total: stats.mem_total,
            used,
            free: stats.mem_free,
            available: stats.mem_available,
            usage_percent,
            buffers: stats.buffers,
            cached: stats.cached,
            shared: stats.shmem,
            swap_total: stats.swap_total,
            swap_used,
            swap_free: stats.swap_free,
            swap_usage_percent,
        })
    }

    /// Read memory information from /proc/meminfo
    fn read_meminfo(&self) -> MonitorResult<MemInfoStats> {
        // Mock implementation - in real system would read from /proc/meminfo
        // Values in KB, converted to bytes
        Ok(MemInfoStats {
            mem_total: 128 * 1024 * 1024,      // 128 MB
            mem_free: 64 * 1024 * 1024,        // 64 MB
            mem_available: 96 * 1024 * 1024,   // 96 MB
            buffers: 8 * 1024 * 1024,          // 8 MB
            cached: 24 * 1024 * 1024,          // 24 MB
            s_reclaimable: 4 * 1024 * 1024,    // 4 MB
            shmem: 2 * 1024 * 1024,            // 2 MB
            swap_total: 0,                      // No swap
            swap_free: 0,
        })
    }

    /// Get memory usage by category
    pub fn get_usage_by_category(&self) -> MonitorResult<Vec<(String, u64, f64)>> {
        let usage = self.get_usage()?;
        let total = usage.total as f64;
        
        let mut categories = Vec::new();
        
        categories.push(("Used".to_string(), usage.used, (usage.used as f64 / total) * 100.0));
        categories.push(("Buffers".to_string(), usage.buffers, (usage.buffers as f64 / total) * 100.0));
        categories.push(("Cached".to_string(), usage.cached, (usage.cached as f64 / total) * 100.0));
        categories.push(("Free".to_string(), usage.free, (usage.free as f64 / total) * 100.0));
        
        Ok(categories)
    }

    /// Check if memory usage is critical
    pub fn is_memory_critical(&self, threshold: f64) -> MonitorResult<bool> {
        let usage = self.get_usage()?;
        Ok(usage.usage_percent > threshold)
    }

    /// Get available memory in human readable format
    pub fn get_available_formatted(&self) -> MonitorResult<String> {
        let usage = self.get_usage()?;
        Ok(Self::format_bytes(usage.available))
    }

    /// Get used memory in human readable format
    pub fn get_used_formatted(&self) -> MonitorResult<String> {
        let usage = self.get_usage()?;
        Ok(Self::format_bytes(usage.used))
    }

    /// Get total memory in human readable format
    pub fn get_total_formatted(&self) -> MonitorResult<String> {
        let usage = self.get_usage()?;
        Ok(Self::format_bytes(usage.total))
    }

    /// Format bytes in human readable format
    fn format_bytes(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        if unit_index == 0 {
            format!("{} {}", bytes, UNITS[unit_index])
        } else {
            format!("{:.1} {}", size, UNITS[unit_index])
        }
    }

    /// Get memory pressure information
    pub fn get_memory_pressure(&self) -> MonitorResult<f64> {
        let usage = self.get_usage()?;
        
        // Calculate memory pressure based on available memory
        let pressure = if usage.total > 0 {
            let available_ratio = usage.available as f64 / usage.total as f64;
            (1.0 - available_ratio) * 100.0
        } else {
            0.0
        };

        Ok(pressure.max(0.0).min(100.0))
    }

    /// Get swap usage information
    pub fn get_swap_info(&self) -> MonitorResult<Option<(u64, u64, f64)>> {
        let usage = self.get_usage()?;
        
        if usage.swap_total > 0 {
            Ok(Some((usage.swap_total, usage.swap_used, usage.swap_usage_percent)))
        } else {
            Ok(None)
        }
    }

    /// Check if system has swap enabled
    pub fn has_swap(&self) -> MonitorResult<bool> {
        let usage = self.get_usage()?;
        Ok(usage.swap_total > 0)
    }

    /// Get memory statistics for monitoring
    pub fn get_memory_stats(&self) -> MonitorResult<Vec<(String, u64)>> {
        let info = self.get_memory_info()?;
        
        Ok(vec![
            ("Total".to_string(), info.total),
            ("Used".to_string(), info.used),
            ("Free".to_string(), info.free),
            ("Available".to_string(), info.available),
            ("Buffers".to_string(), info.buffers),
            ("Cached".to_string(), info.cached),
            ("Shared".to_string(), info.shared),
            ("Slab".to_string(), info.slab),
        ])
    }

    /// Get memory usage trend (mock implementation)
    pub fn get_usage_trend(&self) -> MonitorResult<Vec<f64>> {
        // Mock implementation - in real system would track usage over time
        Ok(vec![45.2, 46.1, 44.8, 47.3, 48.1, 46.9, 45.5])
    }
}

impl Default for MemoryMonitor {
    fn default() -> Self {
        Self::new().expect("Failed to create MemoryMonitor")
    }
}
