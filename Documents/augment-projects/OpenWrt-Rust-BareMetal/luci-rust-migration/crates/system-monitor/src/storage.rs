//! Storage Monitoring
//!
//! This module provides storage usage monitoring and disk information gathering.

use crate::{MonitorError, MonitorResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Storage device information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StorageDevice {
    pub name: String,
    pub device_path: String,
    pub mount_point: String,
    pub filesystem: String,
    pub total_space: u64,
    pub used_space: u64,
    pub available_space: u64,
    pub usage_percent: f64,
    pub inodes_total: u64,
    pub inodes_used: u64,
    pub inodes_available: u64,
    pub inodes_usage_percent: f64,
}

/// Storage usage summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageUsage {
    pub mount_point: String,
    pub filesystem: String,
    pub total: u64,
    pub used: u64,
    pub available: u64,
    pub usage_percent: f64,
}

/// Disk I/O statistics
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DiskIoStats {
    pub device: String,
    pub reads_completed: u64,
    pub reads_merged: u64,
    pub sectors_read: u64,
    pub time_reading: u64,
    pub writes_completed: u64,
    pub writes_merged: u64,
    pub sectors_written: u64,
    pub time_writing: u64,
    pub io_in_progress: u64,
    pub time_io: u64,
    pub weighted_time_io: u64,
}

/// Storage monitor
pub struct StorageMonitor {
    mounted_filesystems: HashMap<String, StorageDevice>,
}

impl StorageMonitor {
    /// Create a new storage monitor
    pub fn new() -> MonitorResult<Self> {
        let mut monitor = Self {
            mounted_filesystems: HashMap::new(),
        };
        
        monitor.refresh_filesystems()?;
        Ok(monitor)
    }

    /// Refresh mounted filesystems information
    pub fn refresh_filesystems(&mut self) -> MonitorResult<()> {
        self.mounted_filesystems.clear();
        
        // Mock implementation - in real system would read from /proc/mounts and use statvfs
        let filesystems = vec![
            StorageDevice {
                name: "rootfs".to_string(),
                device_path: "/dev/root".to_string(),
                mount_point: "/".to_string(),
                filesystem: "squashfs".to_string(),
                total_space: 16 * 1024 * 1024,      // 16 MB
                used_space: 12 * 1024 * 1024,       // 12 MB
                available_space: 4 * 1024 * 1024,   // 4 MB
                usage_percent: 75.0,
                inodes_total: 1000,
                inodes_used: 750,
                inodes_available: 250,
                inodes_usage_percent: 75.0,
            },
            StorageDevice {
                name: "tmpfs".to_string(),
                device_path: "tmpfs".to_string(),
                mount_point: "/tmp".to_string(),
                filesystem: "tmpfs".to_string(),
                total_space: 64 * 1024 * 1024,      // 64 MB
                used_space: 8 * 1024 * 1024,        // 8 MB
                available_space: 56 * 1024 * 1024,  // 56 MB
                usage_percent: 12.5,
                inodes_total: 16384,
                inodes_used: 100,
                inodes_available: 16284,
                inodes_usage_percent: 0.6,
            },
            StorageDevice {
                name: "overlay".to_string(),
                device_path: "/dev/mtdblock3".to_string(),
                mount_point: "/overlay".to_string(),
                filesystem: "jffs2".to_string(),
                total_space: 8 * 1024 * 1024,       // 8 MB
                used_space: 2 * 1024 * 1024,        // 2 MB
                available_space: 6 * 1024 * 1024,   // 6 MB
                usage_percent: 25.0,
                inodes_total: 2048,
                inodes_used: 150,
                inodes_available: 1898,
                inodes_usage_percent: 7.3,
            },
        ];

        for fs in filesystems {
            self.mounted_filesystems.insert(fs.mount_point.clone(), fs);
        }

        Ok(())
    }

    /// Get storage usage for a specific mount point
    pub fn get_usage(&self, mount_point: &str) -> MonitorResult<StorageUsage> {
        let device = self.mounted_filesystems.get(mount_point)
            .ok_or_else(|| MonitorError::InvalidFormat(format!("Mount point {} not found", mount_point)))?;

        Ok(StorageUsage {
            mount_point: device.mount_point.clone(),
            filesystem: device.filesystem.clone(),
            total: device.total_space,
            used: device.used_space,
            available: device.available_space,
            usage_percent: device.usage_percent,
        })
    }

    /// Get storage usage for all mounted filesystems
    pub fn get_all_usage(&self) -> MonitorResult<Vec<StorageUsage>> {
        let mut usage_list = Vec::new();
        
        for device in self.mounted_filesystems.values() {
            usage_list.push(StorageUsage {
                mount_point: device.mount_point.clone(),
                filesystem: device.filesystem.clone(),
                total: device.total_space,
                used: device.used_space,
                available: device.available_space,
                usage_percent: device.usage_percent,
            });
        }

        // Sort by mount point
        usage_list.sort_by(|a, b| a.mount_point.cmp(&b.mount_point));
        Ok(usage_list)
    }

    /// Get detailed information for a storage device
    pub fn get_device_info(&self, mount_point: &str) -> MonitorResult<&StorageDevice> {
        self.mounted_filesystems.get(mount_point)
            .ok_or_else(|| MonitorError::InvalidFormat(format!("Mount point {} not found", mount_point)))
    }

    /// Get all storage devices
    pub fn get_all_devices(&self) -> Vec<&StorageDevice> {
        self.mounted_filesystems.values().collect()
    }

    /// Check if storage usage is critical for any filesystem
    pub fn is_storage_critical(&self, threshold: f64) -> MonitorResult<Vec<String>> {
        let mut critical_mounts = Vec::new();
        
        for device in self.mounted_filesystems.values() {
            if device.usage_percent > threshold {
                critical_mounts.push(device.mount_point.clone());
            }
        }

        Ok(critical_mounts)
    }

    /// Get disk I/O statistics
    pub fn get_disk_io_stats(&self) -> MonitorResult<Vec<DiskIoStats>> {
        // Mock implementation - in real system would read from /proc/diskstats
        Ok(vec![
            DiskIoStats {
                device: "mtdblock0".to_string(),
                reads_completed: 1234,
                reads_merged: 0,
                sectors_read: 9876,
                time_reading: 5000,
                writes_completed: 567,
                writes_merged: 0,
                sectors_written: 4532,
                time_writing: 2000,
                io_in_progress: 0,
                time_io: 7000,
                weighted_time_io: 7000,
            },
            DiskIoStats {
                device: "mtdblock3".to_string(),
                reads_completed: 456,
                reads_merged: 0,
                sectors_read: 3648,
                time_reading: 1500,
                writes_completed: 789,
                writes_merged: 0,
                sectors_written: 6312,
                time_writing: 3000,
                io_in_progress: 0,
                time_io: 4500,
                weighted_time_io: 4500,
            },
        ])
    }

    /// Get total storage usage across all filesystems
    pub fn get_total_usage(&self) -> MonitorResult<(u64, u64, u64, f64)> {
        let mut total_space = 0;
        let mut total_used = 0;
        let mut total_available = 0;

        for device in self.mounted_filesystems.values() {
            // Skip tmpfs and other virtual filesystems for total calculation
            if device.filesystem != "tmpfs" && device.filesystem != "devtmpfs" {
                total_space += device.total_space;
                total_used += device.used_space;
                total_available += device.available_space;
            }
        }

        let usage_percent = if total_space > 0 {
            (total_used as f64 / total_space as f64) * 100.0
        } else {
            0.0
        };

        Ok((total_space, total_used, total_available, usage_percent))
    }

    /// Format bytes in human readable format
    pub fn format_bytes(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        if unit_index == 0 {
            format!("{} {}", bytes, UNITS[unit_index])
        } else {
            format!("{:.1} {}", size, UNITS[unit_index])
        }
    }

    /// Get storage usage summary
    pub fn get_usage_summary(&self) -> MonitorResult<Vec<(String, String, String, f64)>> {
        let mut summary = Vec::new();
        
        for device in self.mounted_filesystems.values() {
            summary.push((
                device.mount_point.clone(),
                Self::format_bytes(device.used_space),
                Self::format_bytes(device.total_space),
                device.usage_percent,
            ));
        }

        summary.sort_by(|a, b| a.0.cmp(&b.0));
        Ok(summary)
    }

    /// Check filesystem health
    pub fn check_filesystem_health(&self) -> MonitorResult<Vec<(String, bool, String)>> {
        let mut health_status = Vec::new();
        
        for device in self.mounted_filesystems.values() {
            let is_healthy = device.usage_percent < 90.0 && device.inodes_usage_percent < 90.0;
            let status = if is_healthy {
                "Healthy".to_string()
            } else if device.usage_percent >= 95.0 || device.inodes_usage_percent >= 95.0 {
                "Critical".to_string()
            } else {
                "Warning".to_string()
            };
            
            health_status.push((device.mount_point.clone(), is_healthy, status));
        }

        Ok(health_status)
    }
}

impl Default for StorageMonitor {
    fn default() -> Self {
        Self::new().expect("Failed to create StorageMonitor")
    }
}
