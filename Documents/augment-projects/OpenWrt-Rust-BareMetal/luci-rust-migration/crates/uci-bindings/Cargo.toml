[package]
name = "luci-uci-bindings"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "LuCI UCI Bindings - FFI bindings for OpenWrt configuration system"
keywords.workspace = true
categories.workspace = true

[dependencies]
# Workspace dependencies
luci-shared-types = { path = "../shared-types" }
luci-utilities = { path = "../utilities" }

# System integration
libc = { workspace = true }
nix = { workspace = true }

# Serialization
serde = { workspace = true }
serde_json = { workspace = true }

# Async support
tokio = { workspace = true, optional = true }
futures = { workspace = true, optional = true }

# Utilities
uuid = { workspace = true }
chrono = { workspace = true }
rand = { workspace = true }

# Logging
log = { workspace = true }

# Error handling
anyhow = { workspace = true }
thiserror = { workspace = true }

# Embedded support
heapless = { workspace = true, optional = true }

[features]
default = ["async"]
async = ["tokio", "futures"]
embedded = ["heapless", "luci-shared-types/embedded", "luci-utilities/embedded"]

[build-dependencies]
cc = "1.0"
