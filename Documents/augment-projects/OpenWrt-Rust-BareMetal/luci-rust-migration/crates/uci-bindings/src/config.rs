//! UCI Configuration Management
//!
//! This module provides high-level configuration management functionality
//! for OpenWrt UCI system integration.

use crate::{UciContext, UciError, UciResult, UciValue};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Network interface configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkInterface {
    pub name: String,
    pub proto: String,
    pub ipaddr: Option<String>,
    pub netmask: Option<String>,
    pub gateway: Option<String>,
    pub dns: Vec<String>,
    pub enabled: bool,
    pub mtu: Option<u32>,
    pub mac: Option<String>,
}

/// WiFi device configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WifiDevice {
    pub name: String,
    pub device_type: String,
    pub channel: String,
    pub hwmode: String,
    pub htmode: Option<String>,
    pub txpower: Option<u32>,
    pub country: Option<String>,
    pub disabled: bool,
}

/// WiFi interface configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WifiInterface {
    pub device: String,
    pub network: String,
    pub mode: String,
    pub ssid: String,
    pub encryption: String,
    pub key: Option<String>,
    pub hidden: bool,
    pub disabled: bool,
}

/// DHCP configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DhcpConfig {
    pub interface: String,
    pub start: u32,
    pub limit: u32,
    pub leasetime: String,
    pub dhcpv6: Option<String>,
    pub ra: Option<String>,
}

/// Firewall zone configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FirewallZone {
    pub name: String,
    pub network: Vec<String>,
    pub input: String,
    pub output: String,
    pub forward: String,
    pub masq: bool,
    pub mtu_fix: bool,
}

/// Firewall rule configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FirewallRule {
    pub name: String,
    pub src: Option<String>,
    pub dest: Option<String>,
    pub src_port: Option<String>,
    pub dest_port: Option<String>,
    pub proto: Option<String>,
    pub target: String,
    pub enabled: bool,
}

/// Configuration manager for UCI operations
pub struct ConfigManager {
    uci: UciContext,
}

impl ConfigManager {
    /// Create a new configuration manager
    pub fn new() -> UciResult<Self> {
        Ok(Self {
            uci: UciContext::new()?,
        })
    }

    /// Get network interface configuration
    pub fn get_network_interface(&mut self, name: &str) -> UciResult<NetworkInterface> {
        self.uci.load_package("network")?;
        
        let proto = self.uci.get("network", name, "proto")?;
        let ipaddr = self.uci.get("network", name, "ipaddr").ok();
        let netmask = self.uci.get("network", name, "netmask").ok();
        let gateway = self.uci.get("network", name, "gateway").ok();
        
        Ok(NetworkInterface {
            name: name.to_string(),
            proto: match proto {
                UciValue::String(s) => s.clone(),
                _ => "none".to_string(),
            },
            ipaddr: ipaddr.and_then(|v| match v {
                UciValue::String(s) => Some(s.clone()),
                _ => None,
            }),
            netmask: netmask.and_then(|v| match v {
                UciValue::String(s) => Some(s.clone()),
                _ => None,
            }),
            gateway: gateway.and_then(|v| match v {
                UciValue::String(s) => Some(s.clone()),
                _ => None,
            }),
            dns: vec![], // TODO: Parse DNS list
            enabled: true, // TODO: Get from UCI
            mtu: None, // TODO: Get from UCI
            mac: None, // TODO: Get from UCI
        })
    }

    /// Set network interface configuration
    pub fn set_network_interface(&mut self, interface: &NetworkInterface) -> UciResult<()> {
        self.uci.load_package("network")?;
        
        self.uci.set("network", &interface.name, "proto", UciValue::String(interface.proto.clone()))?;
        
        if let Some(ref ipaddr) = interface.ipaddr {
            self.uci.set("network", &interface.name, "ipaddr", UciValue::String(ipaddr.clone()))?;
        }
        
        if let Some(ref netmask) = interface.netmask {
            self.uci.set("network", &interface.name, "netmask", UciValue::String(netmask.clone()))?;
        }
        
        if let Some(ref gateway) = interface.gateway {
            self.uci.set("network", &interface.name, "gateway", UciValue::String(gateway.clone()))?;
        }
        
        self.uci.save_package("network")?;
        Ok(())
    }

    /// Get WiFi device configuration
    pub fn get_wifi_device(&mut self, name: &str) -> UciResult<WifiDevice> {
        self.uci.load_package("wireless")?;
        
        let device_type = self.uci.get("wireless", name, "type")?;
        let channel = self.uci.get("wireless", name, "channel")?;
        let hwmode = self.uci.get("wireless", name, "hwmode")?;
        
        Ok(WifiDevice {
            name: name.to_string(),
            device_type: match device_type {
                UciValue::String(s) => s.clone(),
                _ => "mac80211".to_string(),
            },
            channel: match channel {
                UciValue::String(s) => s.clone(),
                _ => "auto".to_string(),
            },
            hwmode: match hwmode {
                UciValue::String(s) => s.clone(),
                _ => "11g".to_string(),
            },
            htmode: None, // TODO: Get from UCI
            txpower: None, // TODO: Get from UCI
            country: None, // TODO: Get from UCI
            disabled: false, // TODO: Get from UCI
        })
    }

    /// Set WiFi device configuration
    pub fn set_wifi_device(&mut self, device: &WifiDevice) -> UciResult<()> {
        self.uci.load_package("wireless")?;
        
        self.uci.set("wireless", &device.name, "type", UciValue::String(device.device_type.clone()))?;
        self.uci.set("wireless", &device.name, "channel", UciValue::String(device.channel.clone()))?;
        self.uci.set("wireless", &device.name, "hwmode", UciValue::String(device.hwmode.clone()))?;
        
        if let Some(ref htmode) = device.htmode {
            self.uci.set("wireless", &device.name, "htmode", UciValue::String(htmode.clone()))?;
        }
        
        if let Some(txpower) = device.txpower {
            self.uci.set("wireless", &device.name, "txpower", UciValue::Integer(txpower as i64))?;
        }
        
        if let Some(ref country) = device.country {
            self.uci.set("wireless", &device.name, "country", UciValue::String(country.clone()))?;
        }
        
        self.uci.set("wireless", &device.name, "disabled", UciValue::Boolean(device.disabled))?;
        
        self.uci.save_package("wireless")?;
        Ok(())
    }

    /// Get DHCP configuration
    pub fn get_dhcp_config(&mut self, interface: &str) -> UciResult<DhcpConfig> {
        self.uci.load_package("dhcp")?;
        
        let start = self.uci.get("dhcp", interface, "start")?;
        let limit = self.uci.get("dhcp", interface, "limit")?;
        let leasetime = self.uci.get("dhcp", interface, "leasetime")?;
        
        Ok(DhcpConfig {
            interface: interface.to_string(),
            start: match start {
                UciValue::String(s) => s.parse().unwrap_or(100),
                UciValue::Integer(i) => *i as u32,
                _ => 100,
            },
            limit: match limit {
                UciValue::String(s) => s.parse().unwrap_or(150),
                UciValue::Integer(i) => *i as u32,
                _ => 150,
            },
            leasetime: match leasetime {
                UciValue::String(s) => s.clone(),
                _ => "12h".to_string(),
            },
            dhcpv6: None, // TODO: Get from UCI
            ra: None, // TODO: Get from UCI
        })
    }

    /// Set DHCP configuration
    pub fn set_dhcp_config(&mut self, config: &DhcpConfig) -> UciResult<()> {
        self.uci.load_package("dhcp")?;
        
        self.uci.set("dhcp", &config.interface, "interface", UciValue::String(config.interface.clone()))?;
        self.uci.set("dhcp", &config.interface, "start", UciValue::Integer(config.start as i64))?;
        self.uci.set("dhcp", &config.interface, "limit", UciValue::Integer(config.limit as i64))?;
        self.uci.set("dhcp", &config.interface, "leasetime", UciValue::String(config.leasetime.clone()))?;
        
        self.uci.save_package("dhcp")?;
        Ok(())
    }

    /// Commit all changes
    pub fn commit_all(&mut self) -> UciResult<()> {
        self.uci.commit_package("network")?;
        self.uci.commit_package("wireless")?;
        self.uci.commit_package("dhcp")?;
        self.uci.commit_package("firewall")?;
        Ok(())
    }

    /// List all network interfaces
    pub fn list_network_interfaces(&mut self) -> UciResult<Vec<String>> {
        self.uci.load_package("network")?;
        let sections = self.uci.list_sections("network")?;
        
        Ok(sections.iter()
            .filter(|s| s.section_type == "interface")
            .map(|s| s.name.clone())
            .collect())
    }

    /// List all WiFi devices
    pub fn list_wifi_devices(&mut self) -> UciResult<Vec<String>> {
        self.uci.load_package("wireless")?;
        let sections = self.uci.list_sections("wireless")?;
        
        Ok(sections.iter()
            .filter(|s| s.section_type == "wifi-device")
            .map(|s| s.name.clone())
            .collect())
    }
}

impl Default for ConfigManager {
    fn default() -> Self {
        Self::new().expect("Failed to create ConfigManager")
    }
}
