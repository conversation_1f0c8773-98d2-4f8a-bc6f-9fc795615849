//! UCI Context Management
//!
//! This module provides context management for UCI operations,
//! including transaction handling and error recovery.

use crate::{UciError, UciResult};
use std::collections::HashMap;

/// UCI transaction state
#[derive(Debug, Clone)]
pub enum TransactionState {
    Clean,
    Modified,
    Committed,
    Reverted,
}

/// UCI transaction for atomic operations
pub struct UciTransaction {
    pub package: String,
    pub state: TransactionState,
    pub changes: HashMap<String, String>,
}

impl UciTransaction {
    /// Create a new transaction
    pub fn new(package: &str) -> Self {
        Self {
            package: package.to_string(),
            state: TransactionState::Clean,
            changes: HashMap::new(),
        }
    }

    /// Add a change to the transaction
    pub fn add_change(&mut self, path: &str, value: &str) {
        self.changes.insert(path.to_string(), value.to_string());
        self.state = TransactionState::Modified;
    }

    /// Commit the transaction
    pub fn commit(&mut self) -> UciResult<()> {
        // In real implementation, this would apply changes to UCI
        self.state = TransactionState::Committed;
        Ok(())
    }

    /// Revert the transaction
    pub fn revert(&mut self) -> UciResult<()> {
        self.changes.clear();
        self.state = TransactionState::Reverted;
        Ok(())
    }

    /// Check if transaction has changes
    pub fn has_changes(&self) -> bool {
        !self.changes.is_empty()
    }
}

/// UCI context manager for handling multiple packages
pub struct UciContextManager {
    transactions: HashMap<String, UciTransaction>,
}

impl UciContextManager {
    /// Create a new context manager
    pub fn new() -> Self {
        Self {
            transactions: HashMap::new(),
        }
    }

    /// Begin a transaction for a package
    pub fn begin_transaction(&mut self, package: &str) -> UciResult<()> {
        let transaction = UciTransaction::new(package);
        self.transactions.insert(package.to_string(), transaction);
        Ok(())
    }

    /// Get a transaction for a package
    pub fn get_transaction(&mut self, package: &str) -> Option<&mut UciTransaction> {
        self.transactions.get_mut(package)
    }

    /// Commit all transactions
    pub fn commit_all(&mut self) -> UciResult<()> {
        for transaction in self.transactions.values_mut() {
            transaction.commit()?;
        }
        Ok(())
    }

    /// Revert all transactions
    pub fn revert_all(&mut self) -> UciResult<()> {
        for transaction in self.transactions.values_mut() {
            transaction.revert()?;
        }
        Ok(())
    }

    /// Check if any transaction has changes
    pub fn has_pending_changes(&self) -> bool {
        self.transactions.values().any(|t| t.has_changes())
    }

    /// Get list of packages with pending changes
    pub fn get_modified_packages(&self) -> Vec<String> {
        self.transactions.iter()
            .filter(|(_, t)| t.has_changes())
            .map(|(name, _)| name.clone())
            .collect()
    }
}

impl Default for UciContextManager {
    fn default() -> Self {
        Self::new()
    }
}
