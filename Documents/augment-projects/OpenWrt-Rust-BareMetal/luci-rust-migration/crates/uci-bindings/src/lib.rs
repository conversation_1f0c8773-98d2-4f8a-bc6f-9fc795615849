//! UCI Bindings for OpenWrt Configuration System
//!
//! This crate provides Rust bindings for OpenWrt's Unified Configuration Interface (UCI),
//! allowing safe and efficient access to OpenWrt configuration files from Rust code.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_int, c_void};
use std::ptr;
use thiserror::Error;
use luci_shared_types::*;

pub mod config;
pub mod context;
pub mod package;
pub mod section;
pub mod option;

pub use config::*;
pub use context::*;
pub use package::*;
pub use section::*;
pub use option::*;

/// UCI error types
#[derive(Error, Debug, Clone)]
pub enum UciError {
    #[error("UCI context creation failed")]
    ContextCreationFailed,
    #[error("Package not found: {0}")]
    PackageNotFound(String),
    #[error("Section not found: {0}")]
    SectionNotFound(String),
    #[error("Option not found: {0}")]
    OptionNotFound(String),
    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),
    #[error("Permission denied")]
    PermissionDenied,
    #[error("I/O error: {0}")]
    IoError(String),
    #[error("Parse error: {0}")]
    ParseError(String),
    #[error("Commit failed: {0}")]
    CommitFailed(String),
}

pub type UciResult<T> = Result<T, UciError>;

/// UCI configuration value
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(untagged)]
pub enum UciValue {
    String(String),
    List(Vec<String>),
    Boolean(bool),
    Integer(i64),
}

impl From<String> for UciValue {
    fn from(s: String) -> Self {
        UciValue::String(s)
    }
}

impl From<&str> for UciValue {
    fn from(s: &str) -> Self {
        UciValue::String(s.to_string())
    }
}

impl From<Vec<String>> for UciValue {
    fn from(v: Vec<String>) -> Self {
        UciValue::List(v)
    }
}

impl From<bool> for UciValue {
    fn from(b: bool) -> Self {
        UciValue::Boolean(b)
    }
}

impl From<i64> for UciValue {
    fn from(i: i64) -> Self {
        UciValue::Integer(i)
    }
}

/// UCI configuration entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UciOption {
    pub name: String,
    pub value: UciValue,
}

/// UCI configuration section
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UciSection {
    pub name: String,
    pub section_type: String,
    pub options: HashMap<String, UciValue>,
    pub anonymous: bool,
}

/// UCI configuration package
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UciPackage {
    pub name: String,
    pub sections: HashMap<String, UciSection>,
    pub path: String,
}

/// Main UCI context for configuration management
pub struct UciContext {
    ctx: *mut c_void,
    packages: HashMap<String, UciPackage>,
}

impl UciContext {
    /// Create a new UCI context
    pub fn new() -> UciResult<Self> {
        // For now, we'll implement a mock UCI context
        // In a real implementation, this would call uci_alloc_context()
        Ok(Self {
            ctx: ptr::null_mut(),
            packages: HashMap::new(),
        })
    }

    /// Load a UCI package
    pub fn load_package(&mut self, name: &str) -> UciResult<&UciPackage> {
        // Mock implementation - in real UCI, this would call uci_load()
        if !self.packages.contains_key(name) {
            let package = self.load_package_from_file(name)?;
            self.packages.insert(name.to_string(), package);
        }

        self.packages.get(name).ok_or_else(|| UciError::PackageNotFound(name.to_string()))
    }

    /// Save a UCI package
    pub fn save_package(&mut self, name: &str) -> UciResult<()> {
        // Mock implementation - in real UCI, this would call uci_save()
        if !self.packages.contains_key(name) {
            return Err(UciError::PackageNotFound(name.to_string()));
        }

        // In real implementation, write to file system
        Ok(())
    }

    /// Commit changes to a UCI package
    pub fn commit_package(&mut self, name: &str) -> UciResult<()> {
        // Mock implementation - in real UCI, this would call uci_commit()
        if !self.packages.contains_key(name) {
            return Err(UciError::PackageNotFound(name.to_string()));
        }

        // In real implementation, apply changes to system
        Ok(())
    }

    /// Get a configuration value
    pub fn get(&self, package: &str, section: &str, option: &str) -> UciResult<&UciValue> {
        let pkg = self.packages.get(package)
            .ok_or_else(|| UciError::PackageNotFound(package.to_string()))?;

        let sect = pkg.sections.get(section)
            .ok_or_else(|| UciError::SectionNotFound(section.to_string()))?;

        sect.options.get(option)
            .ok_or_else(|| UciError::OptionNotFound(option.to_string()))
    }

    /// Set a configuration value
    pub fn set(&mut self, package: &str, section: &str, option: &str, value: UciValue) -> UciResult<()> {
        let pkg = self.packages.get_mut(package)
            .ok_or_else(|| UciError::PackageNotFound(package.to_string()))?;

        let sect = pkg.sections.get_mut(section)
            .ok_or_else(|| UciError::SectionNotFound(section.to_string()))?;

        sect.options.insert(option.to_string(), value);
        Ok(())
    }

    /// Delete a configuration option
    pub fn delete(&mut self, package: &str, section: &str, option: &str) -> UciResult<()> {
        let pkg = self.packages.get_mut(package)
            .ok_or_else(|| UciError::PackageNotFound(package.to_string()))?;

        let sect = pkg.sections.get_mut(section)
            .ok_or_else(|| UciError::SectionNotFound(section.to_string()))?;

        sect.options.remove(option)
            .ok_or_else(|| UciError::OptionNotFound(option.to_string()))?;

        Ok(())
    }

    /// Add a new section
    pub fn add_section(&mut self, package: &str, section_type: &str, name: Option<&str>) -> UciResult<String> {
        let pkg = self.packages.get_mut(package)
            .ok_or_else(|| UciError::PackageNotFound(package.to_string()))?;

        let section_name = name.unwrap_or(&format!("cfg{:06x}", rand::random::<u32>())).to_string();

        let section = UciSection {
            name: section_name.clone(),
            section_type: section_type.to_string(),
            options: HashMap::new(),
            anonymous: name.is_none(),
        };

        pkg.sections.insert(section_name.clone(), section);
        Ok(section_name)
    }

    /// Delete a section
    pub fn delete_section(&mut self, package: &str, section: &str) -> UciResult<()> {
        let pkg = self.packages.get_mut(package)
            .ok_or_else(|| UciError::PackageNotFound(package.to_string()))?;

        pkg.sections.remove(section)
            .ok_or_else(|| UciError::SectionNotFound(section.to_string()))?;

        Ok(())
    }

    /// List all sections in a package
    pub fn list_sections(&self, package: &str) -> UciResult<Vec<&UciSection>> {
        let pkg = self.packages.get(package)
            .ok_or_else(|| UciError::PackageNotFound(package.to_string()))?;

        Ok(pkg.sections.values().collect())
    }

    /// Load package from file (mock implementation)
    fn load_package_from_file(&self, name: &str) -> UciResult<UciPackage> {
        // Mock implementation - create sample configurations
        let mut sections = HashMap::new();

        match name {
            "network" => {
                // Create sample network configuration
                let mut lan_options = HashMap::new();
                lan_options.insert("proto".to_string(), UciValue::String("static".to_string()));
                lan_options.insert("ipaddr".to_string(), UciValue::String("***********".to_string()));
                lan_options.insert("netmask".to_string(), UciValue::String("*************".to_string()));

                sections.insert("lan".to_string(), UciSection {
                    name: "lan".to_string(),
                    section_type: "interface".to_string(),
                    options: lan_options,
                    anonymous: false,
                });
            },
            "wireless" => {
                // Create sample wireless configuration
                let mut radio_options = HashMap::new();
                radio_options.insert("type".to_string(), UciValue::String("mac80211".to_string()));
                radio_options.insert("channel".to_string(), UciValue::String("auto".to_string()));
                radio_options.insert("hwmode".to_string(), UciValue::String("11g".to_string()));

                sections.insert("radio0".to_string(), UciSection {
                    name: "radio0".to_string(),
                    section_type: "wifi-device".to_string(),
                    options: radio_options,
                    anonymous: false,
                });
            },
            "dhcp" => {
                // Create sample DHCP configuration
                let mut dhcp_options = HashMap::new();
                dhcp_options.insert("interface".to_string(), UciValue::String("lan".to_string()));
                dhcp_options.insert("start".to_string(), UciValue::String("100".to_string()));
                dhcp_options.insert("limit".to_string(), UciValue::String("150".to_string()));
                dhcp_options.insert("leasetime".to_string(), UciValue::String("12h".to_string()));

                sections.insert("lan".to_string(), UciSection {
                    name: "lan".to_string(),
                    section_type: "dhcp".to_string(),
                    options: dhcp_options,
                    anonymous: false,
                });
            },
            _ => {
                return Err(UciError::PackageNotFound(name.to_string()));
            }
        }

        Ok(UciPackage {
            name: name.to_string(),
            sections,
            path: format!("/etc/config/{}", name),
        })
    }
}

impl Drop for UciContext {
    fn drop(&mut self) {
        // In real implementation, this would call uci_free_context()
        if !self.ctx.is_null() {
            // uci_free_context(self.ctx);
        }
    }
}

// Thread safety - UCI context is not thread-safe by default
unsafe impl Send for UciContext {}
// Note: Not implementing Sync as UCI context should not be shared between threads

/// Global UCI context instance
static mut GLOBAL_UCI_CONTEXT: Option<UciContext> = None;
static UCI_INIT: std::sync::Once = std::sync::Once::new();

/// Get or create the global UCI context
pub fn get_uci_context() -> UciResult<&'static mut UciContext> {
    unsafe {
        UCI_INIT.call_once(|| {
            GLOBAL_UCI_CONTEXT = Some(UciContext::new().expect("Failed to create UCI context"));
        });

        GLOBAL_UCI_CONTEXT.as_mut().ok_or(UciError::ContextCreationFailed)
    }
}
