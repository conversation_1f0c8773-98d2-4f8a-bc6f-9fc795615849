//! UCI Option Management
//!
//! This module provides option-level operations for UCI configuration management.

use crate::{UciError, UciResult, UciValue};
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use std::str::FromStr;

/// UCI option operations
pub trait UciOptionOps {
    /// Get an option value
    fn get_option(&self, package: &str, section: &str, option: &str) -> UciResult<&UciValue>;
    
    /// Set an option value
    fn set_option(&mut self, package: &str, section: &str, option: &str, value: UciValue) -> UciResult<()>;
    
    /// Delete an option
    fn delete_option(&mut self, package: &str, section: &str, option: &str) -> UciResult<()>;
    
    /// Check if option exists
    fn option_exists(&self, package: &str, section: &str, option: &str) -> bool;
    
    /// List all options in a section
    fn list_options(&self, package: &str, section: &str) -> UciResult<Vec<String>>;
}

/// Option value parser and validator
pub struct OptionParser;

impl OptionParser {
    /// Parse a string value as IP address
    pub fn parse_ip_address(value: &str) -> UciResult<IpAddr> {
        IpAddr::from_str(value)
            .map_err(|_| UciError::ParseError(format!("Invalid IP address: {}", value)))
    }

    /// Parse a string value as IPv4 address
    pub fn parse_ipv4_address(value: &str) -> UciResult<Ipv4Addr> {
        Ipv4Addr::from_str(value)
            .map_err(|_| UciError::ParseError(format!("Invalid IPv4 address: {}", value)))
    }

    /// Parse a string value as IPv6 address
    pub fn parse_ipv6_address(value: &str) -> UciResult<Ipv6Addr> {
        Ipv6Addr::from_str(value)
            .map_err(|_| UciError::ParseError(format!("Invalid IPv6 address: {}", value)))
    }

    /// Parse a string value as port number
    pub fn parse_port(value: &str) -> UciResult<u16> {
        value.parse::<u16>()
            .map_err(|_| UciError::ParseError(format!("Invalid port number: {}", value)))
    }

    /// Parse a string value as boolean
    pub fn parse_boolean(value: &str) -> UciResult<bool> {
        match value.to_lowercase().as_str() {
            "1" | "true" | "yes" | "on" | "enabled" => Ok(true),
            "0" | "false" | "no" | "off" | "disabled" => Ok(false),
            _ => Err(UciError::ParseError(format!("Invalid boolean value: {}", value))),
        }
    }

    /// Parse a string value as integer
    pub fn parse_integer(value: &str) -> UciResult<i64> {
        value.parse::<i64>()
            .map_err(|_| UciError::ParseError(format!("Invalid integer: {}", value)))
    }

    /// Parse a list value (space or comma separated)
    pub fn parse_list(value: &str) -> Vec<String> {
        value.split_whitespace()
            .chain(value.split(','))
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect()
    }
}

/// Option validator for common UCI options
pub struct OptionValidator;

impl OptionValidator {
    /// Validate network protocol option
    pub fn validate_network_proto(value: &str) -> UciResult<()> {
        match value {
            "static" | "dhcp" | "pppoe" | "none" | "6in4" | "6to4" | "6rd" => Ok(()),
            _ => Err(UciError::InvalidConfig(format!("Invalid network protocol: {}", value))),
        }
    }

    /// Validate WiFi mode option
    pub fn validate_wifi_mode(value: &str) -> UciResult<()> {
        match value {
            "ap" | "sta" | "adhoc" | "monitor" | "mesh" => Ok(()),
            _ => Err(UciError::InvalidConfig(format!("Invalid WiFi mode: {}", value))),
        }
    }

    /// Validate WiFi encryption option
    pub fn validate_wifi_encryption(value: &str) -> UciResult<()> {
        match value {
            "none" | "wep" | "psk" | "psk2" | "psk-mixed" | "wpa" | "wpa2" | "wpa-mixed" => Ok(()),
            _ => Err(UciError::InvalidConfig(format!("Invalid WiFi encryption: {}", value))),
        }
    }

    /// Validate firewall target option
    pub fn validate_firewall_target(value: &str) -> UciResult<()> {
        match value {
            "ACCEPT" | "REJECT" | "DROP" | "MARK" | "NOTRACK" => Ok(()),
            _ => Err(UciError::InvalidConfig(format!("Invalid firewall target: {}", value))),
        }
    }

    /// Validate firewall policy option
    pub fn validate_firewall_policy(value: &str) -> UciResult<()> {
        match value {
            "ACCEPT" | "REJECT" | "DROP" => Ok(()),
            _ => Err(UciError::InvalidConfig(format!("Invalid firewall policy: {}", value))),
        }
    }

    /// Validate IP address option
    pub fn validate_ip_address(value: &str) -> UciResult<()> {
        OptionParser::parse_ip_address(value)?;
        Ok(())
    }

    /// Validate netmask option
    pub fn validate_netmask(value: &str) -> UciResult<()> {
        // Check if it's a valid IPv4 address (dotted decimal notation)
        if let Ok(_) = OptionParser::parse_ipv4_address(value) {
            return Ok(());
        }

        // Check if it's a valid CIDR prefix length
        if let Ok(prefix) = value.parse::<u8>() {
            if prefix <= 32 {
                return Ok(());
            }
        }

        Err(UciError::InvalidConfig(format!("Invalid netmask: {}", value)))
    }

    /// Validate port option
    pub fn validate_port(value: &str) -> UciResult<()> {
        OptionParser::parse_port(value)?;
        Ok(())
    }

    /// Validate port range option
    pub fn validate_port_range(value: &str) -> UciResult<()> {
        if value.contains('-') {
            let parts: Vec<&str> = value.split('-').collect();
            if parts.len() != 2 {
                return Err(UciError::InvalidConfig(format!("Invalid port range format: {}", value)));
            }
            
            let start = OptionParser::parse_port(parts[0])?;
            let end = OptionParser::parse_port(parts[1])?;
            
            if start >= end {
                return Err(UciError::InvalidConfig(format!("Invalid port range: start >= end")));
            }
        } else {
            // Single port
            OptionParser::parse_port(value)?;
        }
        
        Ok(())
    }
}

/// Helper functions for common option operations
pub struct OptionHelpers;

impl OptionHelpers {
    /// Convert UciValue to string
    pub fn value_to_string(value: &UciValue) -> String {
        match value {
            UciValue::String(s) => s.clone(),
            UciValue::Boolean(b) => if *b { "1".to_string() } else { "0".to_string() },
            UciValue::Integer(i) => i.to_string(),
            UciValue::List(l) => l.join(" "),
        }
    }

    /// Convert UciValue to boolean
    pub fn value_to_bool(value: &UciValue) -> UciResult<bool> {
        match value {
            UciValue::Boolean(b) => Ok(*b),
            UciValue::String(s) => OptionParser::parse_boolean(s),
            UciValue::Integer(i) => Ok(*i != 0),
            _ => Err(UciError::ParseError("Cannot convert to boolean".to_string())),
        }
    }

    /// Convert UciValue to integer
    pub fn value_to_int(value: &UciValue) -> UciResult<i64> {
        match value {
            UciValue::Integer(i) => Ok(*i),
            UciValue::String(s) => OptionParser::parse_integer(s),
            UciValue::Boolean(b) => Ok(if *b { 1 } else { 0 }),
            _ => Err(UciError::ParseError("Cannot convert to integer".to_string())),
        }
    }

    /// Convert UciValue to list
    pub fn value_to_list(value: &UciValue) -> Vec<String> {
        match value {
            UciValue::List(l) => l.clone(),
            UciValue::String(s) => OptionParser::parse_list(s),
            _ => vec![Self::value_to_string(value)],
        }
    }
}
