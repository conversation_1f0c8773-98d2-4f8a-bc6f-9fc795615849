//! UCI Package Management
//!
//! This module provides package-level operations for UCI configuration management.

use crate::{UciError, UciResult, UciSection};
use std::collections::HashMap;

/// UCI package operations
pub trait UciPackageOps {
    /// Load package from filesystem
    fn load(&mut self, name: &str) -> UciResult<()>;
    
    /// Save package to filesystem
    fn save(&mut self, name: &str) -> UciResult<()>;
    
    /// Commit package changes
    fn commit(&mut self, name: &str) -> UciResult<()>;
    
    /// Revert package changes
    fn revert(&mut self, name: &str) -> UciResult<()>;
    
    /// Check if package exists
    fn exists(&self, name: &str) -> bool;
    
    /// Get package sections
    fn get_sections(&self, name: &str) -> UciResult<Vec<&UciSection>>;
}

/// Package validation rules
pub struct PackageValidator {
    required_sections: HashMap<String, Vec<String>>,
    optional_sections: HashMap<String, Vec<String>>,
}

impl PackageValidator {
    /// Create a new package validator
    pub fn new() -> Self {
        let mut validator = Self {
            required_sections: HashMap::new(),
            optional_sections: HashMap::new(),
        };
        
        validator.setup_default_rules();
        validator
    }

    /// Setup default validation rules for common packages
    fn setup_default_rules(&mut self) {
        // Network package rules
        self.required_sections.insert("network".to_string(), vec![
            "interface".to_string(),
        ]);
        
        // Wireless package rules
        self.required_sections.insert("wireless".to_string(), vec![
            "wifi-device".to_string(),
        ]);
        
        // DHCP package rules
        self.required_sections.insert("dhcp".to_string(), vec![
            "dnsmasq".to_string(),
        ]);
        
        // Firewall package rules
        self.required_sections.insert("firewall".to_string(), vec![
            "defaults".to_string(),
        ]);
    }

    /// Validate a package configuration
    pub fn validate_package(&self, package_name: &str, sections: &HashMap<String, UciSection>) -> UciResult<()> {
        if let Some(required) = self.required_sections.get(package_name) {
            for section_type in required {
                let has_section = sections.values()
                    .any(|s| s.section_type == *section_type);
                
                if !has_section {
                    return Err(UciError::InvalidConfig(
                        format!("Package {} missing required section type: {}", package_name, section_type)
                    ));
                }
            }
        }
        
        Ok(())
    }

    /// Add custom validation rule
    pub fn add_required_section(&mut self, package: &str, section_type: &str) {
        self.required_sections
            .entry(package.to_string())
            .or_insert_with(Vec::new)
            .push(section_type.to_string());
    }
}

impl Default for PackageValidator {
    fn default() -> Self {
        Self::new()
    }
}
