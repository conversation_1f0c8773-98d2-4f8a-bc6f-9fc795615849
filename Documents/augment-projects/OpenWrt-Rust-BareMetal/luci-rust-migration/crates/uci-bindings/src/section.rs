//! UCI Section Management
//!
//! This module provides section-level operations for UCI configuration management.

use crate::{UciError, UciResult, UciValue};
use std::collections::HashMap;

/// UCI section operations
pub trait UciSectionOps {
    /// Add a new section
    fn add_section(&mut self, package: &str, section_type: &str, name: Option<&str>) -> UciResult<String>;
    
    /// Delete a section
    fn delete_section(&mut self, package: &str, section: &str) -> UciResult<()>;
    
    /// Rename a section
    fn rename_section(&mut self, package: &str, old_name: &str, new_name: &str) -> UciResult<()>;
    
    /// Check if section exists
    fn section_exists(&self, package: &str, section: &str) -> bool;
    
    /// Get section type
    fn get_section_type(&self, package: &str, section: &str) -> UciResult<String>;
}

/// Section configuration builder
pub struct SectionBuilder {
    section_type: String,
    name: Option<String>,
    options: HashMap<String, UciValue>,
    anonymous: bool,
}

impl SectionBuilder {
    /// Create a new section builder
    pub fn new(section_type: &str) -> Self {
        Self {
            section_type: section_type.to_string(),
            name: None,
            options: HashMap::new(),
            anonymous: true,
        }
    }

    /// Set section name (makes it non-anonymous)
    pub fn name(mut self, name: &str) -> Self {
        self.name = Some(name.to_string());
        self.anonymous = false;
        self
    }

    /// Add an option
    pub fn option<V: Into<UciValue>>(mut self, key: &str, value: V) -> Self {
        self.options.insert(key.to_string(), value.into());
        self
    }

    /// Build the section
    pub fn build(self) -> crate::UciSection {
        let name = self.name.unwrap_or_else(|| format!("cfg{:06x}", rand::random::<u32>()));
        
        crate::UciSection {
            name,
            section_type: self.section_type,
            options: self.options,
            anonymous: self.anonymous,
        }
    }
}

/// Common section builders for OpenWrt configurations
pub struct CommonSections;

impl CommonSections {
    /// Create a network interface section
    pub fn network_interface(name: &str) -> SectionBuilder {
        SectionBuilder::new("interface").name(name)
    }

    /// Create a WiFi device section
    pub fn wifi_device(name: &str) -> SectionBuilder {
        SectionBuilder::new("wifi-device").name(name)
    }

    /// Create a WiFi interface section
    pub fn wifi_iface() -> SectionBuilder {
        SectionBuilder::new("wifi-iface")
    }

    /// Create a DHCP section
    pub fn dhcp(interface: &str) -> SectionBuilder {
        SectionBuilder::new("dhcp")
            .name(interface)
            .option("interface", interface)
    }

    /// Create a firewall zone section
    pub fn firewall_zone(name: &str) -> SectionBuilder {
        SectionBuilder::new("zone").name(name)
    }

    /// Create a firewall rule section
    pub fn firewall_rule() -> SectionBuilder {
        SectionBuilder::new("rule")
    }

    /// Create a firewall forwarding section
    pub fn firewall_forwarding() -> SectionBuilder {
        SectionBuilder::new("forwarding")
    }
}

/// Section validation
pub struct SectionValidator;

impl SectionValidator {
    /// Validate network interface section
    pub fn validate_network_interface(section: &crate::UciSection) -> UciResult<()> {
        if section.section_type != "interface" {
            return Err(UciError::InvalidConfig("Not a network interface section".to_string()));
        }

        // Check required options
        if !section.options.contains_key("proto") {
            return Err(UciError::InvalidConfig("Network interface missing 'proto' option".to_string()));
        }

        // Validate protocol-specific options
        if let Some(UciValue::String(proto)) = section.options.get("proto") {
            match proto.as_str() {
                "static" => {
                    if !section.options.contains_key("ipaddr") {
                        return Err(UciError::InvalidConfig("Static interface missing 'ipaddr'".to_string()));
                    }
                    if !section.options.contains_key("netmask") {
                        return Err(UciError::InvalidConfig("Static interface missing 'netmask'".to_string()));
                    }
                },
                "dhcp" => {
                    // DHCP interfaces don't require additional options
                },
                "pppoe" => {
                    if !section.options.contains_key("username") {
                        return Err(UciError::InvalidConfig("PPPoE interface missing 'username'".to_string()));
                    }
                    if !section.options.contains_key("password") {
                        return Err(UciError::InvalidConfig("PPPoE interface missing 'password'".to_string()));
                    }
                },
                _ => {
                    // Unknown protocol, but allow it
                }
            }
        }

        Ok(())
    }

    /// Validate WiFi device section
    pub fn validate_wifi_device(section: &crate::UciSection) -> UciResult<()> {
        if section.section_type != "wifi-device" {
            return Err(UciError::InvalidConfig("Not a WiFi device section".to_string()));
        }

        // Check required options
        if !section.options.contains_key("type") {
            return Err(UciError::InvalidConfig("WiFi device missing 'type' option".to_string()));
        }

        Ok(())
    }

    /// Validate WiFi interface section
    pub fn validate_wifi_interface(section: &crate::UciSection) -> UciResult<()> {
        if section.section_type != "wifi-iface" {
            return Err(UciError::InvalidConfig("Not a WiFi interface section".to_string()));
        }

        // Check required options
        if !section.options.contains_key("device") {
            return Err(UciError::InvalidConfig("WiFi interface missing 'device' option".to_string()));
        }

        if !section.options.contains_key("network") {
            return Err(UciError::InvalidConfig("WiFi interface missing 'network' option".to_string()));
        }

        if !section.options.contains_key("mode") {
            return Err(UciError::InvalidConfig("WiFi interface missing 'mode' option".to_string()));
        }

        Ok(())
    }

    /// Validate DHCP section
    pub fn validate_dhcp(section: &crate::UciSection) -> UciResult<()> {
        if section.section_type != "dhcp" {
            return Err(UciError::InvalidConfig("Not a DHCP section".to_string()));
        }

        // Check required options
        if !section.options.contains_key("interface") {
            return Err(UciError::InvalidConfig("DHCP section missing 'interface' option".to_string()));
        }

        Ok(())
    }
}
