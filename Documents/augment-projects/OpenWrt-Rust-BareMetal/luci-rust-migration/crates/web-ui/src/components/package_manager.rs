//! Package Management Web Components
//!
//! This module provides web interface components for package management functionality.

use leptos::*;
use serde::{Deserialize, Serialize};
use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus};

/// Package management page component
#[component]
pub fn PackageManagerPage() -> impl IntoView {
    let (selected_tab, set_selected_tab) = create_signal("browse".to_string());

    view! {
        <div class="package-manager">
            <div class="bg-white shadow rounded-lg">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <button
                            class=move || format!(
                                "whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {}",
                                if selected_tab.get() == "browse" {
                                    "border-blue-500 text-blue-600"
                                } else {
                                    "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                }
                            )
                            on:click=move |_| set_selected_tab.set("browse".to_string())
                        >
                            "Browse Packages"
                        </button>
                        <button
                            class=move || format!(
                                "whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {}",
                                if selected_tab.get() == "installed" {
                                    "border-blue-500 text-blue-600"
                                } else {
                                    "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                }
                            )
                            on:click=move |_| set_selected_tab.set("installed".to_string())
                        >
                            "Installed Packages"
                        </button>
                        <button
                            class=move || format!(
                                "whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {}",
                                if selected_tab.get() == "updates" {
                                    "border-blue-500 text-blue-600"
                                } else {
                                    "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                }
                            )
                            on:click=move |_| set_selected_tab.set("updates".to_string())
                        >
                            "Updates"
                        </button>
                        <button
                            class=move || format!(
                                "whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {}",
                                if selected_tab.get() == "repositories" {
                                    "border-blue-500 text-blue-600"
                                } else {
                                    "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                }
                            )
                            on:click=move |_| set_selected_tab.set("repositories".to_string())
                        >
                            "Repositories"
                        </button>
                    </nav>
                </div>

                <div class="p-6">
                    {move || match selected_tab.get().as_str() {
                        "browse" => view! { <PackageBrowser /> }.into_view(),
                        "installed" => view! { <InstalledPackages /> }.into_view(),
                        "updates" => view! { <PackageUpdates /> }.into_view(),
                        "repositories" => view! { <RepositoryManager /> }.into_view(),
                        _ => view! { <PackageBrowser /> }.into_view(),
                    }}
                </div>
            </div>
        </div>
    }
}

/// Package browser component
#[component]
pub fn PackageBrowser() -> impl IntoView {
    let (search_query, set_search_query) = create_signal(String::new());
    let (selected_category, set_selected_category) = create_signal("all".to_string());
    let (packages, set_packages) = create_signal(Vec::<PackageInfo>::new());
    let (loading, set_loading) = create_signal(false);

    // Mock package data
    let mock_packages = vec![
        PackageInfo {
            name: "wget".to_string(),
            version: "1.21.3-1".to_string(),
            description: "Non-interactive network downloader".to_string(),
            section: "net".to_string(),
            category: "Network".to_string(),
            repository: "packages".to_string(),
            maintainer: "OpenWrt Developers".to_string(),
            architecture: "mips_24kc".to_string(),
            installed_size: 1024 * 300,
            download_size: 1024 * 200,
            status: PackageStatus::NotInstalled,
            priority: luci_package_manager::PackagePriority::Optional,
            depends: vec!["libc".to_string(), "libpcre".to_string()],
            conflicts: vec![],
            provides: vec![],
            replaces: vec![],
            homepage: Some("https://www.gnu.org/software/wget/".to_string()),
            source: None,
            filename: Some("wget_1.21.3-1_mips_24kc.ipk".to_string()),
            md5sum: Some("ad0234829205b9033196ba818f7a872b".to_string()),
            sha256sum: None,
        },
    ];

    create_effect(move |_| {
        set_packages.set(mock_packages.clone());
    });

    view! {
        <div class="space-y-6">
            // Search and filter controls
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input
                        type="text"
                        placeholder="Search packages..."
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        prop:value=search_query
                        on:input=move |ev| set_search_query.set(event_target_value(&ev))
                    />
                </div>
                <div class="sm:w-48">
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        prop:value=selected_category
                        on:change=move |ev| set_selected_category.set(event_target_value(&ev))
                    >
                        <option value="all">"All Categories"</option>
                        <option value="base">"Base System"</option>
                        <option value="network">"Network"</option>
                        <option value="utilities">"Utilities"</option>
                        <option value="multimedia">"Multimedia"</option>
                        <option value="development">"Development"</option>
                    </select>
                </div>
                <button
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    on:click=move |_| {
                        set_loading.set(true);
                        // In real implementation, would trigger package search
                        set_loading.set(false);
                    }
                >
                    "Search"
                </button>
            </div>

            // Package list
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <ul class="divide-y divide-gray-200">
                    <For
                        each=move || packages.get()
                        key=|package| package.name.clone()
                        children=move |package| {
                            view! {
                                <PackageListItem package=package />
                            }
                        }
                    />
                </ul>
            </div>

            // Loading indicator
            {move || if loading.get() {
                view! {
                    <div class="flex justify-center py-4">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}
        </div>
    }
}

/// Package list item component
#[component]
pub fn PackageListItem(package: PackageInfo) -> impl IntoView {
    let (installing, set_installing) = create_signal(false);
    let package_name = package.name.clone();

    let install_package = move |_| {
        set_installing.set(true);
        // In real implementation, would call package manager API
        log::info!("Installing package: {}", package_name);
        set_installing.set(false);
    };

    view! {
        <li class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900 truncate">
                                {package.name.clone()}
                            </p>
                            <p class="text-sm text-gray-500">
                                {package.description.clone()}
                            </p>
                            <div class="mt-1 flex items-center text-xs text-gray-500 space-x-4">
                                <span>"Version: " {package.version.clone()}</span>
                                <span>"Size: " {format_size(package.download_size)}</span>
                                <span>"Category: " {package.category.clone()}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ml-4 flex-shrink-0">
                    {match package.status {
                        PackageStatus::Installed => view! {
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                "Installed"
                            </span>
                        }.into_view(),
                        PackageStatus::Upgradable => view! {
                            <button
                                class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
                                on:click=install_package
                                disabled=installing
                            >
                                {move || if installing.get() { "Upgrading..." } else { "Upgrade" }}
                            </button>
                        }.into_view(),
                        _ => view! {
                            <button
                                class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                on:click=install_package
                                disabled=installing
                            >
                                {move || if installing.get() { "Installing..." } else { "Install" }}
                            </button>
                        }.into_view(),
                    }}
                </div>
            </div>
        </li>
    }
}

/// Installed packages component
#[component]
pub fn InstalledPackages() -> impl IntoView {
    view! {
        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">"Installed Packages"</h3>
                <button class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                    "Auto Remove Unused"
                </button>
            </div>
            <div class="text-gray-500">
                "Loading installed packages..."
            </div>
        </div>
    }
}

/// Package updates component
#[component]
pub fn PackageUpdates() -> impl IntoView {
    view! {
        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">"Available Updates"</h3>
                <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                    "Update All"
                </button>
            </div>
            <div class="text-gray-500">
                "Checking for updates..."
            </div>
        </div>
    }
}

/// Repository manager component
#[component]
pub fn RepositoryManager() -> impl IntoView {
    view! {
        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">"Package Repositories"</h3>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    "Add Repository"
                </button>
            </div>
            <div class="text-gray-500">
                "Loading repositories..."
            </div>
        </div>
    }
}

/// Format file size in human readable format
fn format_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}
