use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub system_info: SystemInfo,
    pub hardware_info: HardwareInfo,
    pub memory_info: MemoryInfo,
    pub storage_info: StorageInfo,
    pub network_info: Vec<NetworkDevice>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SystemInfo {
    pub hostname: String,
    pub model: String,
    pub architecture: String,
    pub kernel_version: String,
    pub firmware_version: String,
    pub uptime: String,
    pub load_average: String,
    pub current_time: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HardwareInfo {
    pub cpu_model: String,
    pub cpu_cores: u32,
    pub cpu_frequency: String,
    pub cpu_temperature: Option<f32>,
    pub board_name: String,
    pub target: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MemoryInfo {
    pub total_ram: u64,
    pub available_ram: u64,
    pub used_ram: u64,
    pub free_ram: u64,
    pub cached_ram: u64,
    pub buffered_ram: u64,
    pub swap_total: u64,
    pub swap_used: u64,
    pub swap_free: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageInfo {
    pub total_flash: u64,
    pub available_flash: u64,
    pub used_flash: u64,
    pub filesystems: Vec<FilesystemInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilesystemInfo {
    pub device: String,
    pub mount_point: String,
    pub filesystem_type: String,
    pub total_size: u64,
    pub used_size: u64,
    pub available_size: u64,
    pub usage_percentage: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkDevice {
    pub name: String,
    pub device_type: String,
    pub mac_address: String,
    pub ip_addresses: Vec<String>,
    pub status: String,
    pub mtu: u32,
    pub rx_bytes: u64,
    pub tx_bytes: u64,
    pub rx_packets: u64,
    pub tx_packets: u64,
}

#[component]
pub fn DeviceInfoPage() -> impl IntoView {
    let (device_info, set_device_info) = create_signal(None::<DeviceInfo>);
    let (loading, set_loading) = create_signal(true);
    let (error, set_error) = create_signal(None::<String>);

    // Load device info on mount
    create_effect(move |_| {
        spawn_local(async move {
            match fetch_device_info().await {
                Ok(info) => {
                    set_device_info.set(Some(info));
                    set_loading.set(false);
                    set_error.set(None);
                }
                Err(e) => {
                    set_error.set(Some(format!("Failed to fetch device info: {}", e)));
                    set_loading.set(false);
                }
            }
        });
    });

    view! {
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">
                        "Device Information"
                    </h1>
                    <button
                        class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:click=move |_| {
                            set_loading.set(true);
                            spawn_local(async move {
                                match fetch_device_info().await {
                                    Ok(info) => {
                                        set_device_info.set(Some(info));
                                        set_loading.set(false);
                                        set_error.set(None);
                                    }
                                    Err(e) => {
                                        set_error.set(Some(format!("Failed to refresh device info: {}", e)));
                                        set_loading.set(false);
                                    }
                                }
                            });
                        }
                    >
                        "Refresh"
                    </button>
                </div>

                {move || {
                    if loading.get() {
                        view! {
                            <div class="flex items-center justify-center py-12">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <span class="ml-2 text-gray-600">"Loading device information..."</span>
                            </div>
                        }.into_view()
                    } else if let Some(err) = error.get() {
                        view! {
                            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">"Error"</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            {err}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }.into_view()
                    } else if let Some(info) = device_info.get() {
                        view! {
                            <div class="space-y-6">
                                <SystemOverview info=info.clone() />
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <HardwareInfo info=info.clone() />
                                    <MemoryFlashInfo info=info.clone() />
                                </div>
                                <NetworkDevices devices=info.network_info />
                            </div>
                        }.into_view()
                    } else {
                        view! {
                            <div class="text-center py-12 text-gray-500">
                                "No device information available"
                            </div>
                        }.into_view()
                    }
                }}
            </div>
        </div>
    }
}

#[component]
fn SystemOverview(info: DeviceInfo) -> impl IntoView {
    view! {
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">"System Overview"</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">"Hostname"</p>
                            <p class="text-lg font-semibold text-gray-900">{info.system_info.hostname}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">"Model"</p>
                            <p class="text-lg font-semibold text-gray-900">{info.system_info.model}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">"Uptime"</p>
                            <p class="text-lg font-semibold text-gray-900">{info.system_info.uptime}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">"Firmware"</p>
                            <p class="text-lg font-semibold text-gray-900">{info.system_info.firmware_version}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">"Architecture"</p>
                            <p class="text-lg font-semibold text-gray-900">{info.system_info.architecture}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">"Current Time"</p>
                            <p class="text-lg font-semibold text-gray-900">{info.system_info.current_time}</p>
                            <p class="text-xs text-gray-500">"Load: " {info.system_info.load_average}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}

// Helper function to fetch device info
async fn fetch_device_info() -> Result<DeviceInfo, String> {
    let response = reqwest::get("/api/device/info")
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: serde_json::Value = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    let data = api_response.get("data")
        .ok_or("Missing data field in API response")?;

    serde_json::from_value(data.clone())
        .map_err(|e| format!("Failed to deserialize device info: {}", e))
}

#[component]
fn HardwareInfo(info: DeviceInfo) -> impl IntoView {
    view! {
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"Hardware Information"</h3>

            <div class="space-y-4">
                <div class="border-b border-gray-100 pb-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">"CPU"</h4>
                    <dl class="space-y-1">
                        <div class="flex justify-between text-sm">
                            <dt class="text-gray-500">"Model:"</dt>
                            <dd class="text-gray-900 font-medium">{info.hardware_info.cpu_model}</dd>
                        </div>
                        <div class="flex justify-between text-sm">
                            <dt class="text-gray-500">"Cores:"</dt>
                            <dd class="text-gray-900 font-medium">{info.hardware_info.cpu_cores}</dd>
                        </div>
                        <div class="flex justify-between text-sm">
                            <dt class="text-gray-500">"Frequency:"</dt>
                            <dd class="text-gray-900 font-medium">{info.hardware_info.cpu_frequency}</dd>
                        </div>
                        {info.hardware_info.cpu_temperature.map(|temp| view! {
                            <div class="flex justify-between text-sm">
                                <dt class="text-gray-500">"Temperature:"</dt>
                                <dd class="text-gray-900 font-medium">{format!("{:.1}°C", temp)}</dd>
                            </div>
                        })}
                    </dl>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">"System"</h4>
                    <dl class="space-y-1">
                        <div class="flex justify-between text-sm">
                            <dt class="text-gray-500">"Kernel:"</dt>
                            <dd class="text-gray-900 font-medium">{info.system_info.kernel_version}</dd>
                        </div>
                        <div class="flex justify-between text-sm">
                            <dt class="text-gray-500">"Target:"</dt>
                            <dd class="text-gray-900 font-medium">{info.hardware_info.target}</dd>
                        </div>
                        <div class="flex justify-between text-sm">
                            <dt class="text-gray-500">"Board:"</dt>
                            <dd class="text-gray-900 font-medium">{info.hardware_info.board_name}</dd>
                        </div>
                        <div class="flex justify-between text-sm">
                            <dt class="text-gray-500">"Load Average:"</dt>
                            <dd class="text-gray-900 font-medium">
                                {info.system_info.load_average.clone()}
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    }
}

#[component]
fn MemoryFlashInfo(info: DeviceInfo) -> impl IntoView {
    let memory_usage_percent = (info.memory_info.total_ram - info.memory_info.available_ram) as f64 / info.memory_info.total_ram as f64 * 100.0;
    let flash_usage_percent = info.storage_info.used_flash as f64 / info.storage_info.total_flash as f64 * 100.0;

    view! {
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"Memory & Storage"</h3>

            <div class="space-y-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">"Memory"</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm text-gray-600">
                            <span>"Usage"</span>
                            <span>{format!("{:.1}%", memory_usage_percent)}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div
                                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style:width=format!("{}%", memory_usage_percent)
                            ></div>
                        </div>
                        <dl class="grid grid-cols-2 gap-2 text-xs text-gray-500 mt-2">
                            <div class="flex justify-between">
                                <dt>"Total:"</dt>
                                <dd>{format_bytes(info.memory_info.total_ram)}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt>"Available:"</dt>
                                <dd>{format_bytes(info.memory_info.available_ram)}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt>"Free:"</dt>
                                <dd>{format_bytes(info.memory_info.free_ram)}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt>"Cached:"</dt>
                                <dd>{format_bytes(info.memory_info.cached_ram)}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">"Flash Storage"</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm text-gray-600">
                            <span>"Usage"</span>
                            <span>{format!("{:.1}%", flash_usage_percent)}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div
                                class={format!("h-2 rounded-full transition-all duration-300 {}",
                                    if flash_usage_percent > 90.0 { "bg-red-600" }
                                    else if flash_usage_percent > 75.0 { "bg-yellow-600" }
                                    else { "bg-green-600" }
                                )}
                                style:width=format!("{}%", flash_usage_percent)
                            ></div>
                        </div>
                        <dl class="grid grid-cols-2 gap-2 text-xs text-gray-500 mt-2">
                            <div class="flex justify-between">
                                <dt>"Total:"</dt>
                                <dd>{format_bytes(info.storage_info.total_flash)}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt>"Available:"</dt>
                                <dd>{format_bytes(info.storage_info.available_flash)}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt>"Used:"</dt>
                                <dd>{format_bytes(info.storage_info.used_flash)}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt>"Filesystem:"</dt>
                                <dd>{info.storage_info.filesystems.first().map(|fs| fs.filesystem_type.clone()).unwrap_or_else(|| "Unknown".to_string())}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    }
}

#[component]
fn NetworkDevices(devices: Vec<NetworkDevice>) -> impl IntoView {
    view! {
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"Network Devices"</h3>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"Interface"</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"MAC Address"</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"Type"</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"Status"</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"MTU"</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"Traffic"</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {devices.into_iter().map(|device| {
                            view! {
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {device.name}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-mono">
                                        {device.mac_address}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        {device.device_type.clone()}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class={format!("inline-flex px-2 py-1 text-xs font-semibold rounded-full {}",
                                            if device.status == "up" { "bg-green-100 text-green-800" } else { "bg-red-100 text-red-800" }
                                        )}>
                                            {device.status.clone()}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        {format!("MTU: {}", device.mtu)}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        {format!("RX: {} / TX: {}", device.rx_bytes, device.tx_bytes)}
                                    </td>
                                </tr>
                            }
                        }).collect::<Vec<_>>()}
                    </tbody>
                </table>
            </div>
        </div>
    }
}

// Helper function to format bytes
fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}
