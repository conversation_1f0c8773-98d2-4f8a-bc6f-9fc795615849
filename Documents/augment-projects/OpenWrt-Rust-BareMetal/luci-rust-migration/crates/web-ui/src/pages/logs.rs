use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LogLevel {
    Emergency,
    Alert,
    Critical,
    Error,
    Warning,
    Notice,
    Info,
    Debug,
}

impl LogLevel {
    fn to_string(&self) -> &'static str {
        match self {
            LogLevel::Emergency => "EMERG",
            LogLevel::Alert => "ALERT",
            LogLevel::Critical => "CRIT",
            LogLevel::Error => "ERROR",
            LogLevel::Warning => "WARN",
            LogLevel::Notice => "NOTICE",
            LogLevel::Info => "INFO",
            LogLevel::Debug => "DEBUG",
        }
    }

    fn color_class(&self) -> &'static str {
        match self {
            LogLevel::Emergency | LogLevel::Alert | LogLevel::Critical => "text-red-600 bg-red-50",
            LogLevel::Error => "text-red-500 bg-red-50",
            LogLevel::Warning => "text-yellow-600 bg-yellow-50",
            LogLevel::Notice => "text-blue-600 bg-blue-50",
            LogLevel::Info => "text-green-600 bg-green-50",
            LogLevel::Debug => "text-gray-600 bg-gray-50",
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub timestamp: String,
    pub level: LogLevel,
    pub facility: String,
    pub process: String,
    pub pid: Option<u32>,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogFilter {
    pub level: Option<LogLevel>,
    pub facility: Option<String>,
    pub process: Option<String>,
    pub search_text: String,
    pub max_entries: usize,
}

impl Default for LogFilter {
    fn default() -> Self {
        Self {
            level: None,
            facility: None,
            process: None,
            search_text: String::new(),
            max_entries: 100,
        }
    }
}

#[component]
pub fn LogsPage() -> impl IntoView {
    let (log_entries, set_log_entries) = create_signal(Vec::<LogEntry>::new());
    let (loading, set_loading) = create_signal(true);
    let (error, set_error) = create_signal(None::<String>);
    let (filter, set_filter) = create_signal(LogFilter::default());
    let (auto_refresh, set_auto_refresh) = create_signal(true);
    let (available_facilities, set_available_facilities) = create_signal(Vec::<String>::new());
    let (available_processes, set_available_processes) = create_signal(Vec::<String>::new());

    // Auto-refresh every 5 seconds if enabled
    create_effect(move |_| {
        if auto_refresh.get() {
            set_interval(
                move || {
                    spawn_local(async move {
                        match fetch_logs(filter.get()).await {
                            Ok((logs, facilities, processes)) => {
                                set_log_entries.set(logs);
                                set_available_facilities.set(facilities);
                                set_available_processes.set(processes);
                                set_loading.set(false);
                                set_error.set(None);
                            }
                            Err(e) => {
                                set_error.set(Some(format!("Failed to fetch logs: {}", e)));
                                set_loading.set(false);
                            }
                        }
                    });
                },
                Duration::from_secs(5),
            );
        }
    });

    // Initial load
    create_effect(move |_| {
        spawn_local(async move {
            match fetch_logs(filter.get()).await {
                Ok((logs, facilities, processes)) => {
                    set_log_entries.set(logs);
                    set_available_facilities.set(facilities);
                    set_available_processes.set(processes);
                    set_loading.set(false);
                    set_error.set(None);
                }
                Err(e) => {
                    set_error.set(Some(format!("Failed to fetch logs: {}", e)));
                    set_loading.set(false);
                }
            }
        });
    });

    let filtered_logs = move || {
        let logs = log_entries.get();
        let current_filter = filter.get();

        logs.into_iter()
            .filter(|entry| {
                // Level filter
                if let Some(ref level) = current_filter.level {
                    if entry.level != *level {
                        return false;
                    }
                }
                
                // Facility filter
                if let Some(ref facility) = current_filter.facility {
                    if entry.facility != *facility {
                        return false;
                    }
                }
                
                // Process filter
                if let Some(ref process) = current_filter.process {
                    if entry.process != *process {
                        return false;
                    }
                }
                
                // Search text filter
                if !current_filter.search_text.is_empty() {
                    let search_lower = current_filter.search_text.to_lowercase();
                    if !entry.message.to_lowercase().contains(&search_lower) &&
                       !entry.process.to_lowercase().contains(&search_lower) &&
                       !entry.facility.to_lowercase().contains(&search_lower) {
                        return false;
                    }
                }
                
                true
            })
            .take(current_filter.max_entries)
            .collect::<Vec<_>>()
    };

    view! {
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">
                        "System Logs"
                    </h1>
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                prop:checked=auto_refresh.get()
                                on:change=move |ev| {
                                    set_auto_refresh.set(event_target_checked(&ev));
                                }
                            />
                            <span class="text-sm text-gray-600">"Auto-refresh"</span>
                        </label>
                        {move || {
                            if auto_refresh.get() {
                                view! {
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                        <span class="text-sm text-gray-600">"5s"</span>
                                    </div>
                                }.into_view()
                            } else {
                                view! {}.into_view()
                            }
                        }}
                    </div>
                </div>

                <LogFilters 
                    filter=filter
                    set_filter=set_filter
                    available_facilities=available_facilities.get()
                    available_processes=available_processes.get()
                />

                {move || {
                    if loading.get() {
                        view! {
                            <div class="flex items-center justify-center py-12">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <span class="ml-2 text-gray-600">"Loading logs..."</span>
                            </div>
                        }.into_view()
                    } else if let Some(err) = error.get() {
                        view! {
                            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">"Error"</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            {err}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }.into_view()
                    } else {
                        let logs = filtered_logs();
                        view! {
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm text-gray-600">
                                        {format!("Showing {} log entries", logs.len())}
                                    </p>
                                    <button
                                        class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        on:click=move |_| {
                                            spawn_local(async move {
                                                match fetch_logs(filter.get()).await {
                                                    Ok((logs, facilities, processes)) => {
                                                        set_log_entries.set(logs);
                                                        set_available_facilities.set(facilities);
                                                        set_available_processes.set(processes);
                                                        set_error.set(None);
                                                    }
                                                    Err(e) => {
                                                        set_error.set(Some(format!("Failed to refresh logs: {}", e)));
                                                    }
                                                }
                                            });
                                        }
                                    >
                                        "Refresh"
                                    </button>
                                </div>
                                <LogTable logs=logs />
                            </div>
                        }.into_view()
                    }
                }}
            </div>
        </div>
    }
}

#[component]
fn LogFilters(
    filter: ReadSignal<LogFilter>,
    set_filter: WriteSignal<LogFilter>,
    available_facilities: Vec<String>,
    available_processes: Vec<String>,
) -> impl IntoView {
    view! {
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="text-sm font-medium text-gray-900 mb-3">"Filters"</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Log Level"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.level = if value.is_empty() {
                                None
                            } else {
                                match value.as_str() {
                                    "EMERG" => Some(LogLevel::Emergency),
                                    "ALERT" => Some(LogLevel::Alert),
                                    "CRIT" => Some(LogLevel::Critical),
                                    "ERROR" => Some(LogLevel::Error),
                                    "WARN" => Some(LogLevel::Warning),
                                    "NOTICE" => Some(LogLevel::Notice),
                                    "INFO" => Some(LogLevel::Info),
                                    "DEBUG" => Some(LogLevel::Debug),
                                    _ => None,
                                }
                            };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="">"All Levels"</option>
                        <option value="EMERG">"Emergency"</option>
                        <option value="ALERT">"Alert"</option>
                        <option value="CRIT">"Critical"</option>
                        <option value="ERROR">"Error"</option>
                        <option value="WARN">"Warning"</option>
                        <option value="NOTICE">"Notice"</option>
                        <option value="INFO">"Info"</option>
                        <option value="DEBUG">"Debug"</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Facility"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.facility = if value.is_empty() { None } else { Some(value) };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="">"All Facilities"</option>
                        {available_facilities.into_iter().map(|facility| {
                            view! {
                                <option value=facility.clone()>{facility}</option>
                            }
                        }).collect::<Vec<_>>()}
                    </select>
                </div>
                
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Process"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.process = if value.is_empty() { None } else { Some(value) };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="">"All Processes"</option>
                        {available_processes.into_iter().map(|process| {
                            view! {
                                <option value=process.clone()>{process}</option>
                            }
                        }).collect::<Vec<_>>()}
                    </select>
                </div>
                
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Search"</label>
                    <input
                        type="text"
                        placeholder="Search logs..."
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        prop:value=move || filter.get().search_text
                        on:input=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.search_text = value;
                            set_filter.set(current_filter);
                        }
                    />
                </div>
            </div>
        </div>
    }
}

// Helper function to fetch logs
async fn fetch_logs(filter: LogFilter) -> Result<(Vec<LogEntry>, Vec<String>, Vec<String>), String> {
    // Build query parameters
    let mut query_params = vec![];

    if let Some(level) = &filter.level {
        let level_str = match level {
            LogLevel::Emergency => "Emergency",
            LogLevel::Alert => "Alert",
            LogLevel::Critical => "Critical",
            LogLevel::Error => "Error",
            LogLevel::Warning => "Warning",
            LogLevel::Notice => "Notice",
            LogLevel::Info => "Info",
            LogLevel::Debug => "Debug",
        };
        query_params.push(format!("level={}", level_str));
    }

    if let Some(facility) = &filter.facility {
        query_params.push(format!("facility={}", facility));
    }

    if let Some(process) = &filter.process {
        query_params.push(format!("process={}", process));
    }

    if !filter.search_text.is_empty() {
        query_params.push(format!("search_text={}", urlencoding::encode(&filter.search_text)));
    }

    query_params.push("limit=1000".to_string());

    let query_string = if query_params.is_empty() {
        String::new()
    } else {
        format!("?{}", query_params.join("&"))
    };

    let url = format!("/api/logs{}", query_string);

    let response = reqwest::get(&url)
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: serde_json::Value = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    let data = api_response.get("data")
        .ok_or("Missing data field in API response")?;

    let log_response: LogResponse = serde_json::from_value(data.clone())
        .map_err(|e| format!("Failed to deserialize log response: {}", e))?;

    Ok((log_response.logs, log_response.available_facilities, log_response.available_processes))
}

#[derive(Debug, Serialize, Deserialize)]
struct LogResponse {
    logs: Vec<LogEntry>,
    available_facilities: Vec<String>,
    available_processes: Vec<String>,
}

#[component]
fn LogTable(logs: Vec<LogEntry>) -> impl IntoView {
    let is_empty = logs.is_empty();
    view! {
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                "Timestamp"
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                "Level"
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                "Facility"
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                "Process"
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                "Message"
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {logs.into_iter().map(|entry| {
                            view! {
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-mono">
                                        {entry.timestamp}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class={format!("inline-flex px-2 py-1 text-xs font-semibold rounded-full {}", entry.level.color_class())}>
                                            {entry.level.to_string()}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        {entry.facility}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        <div class="flex items-center">
                                            <span>{entry.process}</span>
                                            {entry.pid.map(|pid| view! {
                                                <span class="ml-1 text-xs text-gray-500">
                                                    "[" {pid} "]"
                                                </span>
                                            })}
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-900">
                                        <div class="max-w-md">
                                            <p class="break-words">{entry.message}</p>
                                        </div>
                                    </td>
                                </tr>
                            }
                        }).collect::<Vec<_>>()}
                    </tbody>
                </table>
            </div>

            {if is_empty {
                view! {
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p class="mt-2 text-sm text-gray-500">"No log entries found matching the current filters"</p>
                    </div>
                }.into_view()
            } else {
                view! {}.into_view()
            }}
        </div>
    }
}
