use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NetworkStats {
    pub interfaces: Vec<InterfaceStats>,
    pub total_rx_bytes: u64,
    pub total_tx_bytes: u64,
    pub total_rx_packets: u64,
    pub total_tx_packets: u64,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct InterfaceStats {
    pub name: String,
    pub status: String,
    pub ip_address: Option<String>,
    pub mac_address: String,
    pub rx_bytes: u64,
    pub tx_bytes: u64,
    pub rx_packets: u64,
    pub tx_packets: u64,
    pub rx_errors: u64,
    pub tx_errors: u64,
    pub rx_dropped: u64,
    pub tx_dropped: u64,
    pub speed: Option<u64>, // Mbps
    pub duplex: Option<String>,
    pub mtu: u32,
    pub history: Vec<TrafficPoint>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TrafficPoint {
    pub timestamp: u64,
    pub rx_bytes_per_sec: u64,
    pub tx_bytes_per_sec: u64,
}

#[component]
pub fn NetworkMonitorPage() -> impl IntoView {
    let (network_stats, set_network_stats) = create_signal(None::<NetworkStats>);
    let (loading, set_loading) = create_signal(true);
    let (error, set_error) = create_signal(None::<String>);
    let (selected_interface, set_selected_interface) = create_signal(None::<String>);

    // Auto-refresh every 2 seconds
    create_effect(move |_| {
        let interval = set_interval(
            move || {
                spawn_local(async move {
                    match fetch_network_stats().await {
                        Ok(stats) => {
                            set_network_stats(Some(stats));
                            set_loading(false);
                            set_error(None);
                        }
                        Err(e) => {
                            set_error(Some(format!("Failed to fetch network stats: {}", e)));
                            set_loading(false);
                        }
                    }
                });
            },
            Duration::from_secs(2),
        );

        on_cleanup(move || clear_interval(interval));
    });

    view! {
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">
                        "Network Traffic Monitor"
                    </h1>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">"Auto-refresh: 2s"</span>
                    </div>
                </div>

                {move || {
                    if loading() {
                        view! {
                            <div class="flex items-center justify-center py-12">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <span class="ml-2 text-gray-600">"Loading network statistics..."</span>
                            </div>
                        }.into_view()
                    } else if let Some(err) = error() {
                        view! {
                            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">"Error"</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            {err}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }.into_view()
                    } else if let Some(stats) = network_stats() {
                        view! {
                            <div class="space-y-6">
                                <NetworkOverview stats=stats.clone() />
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <InterfaceSelector 
                                        interfaces=stats.interfaces.clone()
                                        selected=selected_interface
                                        set_selected=set_selected_interface
                                    />
                                    <TrafficSummary stats=stats.clone() />
                                </div>
                                <InterfaceDetails 
                                    interfaces=stats.interfaces
                                    selected=selected_interface()
                                />
                            </div>
                        }.into_view()
                    } else {
                        view! {
                            <div class="text-center py-12 text-gray-500">
                                "No network data available"
                            </div>
                        }.into_view()
                    }
                }}
            </div>
        </div>
    }
}

#[component]
fn NetworkOverview(stats: NetworkStats) -> impl IntoView {
    let active_interfaces = stats.interfaces.iter().filter(|i| i.status == "up").count();
    let total_interfaces = stats.interfaces.len();

    view! {
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-blue-600">"Interfaces"</p>
                        <p class="text-2xl font-bold text-blue-900">{format!("{}/{}", active_interfaces, total_interfaces)}</p>
                    </div>
                </div>
            </div>

            <div class="bg-green-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-green-600">"Total RX"</p>
                        <p class="text-2xl font-bold text-green-900">{format_bytes(stats.total_rx_bytes)}</p>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-yellow-600">"Total TX"</p>
                        <p class="text-2xl font-bold text-yellow-900">{format_bytes(stats.total_tx_bytes)}</p>
                    </div>
                </div>
            </div>

            <div class="bg-purple-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-purple-600">"Total Packets"</p>
                        <p class="text-2xl font-bold text-purple-900">{format_number(stats.total_rx_packets + stats.total_tx_packets)}</p>
                    </div>
                </div>
            </div>
        </div>
    }
}

#[component]
fn InterfaceSelector(
    interfaces: Vec<InterfaceStats>,
    selected: ReadSignal<Option<String>>,
    set_selected: WriteSignal<Option<String>>,
) -> impl IntoView {
    view! {
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"Network Interfaces"</h3>
            
            <div class="space-y-2">
                {interfaces.into_iter().map(|interface| {
                    let interface_name = interface.name.clone();
                    let is_selected = move || selected().as_ref() == Some(&interface_name);
                    
                    view! {
                        <div 
                            class={move || format!("p-3 rounded-lg border cursor-pointer transition-colors {}",
                                if is_selected() { 
                                    "border-blue-500 bg-blue-50" 
                                } else { 
                                    "border-gray-200 hover:border-gray-300 hover:bg-gray-50" 
                                }
                            )}
                            on:click={
                                let interface_name = interface.name.clone();
                                move |_| set_selected(Some(interface_name.clone()))
                            }
                        >
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class={format!("w-3 h-3 rounded-full {}",
                                        if interface.status == "up" { "bg-green-500" } else { "bg-red-500" }
                                    )}></div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{interface.name}</p>
                                        <p class="text-xs text-gray-500">
                                            {interface.ip_address.unwrap_or_else(|| "No IP".to_string())}
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs text-gray-500">"RX: " {format_bytes(interface.rx_bytes)}</p>
                                    <p class="text-xs text-gray-500">"TX: " {format_bytes(interface.tx_bytes)}</p>
                                </div>
                            </div>
                        </div>
                    }
                }).collect::<Vec<_>>()}
            </div>
        </div>
    }
}

#[component]
fn TrafficSummary(stats: NetworkStats) -> impl IntoView {
    let rx_rate = stats.interfaces.iter()
        .filter_map(|i| i.history.last())
        .map(|p| p.rx_bytes_per_sec)
        .sum::<u64>();
    
    let tx_rate = stats.interfaces.iter()
        .filter_map(|i| i.history.last())
        .map(|p| p.tx_bytes_per_sec)
        .sum::<u64>();

    view! {
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"Current Traffic"</h3>
            
            <div class="space-y-4">
                <div>
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span>"Download Rate"</span>
                        <span>{format!("{}/s", format_bytes(rx_rate))}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="bg-green-600 h-3 rounded-full transition-all duration-300" style:width="45%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span>"Upload Rate"</span>
                        <span>{format!("{}/s", format_bytes(tx_rate))}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" style:width="25%"></div>
                    </div>
                </div>
                
                <div class="pt-2 border-t border-gray-100">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="text-gray-500">"Total Downloaded"</p>
                            <p class="font-medium text-gray-900">{format_bytes(stats.total_rx_bytes)}</p>
                        </div>
                        <div>
                            <p class="text-gray-500">"Total Uploaded"</p>
                            <p class="font-medium text-gray-900">{format_bytes(stats.total_tx_bytes)}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}

// Helper functions
fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

fn format_number(num: u64) -> String {
    if num >= 1_000_000_000 {
        format!("{:.1}B", num as f64 / 1_000_000_000.0)
    } else if num >= 1_000_000 {
        format!("{:.1}M", num as f64 / 1_000_000.0)
    } else if num >= 1_000 {
        format!("{:.1}K", num as f64 / 1_000.0)
    } else {
        num.to_string()
    }
}

async fn fetch_network_stats() -> Result<NetworkStats, String> {
    // Mock implementation - in real system would call backend API
    Ok(NetworkStats {
        total_rx_bytes: 1073741824, // 1GB
        total_tx_bytes: 536870912,  // 512MB
        total_rx_packets: 1048576,
        total_tx_packets: 524288,
        interfaces: vec![
            InterfaceStats {
                name: "eth0".to_string(),
                status: "up".to_string(),
                ip_address: Some("***********".to_string()),
                mac_address: "aa:bb:cc:dd:ee:ff".to_string(),
                rx_bytes: 536870912,
                tx_bytes: 268435456,
                rx_packets: 524288,
                tx_packets: 262144,
                rx_errors: 0,
                tx_errors: 0,
                rx_dropped: 0,
                tx_dropped: 0,
                speed: Some(1000),
                duplex: Some("full".to_string()),
                mtu: 1500,
                history: vec![
                    TrafficPoint {
                        timestamp: 1234567890,
                        rx_bytes_per_sec: 1048576,
                        tx_bytes_per_sec: 524288,
                    }
                ],
            },
            InterfaceStats {
                name: "wlan0".to_string(),
                status: "up".to_string(),
                ip_address: Some("***********".to_string()),
                mac_address: "11:22:33:44:55:66".to_string(),
                rx_bytes: 268435456,
                tx_bytes: 134217728,
                rx_packets: 262144,
                tx_packets: 131072,
                rx_errors: 2,
                tx_errors: 1,
                rx_dropped: 0,
                tx_dropped: 0,
                speed: Some(150),
                duplex: None,
                mtu: 1500,
                history: vec![
                    TrafficPoint {
                        timestamp: 1234567890,
                        rx_bytes_per_sec: 524288,
                        tx_bytes_per_sec: 262144,
                    }
                ],
            },
        ],
    })
}

#[component]
fn InterfaceDetails(
    interfaces: Vec<InterfaceStats>,
    selected: Option<String>,
) -> impl IntoView {
    let selected_interface = move || {
        if let Some(ref name) = selected {
            interfaces.iter().find(|i| &i.name == name).cloned()
        } else {
            None
        }
    };

    view! {
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"Interface Details"</h3>

            {move || {
                if let Some(interface) = selected_interface() {
                    view! {
                        <div class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900 mb-3">"Interface Information"</h4>
                                    <dl class="space-y-2">
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"Name:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{interface.name}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"Status:"</dt>
                                            <dd class="text-sm">
                                                <span class={format!("inline-flex px-2 py-1 text-xs font-semibold rounded-full {}",
                                                    if interface.status == "up" { "bg-green-100 text-green-800" } else { "bg-red-100 text-red-800" }
                                                )}>
                                                    {interface.status}
                                                </span>
                                            </dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"IP Address:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">
                                                {interface.ip_address.unwrap_or_else(|| "N/A".to_string())}
                                            </dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"MAC Address:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{interface.mac_address}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"MTU:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{interface.mtu}</dd>
                                        </div>
                                        {interface.speed.map(|speed| view! {
                                            <div class="flex justify-between">
                                                <dt class="text-sm text-gray-500">"Speed:"</dt>
                                                <dd class="text-sm font-medium text-gray-900">{format!("{} Mbps", speed)}</dd>
                                            </div>
                                        })}
                                        {interface.duplex.map(|duplex| view! {
                                            <div class="flex justify-between">
                                                <dt class="text-sm text-gray-500">"Duplex:"</dt>
                                                <dd class="text-sm font-medium text-gray-900">{duplex}</dd>
                                            </div>
                                        })}
                                    </dl>
                                </div>

                                <div>
                                    <h4 class="text-sm font-medium text-gray-900 mb-3">"Traffic Statistics"</h4>
                                    <dl class="space-y-2">
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"RX Bytes:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{format_bytes(interface.rx_bytes)}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"TX Bytes:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{format_bytes(interface.tx_bytes)}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"RX Packets:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{format_number(interface.rx_packets)}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"TX Packets:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{format_number(interface.tx_packets)}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"RX Errors:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{interface.rx_errors}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"TX Errors:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{interface.tx_errors}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"RX Dropped:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{interface.rx_dropped}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm text-gray-500">"TX Dropped:"</dt>
                                            <dd class="text-sm font-medium text-gray-900">{interface.tx_dropped}</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>

                            {if !interface.history.is_empty() {
                                view! {
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 mb-3">"Traffic History"</h4>
                                        <div class="bg-gray-50 rounded-lg p-4">
                                            <div class="text-center text-sm text-gray-500">
                                                "Traffic graph visualization would be implemented here"
                                                <br />
                                                "Current rate: RX " {format!("{}/s", format_bytes(interface.history.last().map(|p| p.rx_bytes_per_sec).unwrap_or(0)))}
                                                " | TX " {format!("{}/s", format_bytes(interface.history.last().map(|p| p.tx_bytes_per_sec).unwrap_or(0)))}
                                            </div>
                                        </div>
                                    </div>
                                }.into_view()
                            } else {
                                view! {}.into_view()
                            }}
                        </div>
                    }.into_view()
                } else {
                    view! {
                        <div class="text-center py-8 text-gray-500">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                            </svg>
                            <p class="mt-2">"Select an interface to view detailed information"</p>
                        </div>
                    }.into_view()
                }
            }}
        </div>
    }
}
