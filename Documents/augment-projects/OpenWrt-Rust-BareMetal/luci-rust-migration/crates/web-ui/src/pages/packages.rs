use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PackageStatus {
    NotInstalled,
    Installed,
    Upgradable,
    Broken,
}

impl PackageStatus {
    fn to_string(&self) -> &'static str {
        match self {
            PackageStatus::NotInstalled => "Not Installed",
            PackageStatus::Installed => "Installed",
            PackageStatus::Upgradable => "Upgradable",
            PackageStatus::Broken => "Broken",
        }
    }

    fn color_class(&self) -> &'static str {
        match self {
            PackageStatus::NotInstalled => "text-gray-600 bg-gray-100",
            PackageStatus::Installed => "text-green-600 bg-green-100",
            PackageStatus::Upgradable => "text-blue-600 bg-blue-100",
            PackageStatus::Broken => "text-red-600 bg-red-100",
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Package {
    pub name: String,
    pub version: String,
    pub available_version: Option<String>,
    pub description: String,
    pub category: String,
    pub size: u64,
    pub installed_size: Option<u64>,
    pub status: PackageStatus,
    pub dependencies: Vec<String>,
    pub conflicts: Vec<String>,
    pub provides: Vec<String>,
    pub maintainer: String,
    pub homepage: Option<String>,
    pub license: String,
    pub architecture: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageFilter {
    pub search_text: String,
    pub category: Option<String>,
    pub status: Option<PackageStatus>,
    pub sort_by: PackageSortBy,
    pub sort_order: SortOrder,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PackageSortBy {
    Name,
    Size,
    Status,
    Category,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SortOrder {
    Ascending,
    Descending,
}

impl Default for PackageFilter {
    fn default() -> Self {
        Self {
            search_text: String::new(),
            category: None,
            status: None,
            sort_by: PackageSortBy::Name,
            sort_order: SortOrder::Ascending,
        }
    }
}

#[component]
pub fn PackagesPage() -> impl IntoView {
    let (packages, set_packages) = create_signal(Vec::<Package>::new());
    let (loading, set_loading) = create_signal(true);
    let (error, set_error) = create_signal(None::<String>);
    let (filter, set_filter) = create_signal(PackageFilter::default());
    let (available_categories, set_available_categories) = create_signal(Vec::<String>::new());
    let (selected_packages, set_selected_packages) = create_signal(Vec::<String>::new());

    // Load packages on mount
    create_effect(move |_| {
        spawn_local(async move {
            match fetch_packages().await {
                Ok((pkgs, categories)) => {
                    set_packages.set(pkgs);
                    set_available_categories.set(categories);
                    set_loading.set(false);
                    set_error.set(None);
                }
                Err(e) => {
                    set_error.set(Some(format!("Failed to fetch packages: {}", e)));
                    set_loading.set(false);
                }
            }
        });
    });

    let filtered_packages = move || {
        let mut pkgs = packages.get();
        let current_filter = filter.get();

        // Apply filters
        pkgs.retain(|pkg| {
            // Search text filter
            if !current_filter.search_text.is_empty() {
                let search_lower = current_filter.search_text.to_lowercase();
                if !pkg.name.to_lowercase().contains(&search_lower) &&
                   !pkg.description.to_lowercase().contains(&search_lower) {
                    return false;
                }
            }

            // Category filter
            if let Some(ref category) = current_filter.category {
                if pkg.category != *category {
                    return false;
                }
            }

            // Status filter
            if let Some(ref status) = current_filter.status {
                if pkg.status != *status {
                    return false;
                }
            }

            true
        });

        // Apply sorting
        pkgs.sort_by(|a, b| {
            let comparison = match current_filter.sort_by {
                PackageSortBy::Name => a.name.cmp(&b.name),
                PackageSortBy::Size => a.size.cmp(&b.size),
                PackageSortBy::Status => a.status.to_string().cmp(b.status.to_string()),
                PackageSortBy::Category => a.category.cmp(&b.category),
            };

            match current_filter.sort_order {
                SortOrder::Ascending => comparison,
                SortOrder::Descending => comparison.reverse(),
            }
        });

        pkgs
    };

    view! {
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">
                        "Package Management"
                    </h1>
                    <div class="flex items-center space-x-4">
                        <button
                            class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            on:click=move |_| {
                                set_loading.set(true);
                                spawn_local(async move {
                                    match fetch_packages().await {
                                        Ok((pkgs, categories)) => {
                                            set_packages.set(pkgs);
                                            set_available_categories.set(categories);
                                            set_loading.set(false);
                                            set_error.set(None);
                                        }
                                        Err(e) => {
                                            set_error.set(Some(format!("Failed to refresh packages: {}", e)));
                                            set_loading.set(false);
                                        }
                                    }
                                });
                            }
                        >
                            "Refresh"
                        </button>
                        <button
                            class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                            disabled=move || selected_packages.get().is_empty()
                            class:opacity-50=move || selected_packages.get().is_empty()
                        >
                            "Install Selected"
                        </button>
                    </div>
                </div>

                <PackageFilters
                    filter=filter
                    set_filter=set_filter
                    available_categories=available_categories.get()
                />

                {move || {
                    if loading.get() {
                        view! {
                            <div class="flex items-center justify-center py-12">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <span class="ml-2 text-gray-600">"Loading packages..."</span>
                            </div>
                        }.into_view()
                    } else if let Some(err) = error.get() {
                        view! {
                            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill_rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip_rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">"Error"</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            {err}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }.into_view()
                    } else {
                        let pkgs = filtered_packages();
                        view! {
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm text-gray-600">
                                        {format!("Showing {} packages", pkgs.len())}
                                    </p>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-600">"Selected:"</span>
                                        <span class="text-sm font-medium text-blue-600">
                                            {move || selected_packages.get().len()}
                                        </span>
                                    </div>
                                </div>
                                <PackageTable
                                    packages=pkgs
                                    selected_packages=selected_packages
                                    set_selected_packages=set_selected_packages
                                />
                            </div>
                        }.into_view()
                    }
                }}
            </div>
        </div>
    }
}

#[component]
fn PackageFilters(
    filter: ReadSignal<PackageFilter>,
    set_filter: WriteSignal<PackageFilter>,
    available_categories: Vec<String>,
) -> impl IntoView {
    view! {
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="text-sm font-medium text-gray-900 mb-3">"Filters & Search"</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div class="lg:col-span-2">
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Search Packages"</label>
                    <input
                        type="text"
                        placeholder="Search by name or description..."
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        prop:value=move || filter.get().search_text
                        on:input=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.search_text = value;
                            set_filter.set(current_filter);
                        }
                    />
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Category"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.category = if value.is_empty() { None } else { Some(value) };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="">"All Categories"</option>
                        {available_categories.into_iter().map(|category| {
                            view! {
                                <option value=category.clone()>{category}</option>
                            }
                        }).collect::<Vec<_>>()}
                    </select>
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Status"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.status = if value.is_empty() {
                                None
                            } else {
                                match value.as_str() {
                                    "NotInstalled" => Some(PackageStatus::NotInstalled),
                                    "Installed" => Some(PackageStatus::Installed),
                                    "Upgradable" => Some(PackageStatus::Upgradable),
                                    "Broken" => Some(PackageStatus::Broken),
                                    _ => None,
                                }
                            };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="">"All Status"</option>
                        <option value="NotInstalled">"Not Installed"</option>
                        <option value="Installed">"Installed"</option>
                        <option value="Upgradable">"Upgradable"</option>
                        <option value="Broken">"Broken"</option>
                    </select>
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Sort By"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.sort_by = match value.as_str() {
                                "Size" => PackageSortBy::Size,
                                "Status" => PackageSortBy::Status,
                                "Category" => PackageSortBy::Category,
                                _ => PackageSortBy::Name,
                            };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="Name">"Name"</option>
                        <option value="Size">"Size"</option>
                        <option value="Status">"Status"</option>
                        <option value="Category">"Category"</option>
                    </select>
                </div>
            </div>
        </div>
    }
}

#[component]
fn PackageTable(
    packages: Vec<Package>,
    selected_packages: ReadSignal<Vec<String>>,
    set_selected_packages: WriteSignal<Vec<String>>,
) -> impl IntoView {
    let format_size = |size: u64| -> String {
        if size < 1024 {
            format!("{} B", size)
        } else if size < 1024 * 1024 {
            format!("{:.1} KB", size as f64 / 1024.0)
        } else {
            format!("{:.1} MB", size as f64 / (1024.0 * 1024.0))
        }
    };

    view! {
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input
                                type="checkbox"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                on:change={
                                    let packages_clone = packages.clone();
                                    move |ev| {
                                        let checked = event_target_checked(&ev);
                                        if checked {
                                            let all_names: Vec<String> = packages_clone.iter()
                                                .filter(|pkg| pkg.status == PackageStatus::NotInstalled || pkg.status == PackageStatus::Upgradable)
                                                .map(|pkg| pkg.name.clone())
                                                .collect();
                                            set_selected_packages.set(all_names);
                                        } else {
                                            set_selected_packages.set(vec![]);
                                        }
                                    }
                                }
                            />
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Package"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Version"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Status"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Size"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Category"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Actions"
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {packages.into_iter().map(|package| {
                        let pkg_name = package.name.clone();
                        let pkg_name_for_selected = pkg_name.clone();
                        let is_selected = move || selected_packages.get().contains(&pkg_name_for_selected);
                        let can_install = package.status == PackageStatus::NotInstalled || package.status == PackageStatus::Upgradable;
                        let can_remove = package.status == PackageStatus::Installed || package.status == PackageStatus::Upgradable;

                        view! {
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {if can_install {
                                        view! {
                                            <input
                                                type="checkbox"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                prop:checked=is_selected
                                                on:change=move |ev| {
                                                    let checked = event_target_checked(&ev);
                                                    let mut current_selected = selected_packages.get();
                                                    if checked {
                                                        if !current_selected.contains(&pkg_name) {
                                                            current_selected.push(pkg_name.clone());
                                                        }
                                                    } else {
                                                        current_selected.retain(|name| name != &pkg_name);
                                                    }
                                                    set_selected_packages.set(current_selected);
                                                }
                                            />
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{package.name.clone()}</div>
                                    <div class="text-sm text-gray-500 max-w-xs truncate">{package.description}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div>{package.version}</div>
                                    {if let Some(available) = &package.available_version {
                                        view! {
                                            <div class="text-xs text-blue-600">
                                                {format!("→ {}", available)}
                                            </div>
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class={format!("inline-flex px-2 py-1 text-xs font-semibold rounded-full {}", package.status.color_class())}>
                                        {package.status.to_string()}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {format_size(package.size)}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {package.category}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    {if can_install {
                                        view! {
                                            <button class="text-blue-600 hover:text-blue-900">
                                                {if package.status == PackageStatus::Upgradable { "Upgrade" } else { "Install" }}
                                            </button>
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }}
                                    {if can_remove {
                                        view! {
                                            <button class="text-red-600 hover:text-red-900 ml-2">
                                                "Remove"
                                            </button>
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }}
                                    <button class="text-gray-600 hover:text-gray-900 ml-2">
                                        "Details"
                                    </button>
                                </td>
                            </tr>
                        }
                    }).collect::<Vec<_>>()}
                </tbody>
            </table>
        </div>
    }
}

// Helper function to fetch packages
async fn fetch_packages() -> Result<(Vec<Package>, Vec<String>), String> {
    // Mock implementation - in real system would call opkg backend
    let packages = vec![
        Package {
            name: "curl".to_string(),
            version: "7.88.1-1".to_string(),
            available_version: Some("7.88.1-2".to_string()),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            category: "Network".to_string(),
            size: 245760,
            installed_size: Some(245760),
            status: PackageStatus::Upgradable,
            dependencies: vec!["libcurl4".to_string(), "ca-certificates".to_string()],
            conflicts: vec![],
            provides: vec![],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://curl.se/".to_string()),
            license: "MIT".to_string(),
            architecture: "mips_24kc".to_string(),
        },
        Package {
            name: "nginx".to_string(),
            version: "1.24.0-1".to_string(),
            available_version: None,
            description: "HTTP and reverse proxy server".to_string(),
            category: "Network".to_string(),
            size: 524288,
            installed_size: None,
            status: PackageStatus::NotInstalled,
            dependencies: vec!["libpcre".to_string(), "zlib".to_string()],
            conflicts: vec!["lighttpd".to_string()],
            provides: vec!["httpd".to_string()],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://nginx.org/".to_string()),
            license: "BSD-2-Clause".to_string(),
            architecture: "mips_24kc".to_string(),
        },
        Package {
            name: "dropbear".to_string(),
            version: "2022.83-1".to_string(),
            available_version: None,
            description: "Small SSH server and client".to_string(),
            category: "Network".to_string(),
            size: 114688,
            installed_size: Some(114688),
            status: PackageStatus::Installed,
            dependencies: vec!["zlib".to_string()],
            conflicts: vec!["openssh-server".to_string()],
            provides: vec!["ssh-server".to_string()],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://matt.ucc.asn.au/dropbear/dropbear.html".to_string()),
            license: "MIT".to_string(),
            architecture: "mips_24kc".to_string(),
        },
        Package {
            name: "htop".to_string(),
            version: "3.2.2-1".to_string(),
            available_version: None,
            description: "Interactive process viewer".to_string(),
            category: "Utilities".to_string(),
            size: 98304,
            installed_size: None,
            status: PackageStatus::NotInstalled,
            dependencies: vec!["libncurses".to_string()],
            conflicts: vec![],
            provides: vec![],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://htop.dev/".to_string()),
            license: "GPL-2.0".to_string(),
            architecture: "mips_24kc".to_string(),
        },
        Package {
            name: "vim".to_string(),
            version: "9.0.1568-1".to_string(),
            available_version: None,
            description: "Vi IMproved - enhanced vi editor".to_string(),
            category: "Utilities".to_string(),
            size: 1048576,
            installed_size: Some(1048576),
            status: PackageStatus::Installed,
            dependencies: vec!["libncurses".to_string()],
            conflicts: vec!["nano".to_string()],
            provides: vec!["editor".to_string()],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://www.vim.org/".to_string()),
            license: "Vim".to_string(),
            architecture: "mips_24kc".to_string(),
        },
        Package {
            name: "kmod-usb-storage".to_string(),
            version: "5.15.134-1".to_string(),
            available_version: Some("5.15.135-1".to_string()),
            description: "Kernel module for USB storage devices".to_string(),
            category: "Kernel".to_string(),
            size: 32768,
            installed_size: Some(32768),
            status: PackageStatus::Upgradable,
            dependencies: vec!["kernel".to_string()],
            conflicts: vec![],
            provides: vec![],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: None,
            license: "GPL-2.0".to_string(),
            architecture: "mips_24kc".to_string(),
        },
        Package {
            name: "broken-package".to_string(),
            version: "1.0.0-1".to_string(),
            available_version: None,
            description: "A package with broken dependencies".to_string(),
            category: "System".to_string(),
            size: 16384,
            installed_size: Some(16384),
            status: PackageStatus::Broken,
            dependencies: vec!["missing-lib".to_string()],
            conflicts: vec![],
            provides: vec![],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: None,
            license: "GPL-2.0".to_string(),
            architecture: "mips_24kc".to_string(),
        },
    ];

    let categories = vec![
        "Network".to_string(),
        "System".to_string(),
        "Utilities".to_string(),
        "Kernel".to_string()
    ];

    Ok((packages, categories))
}
