use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};
use std::time::Duration;
use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, PackageStats};

// Re-export PackageStatus from package-manager crate for consistency
pub use luci_package_manager::PackageStatus;

// Helper functions for PackageStatus display
fn package_status_to_display_string(status: &PackageStatus) -> &'static str {
    match status {
        PackageStatus::NotInstalled => "Not Installed",
        PackageStatus::Installed => "Installed",
        PackageStatus::Upgradable => "Upgradable",
        PackageStatus::Broken => "Broken",
        PackageStatus::ConfigFiles => "Config Files",
    }
}

fn package_status_color_class(status: &PackageStatus) -> &'static str {
    match status {
        PackageStatus::NotInstalled => "text-gray-600 bg-gray-100",
        PackageStatus::Installed => "text-green-600 bg-green-100",
        PackageStatus::Upgradable => "text-blue-600 bg-blue-100",
        PackageStatus::Broken => "text-red-600 bg-red-100",
        PackageStatus::ConfigFiles => "text-yellow-600 bg-yellow-100",
    }
}

// Utility function to format file sizes
fn format_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

// Convert PackageInfo to a UI-friendly format
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Package {
    pub name: String,
    pub version: String,
    pub description: String,
    pub category: String,
    pub section: String,
    pub size: u64,
    pub installed_size: u64,
    pub status: PackageStatus,
    pub dependencies: Vec<String>,
    pub conflicts: Vec<String>,
    pub provides: Vec<String>,
    pub maintainer: String,
    pub homepage: Option<String>,
    pub architecture: String,
    pub repository: String,
    pub priority: String,
}

impl From<PackageInfo> for Package {
    fn from(info: PackageInfo) -> Self {
        Self {
            name: info.name,
            version: info.version,
            description: info.description,
            category: info.category,
            section: info.section,
            size: info.download_size,
            installed_size: info.installed_size,
            status: info.status,
            dependencies: info.depends,
            conflicts: info.conflicts,
            provides: info.provides,
            maintainer: info.maintainer,
            homepage: info.homepage,
            architecture: info.architecture,
            repository: info.repository,
            priority: info.priority.to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageFilter {
    pub search_text: String,
    pub category: Option<String>,
    pub section: Option<String>,
    pub status: Option<PackageStatus>,
    pub sort_by: PackageSortBy,
    pub sort_order: SortOrder,
    pub installed_only: bool,
    pub upgradable_only: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PackageSortBy {
    Name,
    Size,
    Status,
    Category,
    Section,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SortOrder {
    Ascending,
    Descending,
}

impl Default for PackageFilter {
    fn default() -> Self {
        Self {
            search_text: String::new(),
            category: None,
            section: None,
            status: None,
            sort_by: PackageSortBy::Name,
            sort_order: SortOrder::Ascending,
            installed_only: false,
            upgradable_only: false,
        }
    }
}

// Pagination support
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackagePagination {
    pub current_page: usize,
    pub page_size: usize,
    pub total_items: usize,
}

impl Default for PackagePagination {
    fn default() -> Self {
        Self {
            current_page: 1,
            page_size: 50,
            total_items: 0,
        }
    }
}

impl PackagePagination {
    pub fn total_pages(&self) -> usize {
        (self.total_items + self.page_size - 1) / self.page_size
    }

    pub fn start_index(&self) -> usize {
        (self.current_page - 1) * self.page_size
    }

    pub fn end_index(&self) -> usize {
        std::cmp::min(self.start_index() + self.page_size, self.total_items)
    }

    pub fn start_item(&self) -> usize {
        if self.total_items == 0 {
            0
        } else {
            self.start_index() + 1
        }
    }

    pub fn end_item(&self) -> usize {
        self.end_index()
    }
}

#[component]
pub fn PackagesPage() -> impl IntoView {
    let (packages, set_packages) = create_signal(Vec::<Package>::new());
    let (all_packages, set_all_packages) = create_signal(Vec::<Package>::new());
    let (loading, set_loading) = create_signal(true);
    let (error, set_error) = create_signal(None::<String>);
    let (filter, set_filter) = create_signal(PackageFilter::default());
    let (pagination, set_pagination) = create_signal(PackagePagination::default());
    let (available_categories, set_available_categories) = create_signal(Vec::<String>::new());
    let (available_sections, set_available_sections) = create_signal(Vec::<String>::new());
    let (selected_packages, set_selected_packages) = create_signal(Vec::<String>::new());
    let (package_stats, set_package_stats) = create_signal(None::<PackageStats>);
    let (selected_package_details, set_selected_package_details) = create_signal(None::<Package>);

    // State for confirmation dialog
    let (show_confirmation, set_show_confirmation) = create_signal(false);
    let (pending_operation, set_pending_operation) = create_signal(None::<(String, String)>); // (operation_type, package_name)
    let (global_operation_loading, set_global_operation_loading) = create_signal(false);
    let (global_operation_error, set_global_operation_error) = create_signal(None::<String>);
    let (global_operation_success, set_global_operation_success) = create_signal(None::<String>);

    // State for removal safety checking
    let (removal_safety_info, set_removal_safety_info) = create_signal(None::<RemovalSafetyInfo>);
    let (checking_removal_safety, set_checking_removal_safety) = create_signal(false);

    // State for updates management
    let (available_updates, set_available_updates) = create_signal(Vec::<PackageUpdate>::new());
    let (updates_loading, set_updates_loading) = create_signal(false);
    let (updates_error, set_updates_error) = create_signal(None::<String>);
    let (selected_updates, set_selected_updates) = create_signal(Vec::<String>::new());
    let (show_updates_tab, set_show_updates_tab) = create_signal(false);
    let (updating_packages, set_updating_packages) = create_signal(false);

    // Load packages on mount
    create_effect(move |_| {
        spawn_local(async move {
            match fetch_packages().await {
                Ok((pkgs, categories, sections, stats)) => {
                    set_all_packages.set(pkgs.clone());
                    set_packages.set(pkgs);
                    set_available_categories.set(categories);
                    set_available_sections.set(sections);
                    set_package_stats.set(Some(stats));
                    set_loading.set(false);
                    set_error.set(None);
                }
                Err(e) => {
                    set_error.set(Some(format!("Failed to fetch packages: {}", e)));
                    set_loading.set(false);
                }
            }
        });
    });

    // Apply filters and pagination
    create_effect(move |_| {
        let current_filter = filter.get();
        let mut filtered_pkgs = all_packages.get();

        // Apply filters
        filtered_pkgs.retain(|pkg| {
            // Search text filter
            if !current_filter.search_text.is_empty() {
                let search_lower = current_filter.search_text.to_lowercase();
                if !pkg.name.to_lowercase().contains(&search_lower) &&
                   !pkg.description.to_lowercase().contains(&search_lower) {
                    return false;
                }
            }

            // Category filter
            if let Some(ref category) = current_filter.category {
                if pkg.category != *category {
                    return false;
                }
            }

            // Section filter
            if let Some(ref section) = current_filter.section {
                if pkg.section != *section {
                    return false;
                }
            }

            // Status filter
            if let Some(ref status) = current_filter.status {
                if pkg.status != *status {
                    return false;
                }
            }

            // Installed only filter
            if current_filter.installed_only && pkg.status != PackageStatus::Installed {
                return false;
            }

            // Upgradable only filter
            if current_filter.upgradable_only && pkg.status != PackageStatus::Upgradable {
                return false;
            }

            true
        });

        // Apply sorting
        filtered_pkgs.sort_by(|a, b| {
            let comparison = match current_filter.sort_by {
                PackageSortBy::Name => a.name.cmp(&b.name),
                PackageSortBy::Size => a.size.cmp(&b.size),
                PackageSortBy::Status => package_status_to_display_string(&a.status).cmp(package_status_to_display_string(&b.status)),
                PackageSortBy::Category => a.category.cmp(&b.category),
                PackageSortBy::Section => a.section.cmp(&b.section),
            };

            match current_filter.sort_order {
                SortOrder::Ascending => comparison,
                SortOrder::Descending => comparison.reverse(),
            }
        });

        // Update pagination
        let mut current_pagination = pagination.get();
        current_pagination.total_items = filtered_pkgs.len();
        set_pagination.set(current_pagination.clone());

        // Apply pagination
        let start = current_pagination.start_index();
        let end = current_pagination.end_index();
        let paginated_pkgs = if start < filtered_pkgs.len() {
            filtered_pkgs[start..end].to_vec()
        } else {
            Vec::new()
        };

        set_packages.set(paginated_pkgs);
    });

    // Load available updates when updates tab is shown
    create_effect(move |_| {
        if show_updates_tab.get() {
            spawn_local(async move {
                set_updates_loading.set(true);
                set_updates_error.set(None);
                match get_available_updates_api().await {
                    Ok(updates) => {
                        set_available_updates.set(updates);
                        set_updates_loading.set(false);
                    }
                    Err(e) => {
                        set_updates_error.set(Some(format!("Failed to fetch updates: {}", e)));
                        set_updates_loading.set(false);
                    }
                }
            });
        }
    });

    view! {
        <div class="space-y-6">
            // Package Statistics Dashboard
            {move || {
                if let Some(stats) = package_stats.get() {
                    view! {
                        <PackageStatsDashboard stats=stats />
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }
            }}

            <div class="bg-white rounded-lg shadow p-6">
                // Tab Navigation
                <div class="border-b border-gray-200 mb-6">
                    <nav class="-mb-px flex space-x-8">
                        <button
                            class={format!("py-2 px-1 border-b-2 font-medium text-sm {}",
                                if !show_updates_tab.get() {
                                    "border-blue-500 text-blue-600"
                                } else {
                                    "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                }
                            )}
                            on:click=move |_| set_show_updates_tab.set(false)
                        >
                            "All Packages"
                        </button>
                        <button
                            class={format!("py-2 px-1 border-b-2 font-medium text-sm {}",
                                if show_updates_tab.get() {
                                    "border-blue-500 text-blue-600"
                                } else {
                                    "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                }
                            )}
                            on:click=move |_| set_show_updates_tab.set(true)
                        >
                            "Available Updates"
                            {move || {
                                let updates_count = available_updates.get().len();
                                if updates_count > 0 {
                                    view! {
                                        <span class="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                            {updates_count}
                                        </span>
                                    }.into_view()
                                } else {
                                    view! { <span></span> }.into_view()
                                }
                            }}
                        </button>
                    </nav>
                </div>

                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">
                        {move || if show_updates_tab.get() { "Available Updates" } else { "Package Management" }}
                    </h1>
                    <div class="flex items-center space-x-4">
                        <button
                            class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            on:click=move |_| {
                                set_loading.set(true);
                                spawn_local(async move {
                                    match fetch_packages().await {
                                        Ok((pkgs, categories, sections, stats)) => {
                                            set_all_packages.set(pkgs.clone());
                                            set_packages.set(pkgs);
                                            set_available_categories.set(categories);
                                            set_available_sections.set(sections);
                                            set_package_stats.set(Some(stats));
                                            set_loading.set(false);
                                            set_error.set(None);
                                        }
                                        Err(e) => {
                                            set_error.set(Some(format!("Failed to refresh packages: {}", e)));
                                            set_loading.set(false);
                                        }
                                    }
                                });
                            }
                        >
                            "Refresh"
                        </button>
                        <button
                            class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                            disabled=move || selected_packages.get().is_empty()
                            class:opacity-50=move || selected_packages.get().is_empty()
                        >
                            "Install Selected"
                        </button>
                    </div>
                </div>

                // Conditional content based on active tab
                {move || {
                    if show_updates_tab.get() {
                        // Updates Tab Content
                        view! {
                            <UpdatesContent
                                available_updates=available_updates
                                set_available_updates=set_available_updates
                                updates_loading=updates_loading
                                set_updates_loading=set_updates_loading
                                updates_error=updates_error
                                set_updates_error=set_updates_error
                                selected_updates=selected_updates
                                set_selected_updates=set_selected_updates
                                updating_packages=updating_packages
                                set_updating_packages=set_updating_packages
                                show_confirmation=show_confirmation
                                set_show_confirmation=set_show_confirmation
                                pending_operation=pending_operation
                                set_pending_operation=set_pending_operation
                                global_operation_loading=global_operation_loading
                                set_global_operation_loading=set_global_operation_loading
                                global_operation_error=global_operation_error
                                set_global_operation_error=set_global_operation_error
                                global_operation_success=global_operation_success
                                set_global_operation_success=set_global_operation_success
                            />
                        }.into_view()
                    } else {
                        // Packages Tab Content
                        view! {
                            <div>
                                <PackageFilters
                                    filter=filter
                                    set_filter=set_filter
                                    available_categories=available_categories.get()
                                    available_sections=available_sections.get()
                                />

                                {move || {
                    if loading.get() {
                        view! {
                            <div class="flex items-center justify-center py-12">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <span class="ml-2 text-gray-600">"Loading packages..."</span>
                            </div>
                        }.into_view()
                    } else if let Some(err) = error.get() {
                        view! {
                            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill_rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip_rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">"Error"</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            {err}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }.into_view()
                    } else {
                        let pkgs = packages.get();
                        let current_pagination = pagination.get();
                        view! {
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm text-gray-600">
                                        {format!("Showing {} - {} of {} packages",
                                            current_pagination.start_index() + 1,
                                            current_pagination.end_index(),
                                            current_pagination.total_items)}
                                    </p>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-600">"Selected:"</span>
                                        <span class="text-sm font-medium text-blue-600">
                                            {move || selected_packages.get().len()}
                                        </span>
                                    </div>
                                </div>
                                <PackageTable
                                    packages=pkgs
                                    selected_packages=selected_packages
                                    set_selected_packages=set_selected_packages
                                    set_selected_package_details=set_selected_package_details
                                    set_pending_operation=set_pending_operation
                                    set_show_confirmation=set_show_confirmation
                                    set_removal_safety_info=set_removal_safety_info
                                    checking_removal_safety=checking_removal_safety
                                    set_checking_removal_safety=set_checking_removal_safety
                                />
                                <PackagePagination
                                    pagination=pagination.get()
                                    set_pagination=set_pagination
                                />
                            </div>
                        }.into_view()
                    }
                                }}
                            </div>
                        }.into_view()
                    }
                }}
            </div>

            // Package Details Modal
            {move || {
                if let Some(package) = selected_package_details.get() {
                    view! {
                        <PackageDetailsModal
                            package=Some(package)
                            set_package=set_selected_package_details
                            show_confirmation=show_confirmation
                            set_show_confirmation=set_show_confirmation
                            pending_operation=pending_operation
                            set_pending_operation=set_pending_operation
                            operation_loading=global_operation_loading
                            set_operation_loading=set_global_operation_loading
                            operation_error=global_operation_error
                            set_operation_error=set_global_operation_error
                            operation_success=global_operation_success
                            set_operation_success=set_global_operation_success
                        />
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }
            }}

            // Confirmation Dialog
            {move || {
                if show_confirmation.get() {
                    if let Some((operation_type, package_name)) = pending_operation.get() {
                        let operation_type_clone = operation_type.clone();
                        let package_name_clone = package_name.clone();
                        let operation_type_for_button = operation_type.clone();
                        let operation_type_for_class = operation_type.clone();

                        view! {
                            <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                                <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                                    <h3 class="text-lg font-semibold mb-4">
                                        {format!("Confirm {}", operation_type)}
                                    </h3>
                                    <div class="mb-6">
                                        <p class="text-gray-600 mb-4">
                                            {format!("Are you sure you want to {} package '{}'?",
                                                operation_type.to_lowercase(), package_name)}
                                        </p>

                                        // Show removal safety warnings for remove operations
                                        {move || {
                                            if operation_type == "Remove" {
                                                if let Some(safety_info) = removal_safety_info.get() {
                                                    if !safety_info.safe_to_remove {
                                                        view! {
                                                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                                                <div class="flex items-start">
                                                                    <div class="flex-shrink-0">
                                                                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div class="ml-3">
                                                                        <h4 class="text-sm font-medium text-yellow-800">
                                                                            "Dependency Warning"
                                                                        </h4>
                                                                        <div class="mt-2 text-sm text-yellow-700">
                                                                            <p class="mb-2">"Removing this package will break the following dependencies:"</p>
                                                                            <ul class="list-disc list-inside space-y-1">
                                                                                {safety_info.broken_dependencies.iter().map(|dep| {
                                                                                    view! { <li>{dep}</li> }
                                                                                }).collect::<Vec<_>>()}
                                                                            </ul>
                                                                            {if !safety_info.affected_packages.is_empty() {
                                                                                view! {
                                                                                    <p class="mt-2 font-medium">
                                                                                        "Affected packages: " {safety_info.affected_packages.join(", ")}
                                                                                    </p>
                                                                                }.into_view()
                                                                            } else {
                                                                                view! { <span></span> }.into_view()
                                                                            }}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        }.into_view()
                                                    } else {
                                                        view! {
                                                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                                                                <div class="flex items-center">
                                                                    <div class="flex-shrink-0">
                                                                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div class="ml-3">
                                                                        <p class="text-sm font-medium text-green-800">
                                                                            "Safe to remove - no dependency conflicts detected."
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        }.into_view()
                                                    }
                                                } else {
                                                    view! { <span></span> }.into_view()
                                                }
                                            } else {
                                                view! { <span></span> }.into_view()
                                            }
                                        }}
                                    </div>
                                    <div class="flex justify-end space-x-3">
                                        <button
                                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                                            on:click=move |_| {
                                                set_show_confirmation.set(false);
                                                set_pending_operation.set(None);
                                                set_removal_safety_info.set(None);
                                            }
                                        >
                                            "Cancel"
                                        </button>
                                        <button
                                            class=format!("px-4 py-2 text-white rounded hover:opacity-90 {}",
                                                if operation_type_for_class == "Install" || operation_type_for_class == "Upgrade" || operation_type_for_class == "Update" || operation_type_for_class == "Batch Update" || operation_type_for_class == "Update All" {
                                                    "bg-blue-600"
                                                } else {
                                                    "bg-red-600"
                                                }
                                            )
                                            on:click=move |_| {
                                                let pkg_name = package_name_clone.clone();
                                                let op_type = operation_type_clone.clone();

                                                set_show_confirmation.set(false);
                                                set_pending_operation.set(None);
                                                set_removal_safety_info.set(None);
                                                set_global_operation_error.set(None);
                                                set_global_operation_success.set(None);
                                                set_global_operation_loading.set(true);
                                                set_updating_packages.set(true);

                                                spawn_local(async move {
                                                    let result = match op_type.as_str() {
                                                        "Install" | "Upgrade" => install_package_api(&pkg_name).await,
                                                        "Remove" => remove_package_api(&pkg_name).await,
                                                        "Update" => upgrade_package_api(&pkg_name).await,
                                                        "Batch Update" => {
                                                            let selected = selected_updates.get();
                                                            upgrade_packages_api(selected).await
                                                        },
                                                        "Update All" => upgrade_all_packages_api().await,
                                                        _ => Err("Unknown operation type".to_string())
                                                    };

                                                    match result {
                                                        Ok(response) => {
                                                            set_global_operation_success.set(Some(response.message));
                                                            set_global_operation_loading.set(false);
                                                            set_updating_packages.set(false);

                                                            // Refresh updates list after successful update
                                                            if op_type.contains("Update") {
                                                                match get_available_updates_api().await {
                                                                    Ok(updates) => {
                                                                        set_available_updates.set(updates);
                                                                        set_selected_updates.set(Vec::new());
                                                                    }
                                                                    Err(_) => {
                                                                        // Ignore refresh errors, main operation succeeded
                                                                    }
                                                                }
                                                            }
                                                        }
                                                        Err(error) => {
                                                            set_global_operation_error.set(Some(error));
                                                            set_global_operation_loading.set(false);
                                                            set_updating_packages.set(false);
                                                        }
                                                    }
                                                });
                                            }
                                        >
                                            {operation_type_for_button}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }.into_view()
                    } else {
                        view! { <div></div> }.into_view()
                    }
                } else {
                    view! { <div></div> }.into_view()
                }
            }}
        </div>
    }
}

#[component]
fn PackageFilters(
    filter: ReadSignal<PackageFilter>,
    set_filter: WriteSignal<PackageFilter>,
    available_categories: Vec<String>,
    available_sections: Vec<String>,
) -> impl IntoView {
    view! {
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="text-sm font-medium text-gray-900 mb-3">"Filters & Search"</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                <div class="lg:col-span-2">
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Search Packages"</label>
                    <input
                        type="text"
                        placeholder="Search by name or description..."
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        prop:value=move || filter.get().search_text
                        on:input=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.search_text = value;
                            set_filter.set(current_filter);
                        }
                    />
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Category"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.category = if value.is_empty() { None } else { Some(value) };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="">"All Categories"</option>
                        {available_categories.into_iter().map(|category| {
                            view! {
                                <option value=category.clone()>{category}</option>
                            }
                        }).collect::<Vec<_>>()}
                    </select>
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Section"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.section = if value.is_empty() { None } else { Some(value) };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="">"All Sections"</option>
                        {available_sections.into_iter().map(|section| {
                            view! {
                                <option value=section.clone()>{section}</option>
                            }
                        }).collect::<Vec<_>>()}
                    </select>
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Status"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.status = if value.is_empty() {
                                None
                            } else {
                                match value.as_str() {
                                    "NotInstalled" => Some(PackageStatus::NotInstalled),
                                    "Installed" => Some(PackageStatus::Installed),
                                    "Upgradable" => Some(PackageStatus::Upgradable),
                                    "Broken" => Some(PackageStatus::Broken),
                                    "ConfigFiles" => Some(PackageStatus::ConfigFiles),
                                    _ => None,
                                }
                            };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="">"All Status"</option>
                        <option value="NotInstalled">"Not Installed"</option>
                        <option value="Installed">"Installed"</option>
                        <option value="Upgradable">"Upgradable"</option>
                        <option value="Broken">"Broken"</option>
                        <option value="ConfigFiles">"Config Files"</option>
                    </select>
                </div>

                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">"Sort By"</label>
                    <select
                        class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        on:change=move |ev| {
                            let value = event_target_value(&ev);
                            let mut current_filter = filter.get();
                            current_filter.sort_by = match value.as_str() {
                                "Size" => PackageSortBy::Size,
                                "Status" => PackageSortBy::Status,
                                "Category" => PackageSortBy::Category,
                                "Section" => PackageSortBy::Section,
                                _ => PackageSortBy::Name,
                            };
                            set_filter.set(current_filter);
                        }
                    >
                        <option value="Name">"Name"</option>
                        <option value="Size">"Size"</option>
                        <option value="Status">"Status"</option>
                        <option value="Category">"Category"</option>
                        <option value="Section">"Section"</option>
                    </select>
                </div>
            </div>

            // Quick filter buttons
            <div class="mt-4 flex flex-wrap gap-2">
                <button
                    class="px-3 py-1 text-xs font-medium rounded-full border"
                    class:bg-blue-100=move || filter.get().installed_only
                    class:text-blue-800=move || filter.get().installed_only
                    class:border-blue-300=move || filter.get().installed_only
                    class:bg-gray-100=move || !filter.get().installed_only
                    class:text-gray-700=move || !filter.get().installed_only
                    class:border-gray-300=move || !filter.get().installed_only
                    on:click=move |_| {
                        let mut current_filter = filter.get();
                        current_filter.installed_only = !current_filter.installed_only;
                        if current_filter.installed_only {
                            current_filter.upgradable_only = false;
                        }
                        set_filter.set(current_filter);
                    }
                >
                    "Installed Only"
                </button>
                <button
                    class="px-3 py-1 text-xs font-medium rounded-full border"
                    class:bg-blue-100=move || filter.get().upgradable_only
                    class:text-blue-800=move || filter.get().upgradable_only
                    class:border-blue-300=move || filter.get().upgradable_only
                    class:bg-gray-100=move || !filter.get().upgradable_only
                    class:text-gray-700=move || !filter.get().upgradable_only
                    class:border-gray-300=move || !filter.get().upgradable_only
                    on:click=move |_| {
                        let mut current_filter = filter.get();
                        current_filter.upgradable_only = !current_filter.upgradable_only;
                        if current_filter.upgradable_only {
                            current_filter.installed_only = false;
                        }
                        set_filter.set(current_filter);
                    }
                >
                    "Upgradable Only"
                </button>
                <button
                    class="px-3 py-1 text-xs font-medium rounded-full border bg-gray-100 text-gray-700 border-gray-300"
                    on:click=move |_| {
                        set_filter.set(PackageFilter::default());
                    }
                >
                    "Clear Filters"
                </button>
            </div>
        </div>
    }
}

#[component]
fn PackageTable(
    packages: Vec<Package>,
    selected_packages: ReadSignal<Vec<String>>,
    set_selected_packages: WriteSignal<Vec<String>>,
    set_selected_package_details: WriteSignal<Option<Package>>,
    // Removal-related signals
    set_pending_operation: WriteSignal<Option<(String, String)>>,
    set_show_confirmation: WriteSignal<bool>,
    set_removal_safety_info: WriteSignal<Option<RemovalSafetyInfo>>,
    checking_removal_safety: ReadSignal<bool>,
    set_checking_removal_safety: WriteSignal<bool>,
) -> impl IntoView {

    let format_size = |size: u64| -> String {
        if size < 1024 {
            format!("{} B", size)
        } else if size < 1024 * 1024 {
            format!("{:.1} KB", size as f64 / 1024.0)
        } else {
            format!("{:.1} MB", size as f64 / (1024.0 * 1024.0))
        }
    };

    view! {
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input
                                type="checkbox"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                on:change={
                                    let packages_clone = packages.clone();
                                    move |ev| {
                                        let checked = event_target_checked(&ev);
                                        if checked {
                                            let all_names: Vec<String> = packages_clone.iter()
                                                .filter(|pkg| pkg.status == PackageStatus::NotInstalled || pkg.status == PackageStatus::Upgradable)
                                                .map(|pkg| pkg.name.clone())
                                                .collect();
                                            set_selected_packages.set(all_names);
                                        } else {
                                            set_selected_packages.set(vec![]);
                                        }
                                    }
                                }
                            />
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Package"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Version"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Status"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Category"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Section"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Size"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Actions"
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {packages.into_iter().map(|package| {
                        let pkg_name = package.name.clone();
                        let pkg_name_for_selected = pkg_name.clone();
                        let is_selected = move || selected_packages.get().contains(&pkg_name_for_selected);
                        let can_install = package.status == PackageStatus::NotInstalled || package.status == PackageStatus::Upgradable;
                        let can_remove = package.status == PackageStatus::Installed || package.status == PackageStatus::Upgradable;

                        view! {
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {if can_install {
                                        view! {
                                            <input
                                                type="checkbox"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                prop:checked=is_selected
                                                on:change=move |ev| {
                                                    let checked = event_target_checked(&ev);
                                                    let mut current_selected = selected_packages.get();
                                                    if checked {
                                                        if !current_selected.contains(&pkg_name) {
                                                            current_selected.push(pkg_name.clone());
                                                        }
                                                    } else {
                                                        current_selected.retain(|name| name != &pkg_name);
                                                    }
                                                    set_selected_packages.set(current_selected);
                                                }
                                            />
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{package.name.clone()}</div>
                                    <div class="text-sm text-gray-500 max-w-xs truncate">{package.description}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div>{package.version}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class={format!("inline-flex px-2 py-1 text-xs font-semibold rounded-full {}", package_status_color_class(&package.status))}>
                                        {package_status_to_display_string(&package.status)}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {package.category.clone()}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {package.section.clone()}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {format_size(package.size)}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    {if can_install {
                                        view! {
                                            <button class="text-blue-600 hover:text-blue-900">
                                                {if package.status == PackageStatus::Upgradable { "Upgrade" } else { "Install" }}
                                            </button>
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }}
                                    {if can_remove {
                                        let pkg_name_for_remove = package.name.clone();
                                        view! {
                                            <button
                                                class="text-red-600 hover:text-red-900 ml-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                                disabled=move || checking_removal_safety.get()
                                                on:click={
                                                    let pkg_name = pkg_name_for_remove.clone();
                                                    let set_pending_operation = set_pending_operation;
                                                    let set_show_confirmation = set_show_confirmation;
                                                    let set_removal_safety_info = set_removal_safety_info;
                                                    let set_checking_removal_safety = set_checking_removal_safety;

                                                    move |_| {
                                                        let pkg_name = pkg_name.clone();
                                                        let set_pending_operation = set_pending_operation;
                                                        let set_show_confirmation = set_show_confirmation;
                                                        let set_removal_safety_info = set_removal_safety_info;
                                                        let set_checking_removal_safety = set_checking_removal_safety;

                                                        spawn_local(async move {
                                                            set_checking_removal_safety.set(true);

                                                            match check_removal_safety_api(&vec![pkg_name.clone()]).await {
                                                                Ok(safety_info) => {
                                                                    set_removal_safety_info.set(Some(safety_info));
                                                                    set_pending_operation.set(Some(("Remove".to_string(), pkg_name)));
                                                                    set_show_confirmation.set(true);
                                                                },
                                                                Err(e) => {
                                                                    // If safety check fails, still allow removal but show warning
                                                                    web_sys::console::warn_1(&format!("Failed to check removal safety: {}", e).into());
                                                                    set_removal_safety_info.set(None);
                                                                    set_pending_operation.set(Some(("Remove".to_string(), pkg_name)));
                                                                    set_show_confirmation.set(true);
                                                                }
                                                            }

                                                            set_checking_removal_safety.set(false);
                                                        });
                                                    }
                                                }
                                            >
                                                {move || if checking_removal_safety.get() { "Checking..." } else { "Remove" }}
                                            </button>
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }}
                                    <button
                                        class="text-gray-600 hover:text-gray-900 ml-2"
                                        on:click={
                                            let package_clone = package.clone();
                                            move |_| {
                                                set_selected_package_details.set(Some(package_clone.clone()));
                                            }
                                        }
                                    >
                                        "Details"
                                    </button>
                                </td>
                            </tr>
                        }
                    }).collect::<Vec<_>>()}
                </tbody>
            </table>
        </div>
    }
}

// Re-export PackageStats from package-manager crate


// Helper function to fetch packages
async fn fetch_packages() -> Result<(Vec<Package>, Vec<String>, Vec<String>, PackageStats), String> {
    // Mock implementation - in real system would call opkg backend
    let packages = vec![
        Package {
            name: "curl".to_string(),
            version: "7.88.1-1".to_string(),
            description: "Command line tool for transferring data with URL syntax".to_string(),
            category: "Network".to_string(),
            section: "net".to_string(),
            size: 245760,
            installed_size: 245760,
            status: PackageStatus::Upgradable,
            dependencies: vec!["libcurl4".to_string(), "ca-certificates".to_string()],
            conflicts: vec![],
            provides: vec![],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://curl.se/".to_string()),
            architecture: "mips_24kc".to_string(),
            repository: "base".to_string(),
            priority: "optional".to_string(),
        },
        Package {
            name: "nginx".to_string(),
            version: "1.24.0-1".to_string(),
            description: "HTTP and reverse proxy server".to_string(),
            category: "Network".to_string(),
            section: "net".to_string(),
            size: 524288,
            installed_size: 1048576,
            status: PackageStatus::NotInstalled,
            dependencies: vec!["libpcre".to_string(), "zlib".to_string()],
            conflicts: vec!["lighttpd".to_string()],
            provides: vec!["httpd".to_string()],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://nginx.org/".to_string()),
            architecture: "mips_24kc".to_string(),
            repository: "packages".to_string(),
            priority: "optional".to_string(),
        },
        Package {
            name: "dropbear".to_string(),
            version: "2022.83-1".to_string(),
            description: "Small SSH server and client".to_string(),
            category: "Network".to_string(),
            section: "net".to_string(),
            size: 114688,
            installed_size: 114688,
            status: PackageStatus::Installed,
            dependencies: vec!["zlib".to_string()],
            conflicts: vec!["openssh-server".to_string()],
            provides: vec!["ssh-server".to_string()],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://matt.ucc.asn.au/dropbear/dropbear.html".to_string()),
            architecture: "mips_24kc".to_string(),
            repository: "base".to_string(),
            priority: "required".to_string(),
        },
        Package {
            name: "htop".to_string(),
            version: "3.2.2-1".to_string(),
            description: "Interactive process viewer".to_string(),
            category: "Utilities".to_string(),
            section: "utils".to_string(),
            size: 98304,
            installed_size: 196608,
            status: PackageStatus::NotInstalled,
            dependencies: vec!["libncurses".to_string()],
            conflicts: vec![],
            provides: vec![],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://htop.dev/".to_string()),
            architecture: "mips_24kc".to_string(),
            repository: "packages".to_string(),
            priority: "optional".to_string(),
        },
        Package {
            name: "vim".to_string(),
            version: "9.0.1568-1".to_string(),
            description: "Vi IMproved - enhanced vi editor".to_string(),
            category: "Utilities".to_string(),
            section: "utils".to_string(),
            size: 1048576,
            installed_size: 1048576,
            status: PackageStatus::Installed,
            dependencies: vec!["libncurses".to_string()],
            conflicts: vec!["nano".to_string()],
            provides: vec!["editor".to_string()],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: Some("https://www.vim.org/".to_string()),
            architecture: "mips_24kc".to_string(),
            repository: "packages".to_string(),
            priority: "optional".to_string(),
        },
        Package {
            name: "kmod-usb-storage".to_string(),
            version: "5.15.134-1".to_string(),
            description: "Kernel module for USB storage devices".to_string(),
            category: "Kernel".to_string(),
            section: "kernel".to_string(),
            size: 32768,
            installed_size: 32768,
            status: PackageStatus::Upgradable,
            dependencies: vec!["kernel".to_string()],
            conflicts: vec![],
            provides: vec![],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: None,
            architecture: "mips_24kc".to_string(),
            repository: "base".to_string(),
            priority: "standard".to_string(),
        },
        Package {
            name: "broken-package".to_string(),
            version: "1.0.0-1".to_string(),
            description: "A package with broken dependencies".to_string(),
            category: "System".to_string(),
            section: "admin".to_string(),
            size: 16384,
            installed_size: 16384,
            status: PackageStatus::Broken,
            dependencies: vec!["missing-lib".to_string()],
            conflicts: vec![],
            provides: vec![],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: None,
            architecture: "mips_24kc".to_string(),
            repository: "testing".to_string(),
            priority: "extra".to_string(),
        },
        Package {
            name: "config-only-package".to_string(),
            version: "1.0.0-1".to_string(),
            description: "Package with only configuration files remaining".to_string(),
            category: "System".to_string(),
            section: "admin".to_string(),
            size: 25600,
            installed_size: 51200,
            status: PackageStatus::ConfigFiles,
            dependencies: vec![],
            conflicts: vec![],
            provides: vec![],
            maintainer: "OpenWrt Developers".to_string(),
            homepage: None,
            architecture: "mips_24kc".to_string(),
            repository: "base".to_string(),
            priority: "important".to_string(),
        },
    ];

    let categories: Vec<String> = packages.iter()
        .map(|p| p.category.clone())
        .collect::<std::collections::HashSet<_>>()
        .into_iter()
        .collect();

    let sections: Vec<String> = packages.iter()
        .map(|p| p.section.clone())
        .collect::<std::collections::HashSet<_>>()
        .into_iter()
        .collect();

    // Mock package statistics
    let stats = PackageStats {
        total_packages: packages.len(),
        installed_packages: packages.iter().filter(|p| p.status == PackageStatus::Installed).count(),
        upgradable_packages: packages.iter().filter(|p| p.status == PackageStatus::Upgradable).count(),
        broken_packages: packages.iter().filter(|p| p.status == PackageStatus::Broken).count(),
        total_installed_size: packages.iter()
            .filter(|p| p.status == PackageStatus::Installed)
            .map(|p| p.installed_size)
            .sum(),
        categories: categories.iter()
            .map(|cat| (cat.clone(), packages.iter().filter(|p| &p.category == cat).count()))
            .collect(),
    };

    Ok((packages, categories, sections, stats))
}

// Package Statistics Dashboard Component
#[component]
fn PackageStatsDashboard(stats: PackageStats) -> impl IntoView {
    view! {
        <div class="bg-white shadow rounded-lg p-6 mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"Package Statistics"</h3>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{stats.total_packages}</div>
                    <div class="text-sm text-gray-500">"Total"</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{stats.installed_packages}</div>
                    <div class="text-sm text-gray-500">"Installed"</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600">{stats.upgradable_packages}</div>
                    <div class="text-sm text-gray-500">"Upgradable"</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600">{stats.broken_packages}</div>
                    <div class="text-sm text-gray-500">"Broken"</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-600">{format_size(stats.total_installed_size)}</div>
                    <div class="text-sm text-gray-500">"Total Size"</div>
                </div>
            </div>

            {if !stats.categories.is_empty() {
                view! {
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-3">"By Category"</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                            {stats.categories.into_iter().map(|(category, count)| {
                                view! {
                                    <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                        <span class="text-sm text-gray-700">{category}</span>
                                        <span class="text-sm font-medium text-gray-900">{count}</span>
                                    </div>
                                }
                            }).collect::<Vec<_>>()}
                        </div>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }}
        </div>
    }
}

// Package Pagination Component
#[component]
fn PackagePagination(
    pagination: PackagePagination,
    set_pagination: WriteSignal<PackagePagination>,
) -> impl IntoView {
    let total_pages = pagination.total_pages();
    let start_item = pagination.start_item();
    let end_item = pagination.end_item();

    view! {
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <button
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    class:opacity-50={pagination.current_page == 1}
                    disabled={pagination.current_page == 1}
                    on:click=move |_| {
                        if pagination.current_page > 1 {
                            set_pagination.update(|p| p.current_page -= 1);
                        }
                    }
                >
                    "Previous"
                </button>
                <button
                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    class:opacity-50={pagination.current_page >= total_pages}
                    disabled={pagination.current_page >= total_pages}
                    on:click=move |_| {
                        if pagination.current_page < total_pages {
                            set_pagination.update(|p| p.current_page += 1);
                        }
                    }
                >
                    "Next"
                </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        "Showing "
                        <span class="font-medium">{start_item}</span>
                        " to "
                        <span class="font-medium">{end_item}</span>
                        " of "
                        <span class="font-medium">{pagination.total_items}</span>
                        " results"
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="flex items-center space-x-1">
                        <label class="text-sm text-gray-700">"Per page:"</label>
                        <select
                            class="border border-gray-300 rounded px-2 py-1 text-sm"
                            on:change=move |ev| {
                                let new_size = event_target_value(&ev).parse::<usize>().unwrap_or(10);
                                set_pagination.update(|p| {
                                    p.page_size = new_size;
                                    p.current_page = 1; // Reset to first page
                                });
                            }
                        >
                            <option value="10" selected={pagination.page_size == 10}>"10"</option>
                            <option value="25" selected={pagination.page_size == 25}>"25"</option>
                            <option value="50" selected={pagination.page_size == 50}>"50"</option>
                            <option value="100" selected={pagination.page_size == 100}>"100"</option>
                        </select>
                    </div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <button
                            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                            class:opacity-50={pagination.current_page == 1}
                            disabled={pagination.current_page == 1}
                            on:click=move |_| {
                                if pagination.current_page > 1 {
                                    set_pagination.update(|p| p.current_page -= 1);
                                }
                            }
                        >
                            "Previous"
                        </button>

                        {(1..=total_pages.min(10)).map(|page| {
                            let is_current = page == pagination.current_page;
                            view! {
                                <button
                                    class={format!(
                                        "relative inline-flex items-center px-4 py-2 border text-sm font-medium {}",
                                        if is_current {
                                            "z-10 bg-blue-50 border-blue-500 text-blue-600"
                                        } else {
                                            "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                                        }
                                    )}
                                    on:click=move |_| {
                                        set_pagination.update(|p| p.current_page = page);
                                    }
                                >
                                    {page}
                                </button>
                            }
                        }).collect::<Vec<_>>()}

                        <button
                            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                            class:opacity-50={pagination.current_page >= total_pages}
                            disabled={pagination.current_page >= total_pages}
                            on:click=move |_| {
                                if pagination.current_page < total_pages {
                                    set_pagination.update(|p| p.current_page += 1);
                                }
                            }
                        >
                            "Next"
                        </button>
                    </nav>
                </div>
            </div>
        </div>
    }
}

// Package Details Modal Component
#[component]
fn PackageDetailsModal(
    package: Option<Package>,
    set_package: WriteSignal<Option<Package>>,
    show_confirmation: ReadSignal<bool>,
    set_show_confirmation: WriteSignal<bool>,
    pending_operation: ReadSignal<Option<(String, String)>>,
    set_pending_operation: WriteSignal<Option<(String, String)>>,
    operation_loading: ReadSignal<bool>,
    set_operation_loading: WriteSignal<bool>,
    operation_error: ReadSignal<Option<String>>,
    set_operation_error: WriteSignal<Option<String>>,
    operation_success: ReadSignal<Option<String>>,
    set_operation_success: WriteSignal<Option<String>>,
) -> impl IntoView {

    view! {
        {move || {
            if let Some(ref pkg) = package {
                view! {
                    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-bold text-gray-900">{pkg.name.clone()}</h3>
                                <button
                                    class="text-gray-400 hover:text-gray-600"
                                    on:click=move |_| set_package.set(None)
                                >
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>

                            <div class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Version"</h4>
                                        <p class="text-gray-600">{pkg.version.clone()}</p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Status"</h4>
                                        <span class={format!("inline-flex px-2 py-1 text-xs font-semibold rounded-full {}", package_status_color_class(&pkg.status))}>
                                            {package_status_to_display_string(&pkg.status)}
                                        </span>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Category"</h4>
                                        <p class="text-gray-600">{pkg.category.clone()}</p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Section"</h4>
                                        <p class="text-gray-600">{pkg.section.clone()}</p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Size"</h4>
                                        <p class="text-gray-600">{format_size(pkg.size)}</p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Installed Size"</h4>
                                        <p class="text-gray-600">{format_size(pkg.installed_size)}</p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Architecture"</h4>
                                        <p class="text-gray-600">{pkg.architecture.clone()}</p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Repository"</h4>
                                        <p class="text-gray-600">{pkg.repository.clone()}</p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Priority"</h4>
                                        <p class="text-gray-600">{pkg.priority.clone()}</p>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-700">"Maintainer"</h4>
                                        <p class="text-gray-600">{pkg.maintainer.clone()}</p>
                                    </div>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-700">"Description"</h4>
                                    <p class="text-gray-600">{pkg.description.clone()}</p>
                                </div>

                                {if let Some(homepage) = &pkg.homepage {
                                    view! {
                                        <div>
                                            <h4 class="font-semibold text-gray-700">"Homepage"</h4>
                                            <a href={homepage.clone()} target="_blank" class="text-blue-600 hover:text-blue-800 underline">
                                                {homepage.clone()}
                                            </a>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}

                                {if !pkg.dependencies.is_empty() {
                                    view! {
                                        <div>
                                            <h4 class="font-semibold text-gray-700">"Dependencies"</h4>
                                            <div class="flex flex-wrap gap-1">
                                                {pkg.dependencies.iter().map(|dep| {
                                                    view! {
                                                        <span class="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                                            {dep.clone()}
                                                        </span>
                                                    }
                                                }).collect::<Vec<_>>()}
                                            </div>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}

                                {if !pkg.conflicts.is_empty() {
                                    view! {
                                        <div>
                                            <h4 class="font-semibold text-gray-700">"Conflicts"</h4>
                                            <div class="flex flex-wrap gap-1">
                                                {pkg.conflicts.iter().map(|conflict| {
                                                    view! {
                                                        <span class="inline-flex px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                                                            {conflict.clone()}
                                                        </span>
                                                    }
                                                }).collect::<Vec<_>>()}
                                            </div>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}

                                {if !pkg.provides.is_empty() {
                                    view! {
                                        <div>
                                            <h4 class="font-semibold text-gray-700">"Provides"</h4>
                                            <div class="flex flex-wrap gap-1">
                                                {pkg.provides.iter().map(|provides| {
                                                    view! {
                                                        <span class="inline-flex px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                                                            {provides.clone()}
                                                        </span>
                                                    }
                                                }).collect::<Vec<_>>()}
                                            </div>
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }}
                            </div>

                            // Operation status messages
                            {move || {
                                if let Some(error) = operation_error.get() {
                                    view! {
                                        <div class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                                            <strong>"Error: "</strong> {error}
                                        </div>
                                    }.into_view()
                                } else if let Some(success) = operation_success.get() {
                                    view! {
                                        <div class="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                                            <strong>"Success: "</strong> {success}
                                        </div>
                                    }.into_view()
                                } else {
                                    view! { <div></div> }.into_view()
                                }
                            }}

                            <div class="flex justify-end space-x-2 mt-6 pt-4 border-t">
                                <button
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
                                    on:click=move |_| set_package.set(None)
                                >
                                    "Close"
                                </button>
                                {
                                    let pkg_name = pkg.name.clone();
                                    let pkg_status = pkg.status.clone();
                                    if pkg_status == PackageStatus::NotInstalled || pkg_status == PackageStatus::Upgradable {
                                        let pkg_status_for_click = pkg_status.clone();
                                        let pkg_status_for_display = pkg_status.clone();
                                        view! {
                                            <button
                                                class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                                disabled=move || operation_loading.get()
                                                on:click=move |_| {
                                                    let pkg_name = pkg_name.clone();
                                                    let operation_type = if pkg_status_for_click == PackageStatus::Upgradable {
                                                        "Upgrade".to_string()
                                                    } else {
                                                        "Install".to_string()
                                                    };
                                                    set_pending_operation.set(Some((operation_type, pkg_name)));
                                                    set_show_confirmation.set(true);
                                                }
                                            >
                                                {move || if operation_loading.get() {
                                                    "Installing..."
                                                } else if pkg_status_for_display == PackageStatus::Upgradable {
                                                    "Upgrade"
                                                } else {
                                                    "Install"
                                                }}
                                            </button>
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }
                                }
                                {
                                    let pkg_name = pkg.name.clone();
                                    let pkg_status = pkg.status.clone();
                                    if pkg_status == PackageStatus::Installed {
                                        view! {
                                            <span class="text-sm text-gray-500">"Installed"</span>
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }
                                }
                            </div>
                        </div>
                    </div>
                }.into_view()
            } else {
                view! { <div></div> }.into_view()
            }
        }};

        modal_content
    }
}

// API Response Types
#[derive(Debug, Serialize, Deserialize)]
struct ApiResponse<T> {
    success: bool,
    data: Option<T>,
    error: Option<String>,
    timestamp: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct PackageOperationResponse {
    success: bool,
    message: String,
    affected_packages: Vec<String>,
    errors: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct PackageUpdate {
    name: String,
    current_version: String,
    available_version: String,
    description: String,
    size: u64,
    category: String,
    dependencies: Vec<String>,
    changelog: Option<String>,
    security_update: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct UpdateOperationRequest {
    packages: Vec<String>,
    force: Option<bool>,
    no_deps: Option<bool>,
}

// Package API Functions
async fn install_package_api(package_name: &str) -> Result<PackageOperationResponse, String> {
    let url = format!("/api/packages/{}/install", package_name);

    let response = reqwest::Client::new()
        .post(&url)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: ApiResponse<PackageOperationResponse> = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    if api_response.success {
        api_response.data.ok_or_else(|| "Missing data in successful response".to_string())
    } else {
        Err(api_response.error.unwrap_or_else(|| "Unknown error occurred".to_string()))
    }
}

async fn remove_package_api(package_name: &str) -> Result<PackageOperationResponse, String> {
    let url = format!("/api/packages/{}/remove", package_name);

    let response = reqwest::Client::new()
        .delete(&url)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: ApiResponse<PackageOperationResponse> = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    if api_response.success {
        api_response.data.ok_or_else(|| "Missing data in successful response".to_string())
    } else {
        Err(api_response.error.unwrap_or_else(|| "Unknown error occurred".to_string()))
    }
}

// Removal safety check API function
async fn check_removal_safety_api(package_names: &[String]) -> Result<RemovalSafetyInfo, String> {
    let packages_param = package_names.join(",");
    let url = format!("/api/packages/removal-safety?packages={}", packages_param);

    let response = reqwest::Client::new()
        .get(&url)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: ApiResponse<RemovalSafetyInfo> = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    if api_response.success {
        api_response.data.ok_or_else(|| "Missing data in successful response".to_string())
    } else {
        Err(api_response.error.unwrap_or_else(|| "Unknown error occurred".to_string()))
    }
}

// Removal safety information
#[derive(Debug, Clone, Serialize, Deserialize)]
struct RemovalSafetyInfo {
    packages_to_remove: Vec<String>,
    affected_packages: Vec<String>,
    broken_dependencies: Vec<String>,
    safe_to_remove: bool,
}

// Update API Functions
async fn get_available_updates_api() -> Result<Vec<PackageUpdate>, String> {
    let url = "/api/packages/updates";

    let response = reqwest::Client::new()
        .get(url)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: ApiResponse<Vec<PackageUpdate>> = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    if api_response.success {
        api_response.data.ok_or_else(|| "Missing data in successful response".to_string())
    } else {
        Err(api_response.error.unwrap_or_else(|| "Unknown error occurred".to_string()))
    }
}

async fn upgrade_package_api(package_name: &str) -> Result<PackageOperationResponse, String> {
    let url = format!("/api/packages/{}/upgrade", package_name);

    let response = reqwest::Client::new()
        .post(&url)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: ApiResponse<PackageOperationResponse> = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    if api_response.success {
        api_response.data.ok_or_else(|| "Missing data in successful response".to_string())
    } else {
        Err(api_response.error.unwrap_or_else(|| "Unknown error occurred".to_string()))
    }
}

async fn upgrade_packages_api(packages: Vec<String>) -> Result<PackageOperationResponse, String> {
    let url = "/api/packages/upgrade";
    let request = UpdateOperationRequest {
        packages,
        force: None,
        no_deps: None,
    };

    let response = reqwest::Client::new()
        .post(url)
        .json(&request)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: ApiResponse<PackageOperationResponse> = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    if api_response.success {
        api_response.data.ok_or_else(|| "Missing data in successful response".to_string())
    } else {
        Err(api_response.error.unwrap_or_else(|| "Unknown error occurred".to_string()))
    }
}

async fn upgrade_all_packages_api() -> Result<PackageOperationResponse, String> {
    let url = "/api/packages/upgrade-all";

    let response = reqwest::Client::new()
        .post(url)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: ApiResponse<PackageOperationResponse> = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    if api_response.success {
        api_response.data.ok_or_else(|| "Missing data in successful response".to_string())
    } else {
        Err(api_response.error.unwrap_or_else(|| "Unknown error occurred".to_string()))
    }
}

async fn update_package_lists_api() -> Result<PackageOperationResponse, String> {
    let url = "/api/packages/update-lists";

    let response = reqwest::Client::new()
        .post(url)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }

    let api_response: ApiResponse<PackageOperationResponse> = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    if api_response.success {
        api_response.data.ok_or_else(|| "Missing data in successful response".to_string())
    } else {
        Err(api_response.error.unwrap_or_else(|| "Unknown error occurred".to_string()))
    }
}

// Updates Content Component
#[component]
fn UpdatesContent(
    available_updates: ReadSignal<Vec<PackageUpdate>>,
    set_available_updates: WriteSignal<Vec<PackageUpdate>>,
    updates_loading: ReadSignal<bool>,
    set_updates_loading: WriteSignal<bool>,
    updates_error: ReadSignal<Option<String>>,
    set_updates_error: WriteSignal<Option<String>>,
    selected_updates: ReadSignal<Vec<String>>,
    set_selected_updates: WriteSignal<Vec<String>>,
    updating_packages: ReadSignal<bool>,
    set_updating_packages: WriteSignal<bool>,
    show_confirmation: ReadSignal<bool>,
    set_show_confirmation: WriteSignal<bool>,
    pending_operation: ReadSignal<Option<(String, String)>>,
    set_pending_operation: WriteSignal<Option<(String, String)>>,
    global_operation_loading: ReadSignal<bool>,
    set_global_operation_loading: WriteSignal<bool>,
    global_operation_error: ReadSignal<Option<String>>,
    set_global_operation_error: WriteSignal<Option<String>>,
    global_operation_success: ReadSignal<Option<String>>,
    set_global_operation_success: WriteSignal<Option<String>>,
) -> impl IntoView {
    view! {
        <div class="space-y-4">
            // Updates header with actions
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button
                        class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled=move || updates_loading.get()
                        on:click=move |_| {
                            set_updates_loading.set(true);
                            set_updates_error.set(None);
                            spawn_local(async move {
                                match update_package_lists_api().await {
                                    Ok(_) => {
                                        // Refresh available updates after updating lists
                                        match get_available_updates_api().await {
                                            Ok(updates) => {
                                                set_available_updates.set(updates);
                                                set_updates_loading.set(false);
                                            }
                                            Err(e) => {
                                                set_updates_error.set(Some(format!("Failed to fetch updates: {}", e)));
                                                set_updates_loading.set(false);
                                            }
                                        }
                                    }
                                    Err(e) => {
                                        set_updates_error.set(Some(format!("Failed to update package lists: {}", e)));
                                        set_updates_loading.set(false);
                                    }
                                }
                            });
                        }
                    >
                        {move || if updates_loading.get() { "Checking..." } else { "Check for Updates" }}
                    </button>

                    <button
                        class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled=move || selected_updates.get().is_empty() || updating_packages.get()
                        on:click=move |_| {
                            let selected = selected_updates.get();
                            if !selected.is_empty() {
                                set_pending_operation.set(Some(("Batch Update".to_string(), format!("{} packages", selected.len()))));
                                set_show_confirmation.set(true);
                            }
                        }
                    >
                        {move || {
                            let count = selected_updates.get().len();
                            if count > 0 {
                                format!("Update Selected ({})", count)
                            } else {
                                "Update Selected".to_string()
                            }
                        }}
                    </button>

                    <button
                        class="px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled=move || available_updates.get().is_empty() || updating_packages.get()
                        on:click=move |_| {
                            let updates = available_updates.get();
                            if !updates.is_empty() {
                                set_pending_operation.set(Some(("Update All".to_string(), format!("{} packages", updates.len()))));
                                set_show_confirmation.set(true);
                            }
                        }
                    >
                        {move || {
                            let count = available_updates.get().len();
                            if count > 0 {
                                format!("Update All ({})", count)
                            } else {
                                "Update All".to_string()
                            }
                        }}
                    </button>
                </div>
            </div>

            // Status messages
            {move || {
                if let Some(error) = global_operation_error.get() {
                    view! {
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill_rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip_rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">"Error"</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        {error}
                                    </div>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                } else if let Some(success) = global_operation_success.get() {
                    view! {
                        <div class="bg-green-50 border border-green-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill_rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip_rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800">"Success"</h3>
                                    <div class="mt-2 text-sm text-green-700">
                                        {success}
                                    </div>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    view! { <div></div> }.into_view()
                }
            }}

            // Updates content
            {move || {
                if updates_loading.get() {
                    view! {
                        <div class="flex items-center justify-center py-12">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                            <span class="ml-2 text-gray-600">"Loading updates..."</span>
                        </div>
                    }.into_view()
                } else if let Some(err) = updates_error.get() {
                    view! {
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill_rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip_rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">"Error"</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        {err}
                                    </div>
                                </div>
                            </div>
                        </div>
                    }.into_view()
                } else {
                    let updates = available_updates.get();
                    if updates.is_empty() {
                        view! {
                            <div class="text-center py-12">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">"No updates available"</h3>
                                <p class="mt-1 text-sm text-gray-500">"All packages are up to date."</p>
                            </div>
                        }.into_view()
                    } else {
                        view! {
                            <UpdatesTable
                                updates=updates
                                selected_updates=selected_updates
                                set_selected_updates=set_selected_updates
                                set_pending_operation=set_pending_operation
                                set_show_confirmation=set_show_confirmation
                                updating_packages=updating_packages
                            />
                        }.into_view()
                    }
                }
            }}
        </div>
    }
}

// Updates Table Component
#[component]
fn UpdatesTable(
    updates: Vec<PackageUpdate>,
    selected_updates: ReadSignal<Vec<String>>,
    set_selected_updates: WriteSignal<Vec<String>>,
    set_pending_operation: WriteSignal<Option<(String, String)>>,
    set_show_confirmation: WriteSignal<bool>,
    updating_packages: ReadSignal<bool>,
) -> impl IntoView {
    view! {
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input
                                type="checkbox"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                on:change={
                                    let updates_clone = updates.clone();
                                    move |ev| {
                                        let checked = event_target_checked(&ev);
                                        if checked {
                                            let all_names: Vec<String> = updates_clone.iter().map(|u| u.name.clone()).collect();
                                            set_selected_updates.set(all_names);
                                        } else {
                                            set_selected_updates.set(Vec::new());
                                        }
                                    }
                                }
                            />
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Package"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Current Version"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Available Version"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Size"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Type"
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            "Actions"
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {updates.into_iter().map(|update| {
                        let update_name = update.name.clone();
                        let update_name_for_selected = update_name.clone();
                        let update_name_for_change = update_name.clone();
                        let is_selected = move || selected_updates.get().contains(&update_name_for_selected);

                        view! {
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input
                                        type="checkbox"
                                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        prop:checked=is_selected
                                        on:change=move |ev| {
                                            let checked = event_target_checked(&ev);
                                            let mut current_selected = selected_updates.get();
                                            if checked {
                                                if !current_selected.contains(&update_name_for_change) {
                                                    current_selected.push(update_name_for_change.clone());
                                                }
                                            } else {
                                                current_selected.retain(|name| name != &update_name_for_change);
                                            }
                                            set_selected_updates.set(current_selected);
                                        }
                                    />
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{update.name.clone()}</div>
                                            <div class="text-sm text-gray-500 max-w-xs truncate">{update.description}</div>
                                        </div>
                                        {if update.security_update {
                                            view! {
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill_rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip_rule="evenodd" />
                                                    </svg>
                                                    "Security"
                                                </span>
                                            }.into_view()
                                        } else {
                                            view! { <span></span> }.into_view()
                                        }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div>{update.current_version}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="font-medium text-green-600">{update.available_version}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {format_size(update.size)}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class={format!("inline-flex px-2 py-1 text-xs font-semibold rounded-full {}",
                                        if update.security_update {
                                            "bg-red-100 text-red-800"
                                        } else {
                                            "bg-blue-100 text-blue-800"
                                        }
                                    )}>
                                        {if update.security_update { "Security" } else { "Regular" }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button
                                        class="text-blue-600 hover:text-blue-900 disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled=move || updating_packages.get()
                                        on:click={
                                            let pkg_name = update.name.clone();
                                            move |_| {
                                                set_pending_operation.set(Some(("Update".to_string(), pkg_name.clone())));
                                                set_show_confirmation.set(true);
                                            }
                                        }
                                    >
                                        "Update"
                                    </button>
                                    {if let Some(changelog) = &update.changelog {
                                        let changelog_clone = changelog.clone();
                                        view! {
                                            <button
                                                class="ml-2 text-gray-600 hover:text-gray-900"
                                                title="View changelog"
                                            >
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </button>
                                        }.into_view()
                                    } else {
                                        view! { <span></span> }.into_view()
                                    }}
                                </td>
                            </tr>
                        }
                    }).collect::<Vec<_>>()}
                </tbody>
            </table>
        </div>
    }
}


