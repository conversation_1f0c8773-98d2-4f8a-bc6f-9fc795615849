use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProcessInfo {
    pub pid: u32,
    pub name: String,
    pub user: String,
    pub cpu_percent: f64,
    pub memory_percent: f64,
    pub memory_kb: u64,
    pub status: String,
    pub command: String,
    pub start_time: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProcessStats {
    pub total_processes: u32,
    pub running_processes: u32,
    pub sleeping_processes: u32,
    pub zombie_processes: u32,
    pub processes: Vec<ProcessInfo>,
}

#[component]
pub fn ProcessesPage() -> impl IntoView {
    let (process_stats, set_process_stats) = create_signal(None::<ProcessStats>);
    let (loading, set_loading) = create_signal(true);
    let (error, set_error) = create_signal(None::<String>);
    let (sort_by, set_sort_by) = create_signal("cpu".to_string());
    let (sort_desc, set_sort_desc) = create_signal(true);
    let (filter_text, set_filter_text) = create_signal(String::new());

    // Auto-refresh every 3 seconds
    create_effect(move |_| {
        set_interval(
            move || {
                spawn_local(async move {
                    match fetch_process_stats().await {
                        Ok(stats) => {
                            set_process_stats.set(Some(stats));
                            set_loading.set(false);
                            set_error.set(None);
                        }
                        Err(e) => {
                            set_error.set(Some(format!("Failed to fetch process stats: {}", e)));
                            set_loading.set(false);
                        }
                    }
                });
            },
            Duration::from_secs(3),
        );
    });

    let sorted_filtered_processes = move || {
        if let Some(stats) = process_stats.get() {
            let mut processes = stats.processes;

            // Filter processes
            let filter = filter_text.get().to_lowercase();
            if !filter.is_empty() {
                processes.retain(|p| {
                    p.name.to_lowercase().contains(&filter) ||
                    p.command.to_lowercase().contains(&filter) ||
                    p.user.to_lowercase().contains(&filter)
                });
            }

            // Sort processes
            let sort_field = sort_by.get();
            let desc = sort_desc.get();
            
            processes.sort_by(|a, b| {
                let cmp = match sort_field.as_str() {
                    "pid" => a.pid.cmp(&b.pid),
                    "name" => a.name.cmp(&b.name),
                    "user" => a.user.cmp(&b.user),
                    "cpu" => a.cpu_percent.partial_cmp(&b.cpu_percent).unwrap_or(std::cmp::Ordering::Equal),
                    "memory" => a.memory_percent.partial_cmp(&b.memory_percent).unwrap_or(std::cmp::Ordering::Equal),
                    "status" => a.status.cmp(&b.status),
                    _ => std::cmp::Ordering::Equal,
                };
                
                if desc { cmp.reverse() } else { cmp }
            });
            
            processes
        } else {
            Vec::new()
        }
    };

    view! {
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">
                        "Process Monitor"
                    </h1>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">"Auto-refresh: 3s"</span>
                    </div>
                </div>

                {move || {
                    if loading.get() {
                        view! {
                            <div class="flex items-center justify-center py-12">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <span class="ml-2 text-gray-600">"Loading processes..."</span>
                            </div>
                        }.into_view()
                    } else if let Some(err) = error.get() {
                        view! {
                            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">"Error"</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            {err}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }.into_view()
                    } else if let Some(stats) = process_stats.get() {
                        view! {
                            <div class="space-y-6">
                                <ProcessStatsOverview stats=stats.clone() />
                                <ProcessTable 
                                    processes=sorted_filtered_processes()
                                    sort_by=sort_by
                                    set_sort_by=set_sort_by
                                    sort_desc=sort_desc
                                    set_sort_desc=set_sort_desc
                                    filter_text=filter_text
                                    set_filter_text=set_filter_text
                                />
                            </div>
                        }.into_view()
                    } else {
                        view! {
                            <div class="text-center py-12 text-gray-500">
                                "No process data available"
                            </div>
                        }.into_view()
                    }
                }}
            </div>
        </div>
    }
}

#[component]
fn ProcessStatsOverview(stats: ProcessStats) -> impl IntoView {
    view! {
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-blue-600">"Total Processes"</p>
                        <p class="text-2xl font-bold text-blue-900">{stats.total_processes}</p>
                    </div>
                </div>
            </div>

            <div class="bg-green-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-green-600">"Running"</p>
                        <p class="text-2xl font-bold text-green-900">{stats.running_processes}</p>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-yellow-600">"Sleeping"</p>
                        <p class="text-2xl font-bold text-yellow-900">{stats.sleeping_processes}</p>
                    </div>
                </div>
            </div>

            <div class="bg-red-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-red-600">"Zombie"</p>
                        <p class="text-2xl font-bold text-red-900">{stats.zombie_processes}</p>
                    </div>
                </div>
            </div>
        </div>
    }
}

// Helper function to fetch process stats
async fn fetch_process_stats() -> Result<ProcessStats, String> {
    // Mock implementation - in real system would call backend API
    Ok(ProcessStats {
        total_processes: 45,
        running_processes: 3,
        sleeping_processes: 41,
        zombie_processes: 1,
        processes: vec![
            ProcessInfo {
                pid: 1,
                name: "init".to_string(),
                user: "root".to_string(),
                cpu_percent: 0.0,
                memory_percent: 0.1,
                memory_kb: 1024,
                status: "S".to_string(),
                command: "/sbin/init".to_string(),
                start_time: "00:00:01".to_string(),
            },
            ProcessInfo {
                pid: 123,
                name: "uhttpd".to_string(),
                user: "root".to_string(),
                cpu_percent: 2.5,
                memory_percent: 1.2,
                memory_kb: 2048,
                status: "S".to_string(),
                command: "/usr/sbin/uhttpd -f -h /www -r OpenWrt -x /cgi-bin -u /ubus -t 60 -T 30 -k 20 -A 1 -n 3 -N 100 -R -p 0.0.0.0:80".to_string(),
                start_time: "00:00:15".to_string(),
            },
            ProcessInfo {
                pid: 456,
                name: "dnsmasq".to_string(),
                user: "dnsmasq".to_string(),
                cpu_percent: 0.8,
                memory_percent: 0.5,
                memory_kb: 1536,
                status: "S".to_string(),
                command: "/usr/sbin/dnsmasq -C /var/etc/dnsmasq.conf.cfg01411c -k -x /var/run/dnsmasq/dnsmasq.cfg01411c.pid".to_string(),
                start_time: "00:00:20".to_string(),
            },
        ],
    })
}

#[component]
fn ProcessTable(
    processes: Vec<ProcessInfo>,
    sort_by: ReadSignal<String>,
    set_sort_by: WriteSignal<String>,
    sort_desc: ReadSignal<bool>,
    set_sort_desc: WriteSignal<bool>,
    filter_text: ReadSignal<String>,
    set_filter_text: WriteSignal<String>,
) -> impl IntoView {
    let handle_sort = move |field: &str| {
        if sort_by.get() == field {
            set_sort_desc.set(!sort_desc.get());
        } else {
            set_sort_by.set(field.to_string());
            set_sort_desc.set(true);
        }
    };

    let sort_icon = move |field: &str| {
        if sort_by.get() == field {
            if sort_desc.get() {
                "↓"
            } else {
                "↑"
            }
        } else {
            ""
        }
    };

    view! {
        <div class="bg-white rounded-lg border border-gray-200">
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">"Process List"</h3>
                    <div class="flex items-center space-x-2">
                        <input
                            type="text"
                            placeholder="Filter processes..."
                            class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            prop:value=move || filter_text.get()
                            on:input=move |ev| {
                                set_filter_text.set(event_target_value(&ev));
                            }
                        />
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th
                                class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                on:click=move |_| handle_sort("pid")
                            >
                                "PID " {sort_icon("pid")}
                            </th>
                            <th
                                class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                on:click=move |_| handle_sort("name")
                            >
                                "Name " {sort_icon("name")}
                            </th>
                            <th
                                class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                on:click=move |_| handle_sort("user")
                            >
                                "User " {sort_icon("user")}
                            </th>
                            <th
                                class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                on:click=move |_| handle_sort("cpu")
                            >
                                "CPU % " {sort_icon("cpu")}
                            </th>
                            <th
                                class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                on:click=move |_| handle_sort("memory")
                            >
                                "Memory % " {sort_icon("memory")}
                            </th>
                            <th
                                class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                on:click=move |_| handle_sort("status")
                            >
                                "Status " {sort_icon("status")}
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                "Command"
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {processes.into_iter().map(|process| {
                            view! {
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {process.pid}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        {process.name}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        {process.user}
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div
                                                    class="bg-blue-600 h-2 rounded-full"
                                                    style:width=format!("{}%", process.cpu_percent.min(100.0))
                                                ></div>
                                            </div>
                                            <span class="text-xs">{format!("{:.1}%", process.cpu_percent)}</span>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div
                                                    class="bg-green-600 h-2 rounded-full"
                                                    style:width=format!("{}%", process.memory_percent.min(100.0))
                                                ></div>
                                            </div>
                                            <span class="text-xs">{format!("{:.1}%", process.memory_percent)}</span>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class={format!("inline-flex px-2 py-1 text-xs font-semibold rounded-full {}",
                                            match process.status.as_str() {
                                                "R" => "bg-green-100 text-green-800",
                                                "S" => "bg-blue-100 text-blue-800",
                                                "D" => "bg-yellow-100 text-yellow-800",
                                                "Z" => "bg-red-100 text-red-800",
                                                "T" => "bg-gray-100 text-gray-800",
                                                _ => "bg-gray-100 text-gray-800",
                                            }
                                        )}>
                                            {match process.status.as_str() {
                                                "R" => "Running".to_string(),
                                                "S" => "Sleeping".to_string(),
                                                "D" => "Waiting".to_string(),
                                                "Z" => "Zombie".to_string(),
                                                "T" => "Stopped".to_string(),
                                                _ => process.status.clone(),
                                            }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-900">
                                        <div class="max-w-xs truncate" title=process.command.clone()>
                                            {process.command}
                                        </div>
                                    </td>
                                </tr>
                            }
                        }).collect::<Vec<_>>()}
                    </tbody>
                </table>
            </div>
        </div>
    }
}
