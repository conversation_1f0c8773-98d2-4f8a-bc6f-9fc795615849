//! User profile page
//!
//! Displays user information, role, permissions, and allows basic profile management

use leptos::*;
use crate::auth::{use_auth_context, RequirePermission, AdminOnly};

/// User profile page component
#[component]
pub fn ProfilePage() -> impl IntoView {
    let auth = use_auth_context();
    
    view! {
        <div class="container mx-auto px-4 py-6">
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-gray-900">"User Profile"</h1>
                <p class="mt-2 text-sm text-gray-600">"View and manage your account information"</p>
            </div>
            <div class="max-w-4xl mx-auto">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">
                            "Profile Information"
                        </h3>
                        
                        {move || {
                            let auth_state = auth.state.get();
                            if let Some(user) = auth_state.user {
                                view! {
                                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                        // Basic Information
                                        <div class="col-span-2">
                                            <h4 class="text-md font-medium text-gray-900 mb-4">
                                                "Basic Information"
                                            </h4>
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                                                    <div>
                                                        <dt class="text-sm font-medium text-gray-500">
                                                            "Username"
                                                        </dt>
                                                        <dd class="mt-1 text-sm text-gray-900">
                                                            {user.username.clone()}
                                                        </dd>
                                                    </div>
                                                    <div>
                                                        <dt class="text-sm font-medium text-gray-500">
                                                            "Role"
                                                        </dt>
                                                        <dd class="mt-1">
                                                            <span class=format!("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {}",
                                                                match user.role.as_str() {
                                                                    "admin" => "bg-red-100 text-red-800",
                                                                    "user" => "bg-blue-100 text-blue-800",
                                                                    "guest" => "bg-gray-100 text-gray-800",
                                                                    _ => "bg-gray-100 text-gray-800"
                                                                }
                                                            )>
                                                                {user.role.clone()}
                                                            </span>
                                                        </dd>
                                                    </div>
                                                    <div>
                                                        <dt class="text-sm font-medium text-gray-500">
                                                            "Last Login"
                                                        </dt>
                                                        <dd class="mt-1 text-sm text-gray-900">
                                                            {user.last_login.map(|dt| dt.format("%Y-%m-%d %H:%M:%S UTC").to_string())
                                                                .unwrap_or_else(|| "Never".to_string())}
                                                        </dd>
                                                    </div>
                                                    <div>
                                                        <dt class="text-sm font-medium text-gray-500">
                                                            "Session Status"
                                                        </dt>
                                                        <dd class="mt-1">
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                "Active"
                                                            </span>
                                                        </dd>
                                                    </div>
                                                </dl>
                                            </div>
                                        </div>
                                        
                                        // Permissions
                                        <div class="col-span-2">
                                            <h4 class="text-md font-medium text-gray-900 mb-4">
                                                "Permissions"
                                            </h4>
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <div class="flex flex-wrap gap-2">
                                                    {user.permissions.iter().map(|permission| {
                                                        view! {
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                                {permission.clone()}
                                                            </span>
                                                        }
                                                    }).collect::<Vec<_>>()}
                                                </div>
                                                {if user.permissions.is_empty() {
                                                    view! {
                                                        <p class="text-sm text-gray-500 italic">
                                                            "No permissions assigned"
                                                        </p>
                                                    }.into_view()
                                                } else {
                                                    view! { <div></div> }.into_view()
                                                }}
                                            </div>
                                        </div>
                                        
                                        // Actions
                                        <div class="col-span-2">
                                            <h4 class="text-md font-medium text-gray-900 mb-4">
                                                "Actions"
                                            </h4>
                                            <div class="flex flex-wrap gap-3">
                                                <button class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                    "Change Password"
                                                </button>
                                                
                                                <RequirePermission permission="system.read".to_string()>
                                                    <a href="/system" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                        "System Status"
                                                    </a>
                                                </RequirePermission>
                                                
                                                <AdminOnly>
                                                    <a href="/administration" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                                        "Administration"
                                                    </a>
                                                </AdminOnly>
                                            </div>
                                        </div>
                                    </div>
                                }.into_view()
                            } else {
                                view! {
                                    <div class="text-center py-12">
                                        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-gray-100">
                                            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">
                                            "No user information available"
                                        </h3>
                                        <p class="mt-1 text-sm text-gray-500">
                                            "Please log in to view your profile."
                                        </p>
                                        <div class="mt-6">
                                            <a href="/login" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                                "Login"
                                            </a>
                                        </div>
                                    </div>
                                }.into_view()
                            }
                        }}
                    </div>
                </div>
            </div>
        </div>
    }
}
