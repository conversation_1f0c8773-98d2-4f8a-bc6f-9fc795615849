use leptos::*;
use leptos_meta::*;
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SystemStatus {
    pub hostname: String,
    pub uptime: String,
    pub load_average: (f64, f64, f64),
    pub cpu_usage: f64,
    pub memory_total: u64,
    pub memory_used: u64,
    pub memory_free: u64,
    pub storage_usage: Vec<StorageInfo>,
    pub network_interfaces: Vec<NetworkInterface>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StorageInfo {
    pub device: String,
    pub mount_point: String,
    pub total: u64,
    pub used: u64,
    pub available: u64,
    pub usage_percent: f64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NetworkInterface {
    pub name: String,
    pub status: String,
    pub ip_address: Option<String>,
    pub rx_bytes: u64,
    pub tx_bytes: u64,
    pub rx_packets: u64,
    pub tx_packets: u64,
}

#[component]
pub fn StatusPage() -> impl IntoView {
    let (system_status, set_system_status) = create_signal(None::<SystemStatus>);
    let (loading, set_loading) = create_signal(true);
    let (error, set_error) = create_signal(None::<String>);

    // Auto-refresh every 5 seconds
    create_effect(move |_| {
        set_interval(
            move || {
                spawn_local(async move {
                    match fetch_system_status().await {
                        Ok(status) => {
                            set_system_status.set(Some(status));
                            set_loading.set(false);
                            set_error.set(None);
                        }
                        Err(e) => {
                            set_error.set(Some(format!("Failed to fetch system status: {}", e)));
                            set_loading.set(false);
                        }
                    }
                });
            },
            Duration::from_secs(5),
        );
    });

    view! {
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">
                        "System Status"
                    </h1>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">"Auto-refresh: 5s"</span>
                    </div>
                </div>

                {move || {
                    if loading.get() {
                        view! {
                            <div class="flex items-center justify-center py-12">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                <span class="ml-2 text-gray-600">"Loading system status..."</span>
                            </div>
                        }.into_view()
                    } else if let Some(err) = error.get() {
                        view! {
                            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">"Error"</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            {err}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }.into_view()
                    } else if let Some(status) = system_status.get() {
                        view! {
                            <div class="space-y-6">
                                <SystemOverview status=status.clone() />
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <CpuMemoryCard status=status.clone() />
                                    <StorageCard storage_info=status.storage_usage />
                                </div>
                                <NetworkInterfacesCard interfaces=status.network_interfaces />
                            </div>
                        }.into_view()
                    } else {
                        view! {
                            <div class="text-center py-12 text-gray-500">
                                "No system status data available"
                            </div>
                        }.into_view()
                    }
                }}
            </div>
        </div>
    }
}

#[component]
fn SystemOverview(status: SystemStatus) -> impl IntoView {
    view! {
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-blue-600">"Hostname"</p>
                        <p class="text-lg font-semibold text-blue-900">{status.hostname}</p>
                    </div>
                </div>
            </div>

            <div class="bg-green-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-green-600">"Uptime"</p>
                        <p class="text-lg font-semibold text-green-900">{status.uptime}</p>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-yellow-600">"Load Average"</p>
                        <p class="text-lg font-semibold text-yellow-900">
                            {format!("{:.2}", status.load_average.0)}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-purple-50 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-purple-600">"CPU Usage"</p>
                        <p class="text-lg font-semibold text-purple-900">
                            {format!("{:.1}%", status.cpu_usage)}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    }
}

#[component]
fn CpuMemoryCard(status: SystemStatus) -> impl IntoView {
    let memory_usage_percent = (status.memory_used as f64 / status.memory_total as f64) * 100.0;

    view! {
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"CPU & Memory"</h3>

            <div class="space-y-4">
                <div>
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                        <span>"CPU Usage"</span>
                        <span>{format!("{:.1}%", status.cpu_usage)}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style:width=format!("{}%", status.cpu_usage)
                        ></div>
                    </div>
                </div>

                <div>
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                        <span>"Memory Usage"</span>
                        <span>{format!("{:.1}%", memory_usage_percent)}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div
                            class="bg-green-600 h-2 rounded-full transition-all duration-300"
                            style:width=format!("{}%", memory_usage_percent)
                        ></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{format!("Used: {}", format_bytes(status.memory_used))}</span>
                        <span>{format!("Total: {}", format_bytes(status.memory_total))}</span>
                    </div>
                </div>

                <div class="pt-2 border-t border-gray-100">
                    <div class="text-sm text-gray-600">
                        <div class="flex justify-between">
                            <span>"Load Average:"</span>
                            <span>{format!("{:.2}, {:.2}, {:.2}", status.load_average.0, status.load_average.1, status.load_average.2)}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}

#[component]
fn StorageCard(storage_info: Vec<StorageInfo>) -> impl IntoView {
    view! {
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"Storage"</h3>

            <div class="space-y-4">
                {storage_info.into_iter().map(|storage| {
                    view! {
                        <div class="border-b border-gray-100 pb-3 last:border-b-0 last:pb-0">
                            <div class="flex justify-between items-center mb-2">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{storage.device}</p>
                                    <p class="text-xs text-gray-500">{storage.mount_point}</p>
                                </div>
                                <span class="text-sm font-medium text-gray-900">
                                    {format!("{:.1}%", storage.usage_percent)}
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    class={format!("h-2 rounded-full transition-all duration-300 {}",
                                        if storage.usage_percent > 90.0 { "bg-red-600" }
                                        else if storage.usage_percent > 75.0 { "bg-yellow-600" }
                                        else { "bg-green-600" }
                                    )}
                                    style:width=format!("{}%", storage.usage_percent)
                                ></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>{format!("Used: {}", format_bytes(storage.used))}</span>
                                <span>{format!("Available: {}", format_bytes(storage.available))}</span>
                            </div>
                        </div>
                    }
                }).collect::<Vec<_>>()}
            </div>
        </div>
    }
}

#[component]
fn NetworkInterfacesCard(interfaces: Vec<NetworkInterface>) -> impl IntoView {
    view! {
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">"Network Interfaces"</h3>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"Interface"</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"Status"</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"IP Address"</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"RX/TX Bytes"</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">"RX/TX Packets"</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {interfaces.into_iter().map(|interface| {
                            view! {
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {interface.name}
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class={format!("inline-flex px-2 py-1 text-xs font-semibold rounded-full {}",
                                            if interface.status == "up" { "bg-green-100 text-green-800" }
                                            else { "bg-red-100 text-red-800" }
                                        )}>
                                            {interface.status}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                        {interface.ip_address.unwrap_or_else(|| "N/A".to_string())}
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                        <div class="text-xs">
                                            <div>{format!("RX: {}", format_bytes(interface.rx_bytes))}</div>
                                            <div>{format!("TX: {}", format_bytes(interface.tx_bytes))}</div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                        <div class="text-xs">
                                            <div>{format!("RX: {}", interface.rx_packets)}</div>
                                            <div>{format!("TX: {}", interface.tx_packets)}</div>
                                        </div>
                                    </td>
                                </tr>
                            }
                        }).collect::<Vec<_>>()}
                    </tbody>
                </table>
            </div>
        </div>
    }
}

// Helper functions
async fn fetch_system_status() -> Result<SystemStatus, String> {
    // Mock implementation - in real system would call backend API
    Ok(SystemStatus {
        hostname: "OpenWrt".to_string(),
        uptime: "2 days, 14:32:15".to_string(),
        load_average: (0.45, 0.32, 0.28),
        cpu_usage: 15.3,
        memory_total: 134217728, // 128MB
        memory_used: 67108864,   // 64MB
        memory_free: 67108864,   // 64MB
        storage_usage: vec![
            StorageInfo {
                device: "/dev/root".to_string(),
                mount_point: "/".to_string(),
                total: 16777216, // 16MB
                used: 8388608,   // 8MB
                available: 8388608, // 8MB
                usage_percent: 50.0,
            },
            StorageInfo {
                device: "/dev/sda1".to_string(),
                mount_point: "/overlay".to_string(),
                total: 134217728, // 128MB
                used: 33554432,   // 32MB
                available: 100663296, // 96MB
                usage_percent: 25.0,
            },
        ],
        network_interfaces: vec![
            NetworkInterface {
                name: "eth0".to_string(),
                status: "up".to_string(),
                ip_address: Some("***********".to_string()),
                rx_bytes: 1048576,
                tx_bytes: 2097152,
                rx_packets: 1024,
                tx_packets: 2048,
            },
            NetworkInterface {
                name: "wlan0".to_string(),
                status: "up".to_string(),
                ip_address: Some("***********".to_string()),
                rx_bytes: 524288,
                tx_bytes: 1048576,
                rx_packets: 512,
                tx_packets: 1024,
            },
        ],
    })
}

fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}
