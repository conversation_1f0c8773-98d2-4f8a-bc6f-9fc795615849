{"rustc": 12610991425282158916, "features": "[\"default\", \"eventsource\", \"futures-channel\", \"futures-core\", \"futures-sink\", \"http\", \"json\", \"pin-project\", \"serde\", \"serde_json\", \"websocket\"]", "declared_features": "[\"default\", \"eventsource\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-sink\", \"http\", \"io-util\", \"json\", \"pin-project\", \"serde\", \"serde_json\", \"websocket\"]", "target": 7289951416308014359, "profile": 8276155916380437441, "path": 9145437262956966740, "deps": [[1811549171721445101, "futures_channel", false, 1044082373667107368], [5921074888975346911, "gloo_utils", false, 17460812588808637289], [6264115378959545688, "pin_project", false, 11758547709718205563], [6946689283190175495, "wasm_bindgen", false, 17530803955315420270], [7013762810557009322, "futures_sink", false, 9019461531452306766], [7620660491849607393, "futures_core", false, 13580037134113986251], [8008191657135824715, "thiserror", false, 14771840948386242388], [8264480821543757363, "web_sys", false, 421413717215833395], [9003359908906038687, "js_sys", false, 11935293037950106783], [9010263965687315507, "http", false, 12613709012635136463], [9689903380558560274, "serde", false, 14136723675802054954], [15367738274754116744, "serde_json", false, 13238213450351272871], [15917073803248137067, "wasm_bindgen_futures", false, 5307195874015591304]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/gloo-net-28c09c55c6a3ab72/dep-lib-gloo_net", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}