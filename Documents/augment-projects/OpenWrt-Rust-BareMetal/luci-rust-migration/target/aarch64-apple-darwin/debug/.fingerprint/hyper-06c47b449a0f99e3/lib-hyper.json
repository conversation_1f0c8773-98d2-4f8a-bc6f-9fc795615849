{"rustc": 12610991425282158916, "features": "[\"client\", \"h2\", \"http1\", \"http2\", \"runtime\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 8276155916380437441, "path": 5321469031469459121, "deps": [[784494742817713399, "tower_service", false, 4442681223396543130], [1569313478171189446, "want", false, 8407675578482833991], [1811549171721445101, "futures_channel", false, 1044082373667107368], [1906322745568073236, "pin_project_lite", false, 134321414572171600], [4405182208873388884, "http", false, 4137392743471393626], [6163892036024256188, "httparse", false, 1507164316282709505], [6304235478050270880, "httpdate", false, 14719243838630256911], [7620660491849607393, "futures_core", false, 13580037134113986251], [7695812897323945497, "itoa", false, 18086373007184758078], [8606274917505247608, "tracing", false, 14221329219701348664], [8915503303801890683, "http_body", false, 12274096530611768825], [9538054652646069845, "tokio", false, 13251216373673314819], [10629569228670356391, "futures_util", false, 15124213411273824859], [12614995553916589825, "socket2", false, 16362740243570776839], [13809605890706463735, "h2", false, 5897280212018674316], [16066129441945555748, "bytes", false, 4565292215235062127]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/hyper-06c47b449a0f99e3/dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}