{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"default\", \"experimental-islands\", \"nonce\", \"wasm\"]", "target": 8785207715516110154, "profile": 5347358027863023418, "path": 10746707689035322381, "deps": [[525748644567801861, "leptos", false, 8977559099792636064], [1288403060204016458, "tokio_util", false, 16575681408375096346], [2440331656928566749, "leptos_router", false, 3846989787498446726], [2706460456408817945, "futures", false, 16405549148115417185], [2828590642173593838, "cfg_if", false, 13215375576315396321], [3722963349756955755, "once_cell", false, 2981104805269227908], [4495526598637097934, "parking_lot", false, 12561738753119538980], [4891297352905791595, "axum", false, 7171726167141983924], [6237836609150710667, "leptos_meta", false, 17365345707170920728], [8456283572100695936, "leptos_macro", false, 18129317164637858491], [8606274917505247608, "tracing", false, 12040434844566102166], [9529541572801435613, "leptos_integration_utils", false, 13583216373157135661], [9538054652646069845, "tokio", false, 2828153908726863307], [10749053540396391216, "server_fn", false, 17025580323073935729], [15367738274754116744, "serde_json", false, 17312044369037065190], [16900715236047033623, "http_body_util", false, 1309655904085010339]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/leptos_axum-4b9658bab349626d/dep-lib-leptos_axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}