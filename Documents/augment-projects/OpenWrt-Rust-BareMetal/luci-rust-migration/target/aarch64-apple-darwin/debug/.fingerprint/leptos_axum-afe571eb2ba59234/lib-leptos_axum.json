{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"default\", \"experimental-islands\", \"nonce\", \"wasm\"]", "target": 8785207715516110154, "profile": 8276155916380437441, "path": 10746707689035322381, "deps": [[525748644567801861, "leptos", false, 779030475900784625], [1288403060204016458, "tokio_util", false, 17510627985722106482], [2440331656928566749, "leptos_router", false, 1848232871718855022], [2706460456408817945, "futures", false, 8783718116383234232], [2828590642173593838, "cfg_if", false, 16056165954977868191], [3722963349756955755, "once_cell", false, 334531857116249603], [4495526598637097934, "parking_lot", false, 18046286822454439376], [4891297352905791595, "axum", false, 18177713851176775773], [6237836609150710667, "leptos_meta", false, 14137807754954147465], [8456283572100695936, "leptos_macro", false, 18129317164637858491], [8606274917505247608, "tracing", false, 16308205881308244551], [9529541572801435613, "leptos_integration_utils", false, 13662057286483393965], [9538054652646069845, "tokio", false, 10861959258753612748], [10749053540396391216, "server_fn", false, 8035146636845458194], [15367738274754116744, "serde_json", false, 13238213450351272871], [16900715236047033623, "http_body_util", false, 1987702712407650247]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/leptos_axum-afe571eb2ba59234/dep-lib-leptos_axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}