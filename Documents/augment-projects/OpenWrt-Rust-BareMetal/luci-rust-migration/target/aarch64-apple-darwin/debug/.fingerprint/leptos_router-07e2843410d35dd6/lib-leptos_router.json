{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"csr\", \"default\", \"hydrate\", \"nightly\", \"ssr\"]", "target": 1540691908399907605, "profile": 8276155916380437441, "path": 2582915815788084495, "deps": [[40386456601120721, "percent_encoding", false, 12768663558983198052], [525748644567801861, "leptos", false, 13020853765601430723], [2828590642173593838, "cfg_if", false, 16056165954977868191], [3722963349756955755, "once_cell", false, 334531857116249603], [6946689283190175495, "wasm_bindgen", false, 17530803955315420270], [7026509717486028242, "linear_map", false, 571202978196058613], [8008191657135824715, "thiserror", false, 14771840948386242388], [8264480821543757363, "web_sys", false, 421413717215833395], [8485763786069017691, "send_wrapper", false, 10935145334691261748], [8606274917505247608, "tracing", false, 14221329219701348664], [8718487246451984807, "serde_qs", false, 101611507392730789], [9003359908906038687, "js_sys", false, 11935293037950106783], [9689903380558560274, "serde", false, 6119772168628250630], [12744280046734151787, "gloo_net", false, 6343082452668639506], [14931062873021150766, "itertools", false, 9625536216333480304], [15367738274754116744, "serde_json", false, 2194317094359706874], [15917073803248137067, "wasm_bindgen_futures", false, 5307195874015591304], [17917672826516349275, "lazy_static", false, 15320713664561047914]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/leptos_router-07e2843410d35dd6/dep-lib-leptos_router", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}