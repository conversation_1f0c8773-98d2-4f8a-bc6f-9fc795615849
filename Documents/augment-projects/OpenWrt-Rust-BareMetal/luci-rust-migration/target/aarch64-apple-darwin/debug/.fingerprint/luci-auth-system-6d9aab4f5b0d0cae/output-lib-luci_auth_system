{"$message_type":"diagnostic","message":"unused import: `Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":365,"byte_end":373,"line_start":10,"line_end":10,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{Duration, Utc};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":365,"byte_end":375,"line_start":10,"line_end":10,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"use chrono::{Duration, Utc};","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/auth-system/src/lib.rs","byte_start":364,"byte_end":365,"line_start":10,"line_end":10,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use chrono::{Duration, Utc};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/auth-system/src/lib.rs","byte_start":378,"byte_end":379,"line_start":10,"line_end":10,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"use chrono::{Duration, Utc};","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Duration`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/auth-system/src/lib.rs:10:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{Duration, Utc};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Algorithm`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":400,"byte_end":409,"line_start":11,"line_end":11,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"use jsonwebtoken::{Algorithm, DecodingKey, EncodingKey};","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":400,"byte_end":411,"line_start":11,"line_end":11,"column_start":20,"column_end":31,"is_primary":true,"text":[{"text":"use jsonwebtoken::{Algorithm, DecodingKey, EncodingKey};","highlight_start":20,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Algorithm`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/auth-system/src/lib.rs:11:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse jsonwebtoken::{Algorithm, DecodingKey, EncodingKey};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Rng` and `thread_rng`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":449,"byte_end":452,"line_start":12,"line_end":12,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"use rand::{Rng, thread_rng};","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/auth-system/src/lib.rs","byte_start":454,"byte_end":464,"line_start":12,"line_end":12,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"use rand::{Rng, thread_rng};","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":438,"byte_end":467,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rand::{Rng, thread_rng};","highlight_start":1,"highlight_end":29},{"text":"use sha2::{Sha256, Digest};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Rng` and `thread_rng`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/auth-system/src/lib.rs:12:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand::{Rng, thread_rng};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Sha256`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":478,"byte_end":484,"line_start":13,"line_end":13,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest};","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":478,"byte_end":486,"line_start":13,"line_end":13,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest};","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/auth-system/src/lib.rs","byte_start":477,"byte_end":478,"line_start":13,"line_end":13,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest};","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/auth-system/src/lib.rs","byte_start":492,"byte_end":493,"line_start":13,"line_end":13,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Sha256`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/auth-system/src/lib.rs:13:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sha2::{Sha256, Digest};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Digest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":486,"byte_end":492,"line_start":13,"line_end":13,"column_start":20,"column_end":26,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest};","highlight_start":20,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Digest`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/auth-system/src/lib.rs:13:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sha2::{Sha256, Digest};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `encoding_key` and `decoding_key` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"crates/auth-system/src/lib.rs","byte_start":3295,"byte_end":3306,"line_start":119,"line_end":119,"column_start":12,"column_end":23,"is_primary":false,"text":[{"text":"pub struct AuthManager {","highlight_start":12,"highlight_end":23}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/auth-system/src/lib.rs","byte_start":3437,"byte_end":3449,"line_start":123,"line_end":123,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    encoding_key: EncodingKey,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/auth-system/src/lib.rs","byte_start":3468,"byte_end":3480,"line_start":124,"line_end":124,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    decoding_key: DecodingKey,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `encoding_key` and `decoding_key` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/auth-system/src/lib.rs:123:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct AuthManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    encoding_key: EncodingKey,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    decoding_key: DecodingKey,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"6 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 6 warnings emitted\u001b[0m\n\n"}
