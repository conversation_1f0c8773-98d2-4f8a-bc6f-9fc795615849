{"rustc": 12610991425282158916, "features": "[\"async\", \"database\", \"default\", \"futures\", \"sqlx\", \"tokio\"]", "declared_features": "[\"async\", \"database\", \"default\", \"embedded\", \"futures\", \"heapless\", \"sqlx\", \"tokio\"]", "target": 18357653355406880527, "profile": 6675295047989516842, "path": 9924352993126883023, "deps": [[2706460456408817945, "futures", false, 16405549148115417185], [2737927996754702673, "jsonwebtoken", false, 17425370530173345638], [4914321236340703631, "bcrypt", false, 6151521654624295893], [5986029879202738730, "log", false, 1770106447332161632], [8008191657135824715, "thiserror", false, 2760009951614222754], [8319709847752024821, "uuid", false, 12860498545333432582], [9451456094439810778, "regex", false, 15666539926519917650], [9538054652646069845, "tokio", false, 2828153908726863307], [9689903380558560274, "serde", false, 10546623119610134092], [9857275760291862238, "sha2", false, 6519965631980054170], [9897246384292347999, "chrono", false, 3747316119217821086], [10632374999838431203, "sqlx", false, 7998244109993365861], [12984406303388944089, "luci_shared_types", false, 3819879196415779974], [13208667028893622512, "rand", false, 10857151090897136474], [13625485746686963219, "anyhow", false, 10653682492695522482], [14940550291033027160, "luci_utilities", false, 8855748283049653425], [15367738274754116744, "serde_json", false, 17312044369037065190]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/luci-auth-system-89c8fc21b777fca8/dep-lib-luci_auth_system", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}