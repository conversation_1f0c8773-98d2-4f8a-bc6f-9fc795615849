{"rustc": 12610991425282158916, "features": "[\"async\", \"default\", \"futures\", \"tokio\"]", "declared_features": "[\"async\", \"default\", \"embedded\", \"futures\", \"heapless\", \"tokio\"]", "target": 9547349434854658631, "profile": 2330448797067240312, "path": 15797202546639175259, "deps": [[2706460456408817945, "futures", false, 12406829981487485577], [4684437522915235464, "libc", false, 14620522789151788272], [5986029879202738730, "log", false, 10834319452123524630], [6462525672642925639, "nix", false, 8163555703253377662], [8008191657135824715, "thiserror", false, 14771840948386242388], [8319709847752024821, "uuid", false, 4341501060146528473], [9538054652646069845, "tokio", false, 13251216373673314819], [9689903380558560274, "serde", false, 6119772168628250630], [9897246384292347999, "chrono", false, 17517462538754318758], [12984406303388944089, "luci_shared_types", false, 8745958740660232182], [13625485746686963219, "anyhow", false, 16700660540025210199], [14940550291033027160, "luci_utilities", false, 10307756734611131706], [15367738274754116744, "serde_json", false, 2194317094359706874], [16126725806440309987, "build_script_build", false, 12626801190824115005]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/luci-opkg-integration-0d098dc14de1ff08/dep-lib-luci_opkg_integration", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}