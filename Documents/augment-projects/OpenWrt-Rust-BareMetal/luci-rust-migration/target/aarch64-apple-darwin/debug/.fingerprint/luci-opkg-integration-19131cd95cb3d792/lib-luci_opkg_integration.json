{"rustc": 12610991425282158916, "features": "[\"async\", \"default\", \"futures\", \"tokio\"]", "declared_features": "[\"async\", \"default\", \"embedded\", \"futures\", \"heapless\", \"tokio\"]", "target": 9547349434854658631, "profile": 2330448797067240312, "path": 15797202546639175259, "deps": [[2706460456408817945, "futures", false, 8783718116383234232], [4684437522915235464, "libc", false, 14620522789151788272], [5986029879202738730, "log", false, 10834319452123524630], [6462525672642925639, "nix", false, 11037032703162117191], [8008191657135824715, "thiserror", false, 14771840948386242388], [8319709847752024821, "uuid", false, 6395955376148155462], [9538054652646069845, "tokio", false, 10861959258753612748], [9689903380558560274, "serde", false, 14136723675802054954], [9897246384292347999, "chrono", false, 15840396973297443996], [12984406303388944089, "luci_shared_types", false, 3431715223289523197], [13625485746686963219, "anyhow", false, 16700660540025210199], [14940550291033027160, "luci_utilities", false, 6773133744705940263], [15367738274754116744, "serde_json", false, 13238213450351272871], [16126725806440309987, "build_script_build", false, 12626801190824115005]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/luci-opkg-integration-19131cd95cb3d792/dep-lib-luci_opkg_integration", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}