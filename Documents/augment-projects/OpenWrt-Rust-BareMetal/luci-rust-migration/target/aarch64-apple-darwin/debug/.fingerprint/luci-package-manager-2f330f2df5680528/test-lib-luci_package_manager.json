{"rustc": 12610991425282158916, "features": "[\"async\", \"default\", \"futures\", \"tokio\"]", "declared_features": "[\"async\", \"default\", \"embedded\", \"futures\", \"heapless\", \"tokio\"]", "target": 1366838123137205443, "profile": 15057526963834790232, "path": 12927362672432392719, "deps": [[2706460456408817945, "futures", false, 16405549148115417185], [5986029879202738730, "log", false, 1770106447332161632], [8008191657135824715, "thiserror", false, 2760009951614222754], [8319709847752024821, "uuid", false, 12860498545333432582], [9538054652646069845, "tokio", false, 2828153908726863307], [9689903380558560274, "serde", false, 10546623119610134092], [9897246384292347999, "chrono", false, 3747316119217821086], [12984406303388944089, "luci_shared_types", false, 3819879196415779974], [13625485746686963219, "anyhow", false, 10653682492695522482], [14940550291033027160, "luci_utilities", false, 8855748283049653425], [15367738274754116744, "serde_json", false, 17312044369037065190], [16126725806440309987, "luci_opkg_integration", false, 17352876577617790360]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/luci-package-manager-2f330f2df5680528/dep-test-lib-luci_package_manager", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}