{"rustc": 12610991425282158916, "features": "[\"async\", \"default\", \"futures\", \"tokio\"]", "declared_features": "[\"async\", \"default\", \"embedded\", \"futures\", \"heapless\", \"tokio\"]", "target": 1366838123137205443, "profile": 2330448797067240312, "path": 12927362672432392719, "deps": [[2706460456408817945, "futures", false, 8783718116383234232], [5986029879202738730, "log", false, 10834319452123524630], [8008191657135824715, "thiserror", false, 14771840948386242388], [8319709847752024821, "uuid", false, 6395955376148155462], [9538054652646069845, "tokio", false, 10861959258753612748], [9689903380558560274, "serde", false, 14136723675802054954], [9897246384292347999, "chrono", false, 15840396973297443996], [12984406303388944089, "luci_shared_types", false, 3431715223289523197], [13625485746686963219, "anyhow", false, 16700660540025210199], [14940550291033027160, "luci_utilities", false, 6773133744705940263], [15367738274754116744, "serde_json", false, 13238213450351272871], [16126725806440309987, "luci_opkg_integration", false, 2667152312120443292]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/luci-package-manager-921e410ae1def9dc/dep-lib-luci_package_manager", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}