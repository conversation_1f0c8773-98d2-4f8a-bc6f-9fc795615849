{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/lib.rs","byte_start":240,"byte_end":265,"line_start":7,"line_end":7,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/lib.rs","byte_start":236,"byte_end":267,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use std::time::{Duration, SystemTime, UNIX_EPOCH};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/lib.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UNIX_EPOCH`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/lib.rs","byte_start":305,"byte_end":315,"line_start":8,"line_end":8,"column_start":39,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{Duration, SystemTime, UNIX_EPOCH};","highlight_start":39,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/lib.rs","byte_start":303,"byte_end":315,"line_start":8,"line_end":8,"column_start":37,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{Duration, SystemTime, UNIX_EPOCH};","highlight_start":37,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UNIX_EPOCH`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/lib.rs:8:39\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{Duration, SystemTime, UNIX_EPOCH};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `luci_shared_types::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/lib.rs","byte_start":344,"byte_end":364,"line_start":10,"line_end":10,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"use luci_shared_types::*;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/lib.rs","byte_start":340,"byte_end":366,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use luci_shared_types::*;","highlight_start":1,"highlight_end":26},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `luci_shared_types::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/lib.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse luci_shared_types::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/cpu.rs","byte_start":223,"byte_end":231,"line_start":8,"line_end":8,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/cpu.rs","byte_start":223,"byte_end":233,"line_start":8,"line_end":8,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/cpu.rs","byte_start":222,"byte_end":223,"line_start":8,"line_end":8,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/cpu.rs","byte_start":240,"byte_end":241,"line_start":8,"line_end":8,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/cpu.rs:8:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{Duration, Instant};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `MonitorError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/memory.rs","byte_start":115,"byte_end":127,"line_start":5,"line_end":5,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/memory.rs","byte_start":115,"byte_end":129,"line_start":5,"line_end":5,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/memory.rs","byte_start":114,"byte_end":115,"line_start":5,"line_end":5,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/memory.rs","byte_start":142,"byte_end":143,"line_start":5,"line_end":5,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `MonitorError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/memory.rs:5:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{MonitorError, MonitorResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/network.rs","byte_start":234,"byte_end":242,"line_start":8,"line_end":8,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/network.rs","byte_start":234,"byte_end":244,"line_start":8,"line_end":8,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/network.rs","byte_start":233,"byte_end":234,"line_start":8,"line_end":8,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/network.rs","byte_start":251,"byte_end":252,"line_start":8,"line_end":8,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/network.rs:8:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{Duration, Instant};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `MonitorError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/system.rs","byte_start":103,"byte_end":115,"line_start":5,"line_end":5,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/system.rs","byte_start":103,"byte_end":117,"line_start":5,"line_end":5,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/system.rs","byte_start":102,"byte_end":103,"line_start":5,"line_end":5,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/system.rs","byte_start":130,"byte_end":131,"line_start":5,"line_end":5,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `MonitorError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/system.rs:5:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{MonitorError, MonitorResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UNIX_EPOCH`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/system.rs","byte_start":208,"byte_end":218,"line_start":7,"line_end":7,"column_start":39,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{Duration, SystemTime, UNIX_EPOCH};","highlight_start":39,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/system.rs","byte_start":206,"byte_end":218,"line_start":7,"line_end":7,"column_start":37,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{Duration, SystemTime, UNIX_EPOCH};","highlight_start":37,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UNIX_EPOCH`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/system.rs:7:39\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{Duration, SystemTime, UNIX_EPOCH};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `MonitorError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/logs.rs","byte_start":111,"byte_end":123,"line_start":5,"line_end":5,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/logs.rs","byte_start":111,"byte_end":125,"line_start":5,"line_end":5,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/logs.rs","byte_start":110,"byte_end":111,"line_start":5,"line_end":5,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/system-monitor/src/logs.rs","byte_start":138,"byte_end":139,"line_start":5,"line_end":5,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::{MonitorError, MonitorResult};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `MonitorError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/logs.rs:5:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{MonitorError, MonitorResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `last_update`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/system-monitor/src/cpu.rs","byte_start":2791,"byte_end":2802,"line_start":114,"line_end":114,"column_start":52,"column_end":63,"is_primary":true,"text":[{"text":"        let usage = if let (Some(prev_stats), Some(last_update)) = (&self.previous_stats, self.last_update) {","highlight_start":52,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/system-monitor/src/cpu.rs","byte_start":2791,"byte_end":2802,"line_start":114,"line_end":114,"column_start":52,"column_end":63,"is_primary":true,"text":[{"text":"        let usage = if let (Some(prev_stats), Some(last_update)) = (&self.previous_stats, self.last_update) {","highlight_start":52,"highlight_end":63}],"label":null,"suggested_replacement":"_last_update","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `last_update`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/system-monitor/src/cpu.rs:114:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mme(prev_stats), Some(last_update)) = (&self.previous_stats, self.l\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_last_update`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"10 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 10 warnings emitted\u001b[0m\n\n"}
