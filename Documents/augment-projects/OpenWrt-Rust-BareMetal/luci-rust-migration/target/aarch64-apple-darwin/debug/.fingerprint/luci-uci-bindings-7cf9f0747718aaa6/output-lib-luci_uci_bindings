{"$message_type":"diagnostic","message":"unused imports: `CStr` and `CString`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":315,"byte_end":319,"line_start":8,"line_end":8,"column_start":16,"column_end":20,"is_primary":true,"text":[{"text":"use std::ffi::{CStr, CString};","highlight_start":16,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":321,"byte_end":328,"line_start":8,"line_end":8,"column_start":22,"column_end":29,"is_primary":true,"text":[{"text":"use std::ffi::{CStr, CString};","highlight_start":22,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":300,"byte_end":331,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::ffi::{CStr, CString};","highlight_start":1,"highlight_end":31},{"text":"use std::os::raw::{c_char, c_int, c_void};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CStr` and `CString`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/uci-bindings/src/lib.rs:8:16\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::ffi::{CStr, CString};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `c_char` and `c_int`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":350,"byte_end":356,"line_start":9,"line_end":9,"column_start":20,"column_end":26,"is_primary":true,"text":[{"text":"use std::os::raw::{c_char, c_int, c_void};","highlight_start":20,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":358,"byte_end":363,"line_start":9,"line_end":9,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"use std::os::raw::{c_char, c_int, c_void};","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":350,"byte_end":365,"line_start":9,"line_end":9,"column_start":20,"column_end":35,"is_primary":true,"text":[{"text":"use std::os::raw::{c_char, c_int, c_void};","highlight_start":20,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":349,"byte_end":350,"line_start":9,"line_end":9,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"use std::os::raw::{c_char, c_int, c_void};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":371,"byte_end":372,"line_start":9,"line_end":9,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"use std::os::raw::{c_char, c_int, c_void};","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `c_char` and `c_int`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/uci-bindings/src/lib.rs:9:20\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::os::raw::{c_char, c_int, c_void};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `luci_shared_types::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":414,"byte_end":434,"line_start":12,"line_end":12,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"use luci_shared_types::*;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":410,"byte_end":436,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use luci_shared_types::*;","highlight_start":1,"highlight_end":26},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `luci_shared_types::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/uci-bindings/src/lib.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse luci_shared_types::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UciError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/uci-bindings/src/config.rs","byte_start":177,"byte_end":185,"line_start":6,"line_end":6,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"use crate::{UciContext, UciError, UciResult, UciValue};","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/uci-bindings/src/config.rs","byte_start":175,"byte_end":185,"line_start":6,"line_end":6,"column_start":23,"column_end":33,"is_primary":true,"text":[{"text":"use crate::{UciContext, UciError, UciResult, UciValue};","highlight_start":23,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UciError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/uci-bindings/src/config.rs:6:25\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{UciContext, UciError, UciResult, UciValue};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/uci-bindings/src/config.rs","byte_start":250,"byte_end":275,"line_start":8,"line_end":8,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/uci-bindings/src/config.rs","byte_start":246,"byte_end":277,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/uci-bindings/src/config.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UciError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/uci-bindings/src/context.rs","byte_start":163,"byte_end":171,"line_start":6,"line_end":6,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"use crate::{UciError, UciResult};","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/uci-bindings/src/context.rs","byte_start":163,"byte_end":173,"line_start":6,"line_end":6,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"use crate::{UciError, UciResult};","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/uci-bindings/src/context.rs","byte_start":162,"byte_end":163,"line_start":6,"line_end":6,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use crate::{UciError, UciResult};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/uci-bindings/src/context.rs","byte_start":182,"byte_end":183,"line_start":6,"line_end":6,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use crate::{UciError, UciResult};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UciError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/uci-bindings/src/context.rs:6:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{UciError, UciResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `rand`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"crates/uci-bindings/src/section.rs","byte_start":1808,"byte_end":1812,"line_start":60,"line_end":60,"column_start":69,"column_end":73,"is_primary":true,"text":[{"text":"        let name = self.name.unwrap_or_else(|| format!(\"cfg{:06x}\", rand::random::<u32>()));","highlight_start":69,"highlight_end":73}],"label":"use of unresolved module or unlinked crate `rand`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `rand`, use `cargo add rand` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `rand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/uci-bindings/src/section.rs:60:69\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m6x}\", rand::random::<u32>()));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `rand`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `rand`, use `cargo add rand` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `rand`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"crates/uci-bindings/src/lib.rs","byte_start":6099,"byte_end":6103,"line_start":208,"line_end":208,"column_start":65,"column_end":69,"is_primary":true,"text":[{"text":"        let section_name = name.unwrap_or(&format!(\"cfg{:06x}\", rand::random::<u32>())).to_string();","highlight_start":65,"highlight_end":69}],"label":"use of unresolved module or unlinked crate `rand`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `rand`, use `cargo add rand` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `rand`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/uci-bindings/src/lib.rs:208:65\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m208\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m6x}\", rand::random::<u32>())).to_string();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `rand`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `rand`, use `cargo add rand` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors; 6 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 2 previous errors; 6 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0433`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0433`.\u001b[0m\n"}
