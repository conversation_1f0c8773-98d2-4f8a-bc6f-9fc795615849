{"rustc": 12610991425282158916, "features": "[\"async\", \"default\", \"futures\", \"tokio\"]", "declared_features": "[\"async\", \"default\", \"embedded\", \"futures\", \"heapless\", \"tokio\"]", "target": 9077776130730617682, "profile": 15057526963834790232, "path": 15918577116975542860, "deps": [[2706460456408817945, "futures", false, 16405549148115417185], [4684437522915235464, "libc", false, 12457626144142322875], [5986029879202738730, "log", false, 1770106447332161632], [6462525672642925639, "nix", false, 2040260779375282573], [8008191657135824715, "thiserror", false, 2760009951614222754], [8319709847752024821, "uuid", false, 12860498545333432582], [9538054652646069845, "tokio", false, 2828153908726863307], [9689903380558560274, "serde", false, 10546623119610134092], [9897246384292347999, "chrono", false, 3747316119217821086], [12984406303388944089, "luci_shared_types", false, 3819879196415779974], [13208667028893622512, "rand", false, 10857151090897136474], [13625485746686963219, "anyhow", false, 10653682492695522482], [14940550291033027160, "luci_utilities", false, 8855748283049653425], [15367738274754116744, "serde_json", false, 17312044369037065190]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/luci-uci-bindings-baf1026904b1eb48/dep-test-lib-luci_uci_bindings", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}