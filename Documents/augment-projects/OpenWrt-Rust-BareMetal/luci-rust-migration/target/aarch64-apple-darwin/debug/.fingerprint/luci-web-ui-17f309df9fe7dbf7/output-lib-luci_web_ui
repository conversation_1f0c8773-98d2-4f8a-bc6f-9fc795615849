{"$message_type":"diagnostic","message":"unresolved import `package_manager`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":187,"byte_end":202,"line_start":7,"line_end":7,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"use package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus};","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`use` statements changed in Rust 2018; read more at <https://doc.rust-lang.org/edition-guide/rust-2018/module-system/path-clarity.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"a similar path exists","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":187,"byte_end":202,"line_start":7,"line_end":7,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"use package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus};","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"crate::package_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `package_manager`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/package_manager.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse package_manager::{PackageManager, PackageInfo, PackageSearchCriteri\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: a similar path exists: `crate::package_manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `use` statements changed in Rust 2018; read more at <https://doc.rust-lang.org/edition-guide/rust-2018/module-system/path-clarity.html>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `package_manager`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":5257,"byte_end":5272,"line_start":110,"line_end":110,"column_start":23,"column_end":38,"is_primary":true,"text":[{"text":"            priority: package_manager::PackagePriority::Optional,","highlight_start":23,"highlight_end":38}],"label":"use of unresolved module or unlinked crate `package_manager`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `package_manager`, use `cargo add package_manager` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `package_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/package_manager.rs:110:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            priority: package_manager::PackagePriority::Optional,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `package_manager`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `package_manager`, use `cargo add package_manager` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `luci_shared_types::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":264,"byte_end":284,"line_start":9,"line_end":9,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"use luci_shared_types::*;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":260,"byte_end":286,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use luci_shared_types::*;","highlight_start":1,"highlight_end":26},{"text":"use serde::{Serialize, Deserialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `luci_shared_types::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse luci_shared_types::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":190,"byte_end":199,"line_start":6,"line_end":6,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":186,"byte_end":201,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":1,"highlight_end":15},{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":205,"byte_end":219,"line_start":7,"line_end":7,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":201,"byte_end":221,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use leptos_router::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_router::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":225,"byte_end":241,"line_start":8,"line_end":8,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use leptos_router::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":221,"byte_end":243,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_router::*;","highlight_start":1,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_router::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_router::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UserInfo`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/header.rs","byte_start":246,"byte_end":254,"line_start":8,"line_end":8,"column_start":37,"column_end":45,"is_primary":true,"text":[{"text":"use crate::auth::{use_auth_context, UserInfo, RequirePermission, AdminOnly};","highlight_start":37,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/header.rs","byte_start":244,"byte_end":254,"line_start":8,"line_end":8,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"use crate::auth::{use_auth_context, UserInfo, RequirePermission, AdminOnly};","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UserInfo`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/header.rs:8:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::auth::{use_auth_context, UserInfo, RequirePermission, AdminO\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AppState` and `VersionInfo`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/footer.rs","byte_start":187,"byte_end":195,"line_start":7,"line_end":7,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"use crate::{AppState, VersionInfo};","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/footer.rs","byte_start":197,"byte_end":208,"line_start":7,"line_end":7,"column_start":23,"column_end":34,"is_primary":true,"text":[{"text":"use crate::{AppState, VersionInfo};","highlight_start":23,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/footer.rs","byte_start":175,"byte_end":211,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::{AppState, VersionInfo};","highlight_start":1,"highlight_end":36},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `AppState` and `VersionInfo`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/footer.rs:7:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{AppState, VersionInfo};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/charts.rs","byte_start":55,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/charts.rs","byte_start":51,"byte_end":66,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":1,"highlight_end":15},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/charts.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize` and `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":158,"byte_end":169,"line_start":6,"line_end":6,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":171,"byte_end":180,"line_start":6,"line_end":6,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":146,"byte_end":183,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37},{"text":"use package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Deserialize` and `Serialize`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/package_manager.rs:6:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLink` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLink` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(ambiguous_glob_reexports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLink` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLink` in the value namespace is also re-exported here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(ambiguous_glob_reexports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `__NavLink` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `__NavLink` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__NavLink` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__NavLink` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Repeated_field_href` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Repeated_field_href` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Repeated_field_href` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Repeated_field_href` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Repeated_field_children` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Repeated_field_children` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Repeated_field_children` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Repeated_field_children` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Missing_required_field_href` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Missing_required_field_href` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Missing_required_field_href` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Missing_required_field_href` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Missing_required_field_children` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Missing_required_field_children` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Missing_required_field_children` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Missing_required_field_children` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonVariant` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonVariant` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonVariant` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonVariant` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonSize` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonSize` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonSize` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonSize` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `Button` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `Button` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `Button` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `Button` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `__Button` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `__Button` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__Button` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__Button` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_children` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_children` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_children` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_children` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_loading` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_loading` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_loading` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_loading` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_button_type` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_button_type` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_button_type` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_button_type` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Missing_required_field_children` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Missing_required_field_children` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Missing_required_field_children` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Missing_required_field_children` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButton` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButton` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButton` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButton` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `__IconButton` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `__IconButton` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__IconButton` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__IconButton` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_icon` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_icon` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_icon` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_icon` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_tooltip` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_tooltip` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_tooltip` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_tooltip` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Missing_required_field_icon` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Missing_required_field_icon` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Missing_required_field_icon` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Missing_required_field_icon` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBar` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBar` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBar` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBar` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `__ProgressBar` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `__ProgressBar` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__ProgressBar` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__ProgressBar` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Repeated_field_value` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Repeated_field_value` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Repeated_field_value` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Repeated_field_value` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Repeated_field_max` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Repeated_field_max` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Repeated_field_max` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Repeated_field_max` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Repeated_field_show_percentage` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Repeated_field_show_percentage` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Repeated_field_show_percentage` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Repeated_field_show_percentage` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Missing_required_field_value` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Missing_required_field_value` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Missing_required_field_value` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Missing_required_field_value` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `LoadingSpinner` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `LoadingSpinner` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `LoadingSpinner` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `LoadingSpinner` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `__LoadingSpinner` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `__LoadingSpinner` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__LoadingSpinner` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__LoadingSpinner` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `LoadingSpinnerPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `LoadingSpinnerPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `LoadingSpinnerPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `LoadingSpinnerPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `LoadingSpinnerPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `LoadingSpinnerPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `LoadingSpinnerPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `LoadingSpinnerPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `LoadingSpinnerProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `LoadingSpinnerProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `LoadingSpinnerProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `LoadingSpinnerProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `charts::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":648,"byte_end":657,"line_start":32,"line_end":32,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use charts::*;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":640,"byte_end":659,"line_start":32,"line_end":33,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use charts::*;","highlight_start":1,"highlight_end":19},{"text":"pub use buttons::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `charts::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:32:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use charts::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":163,"byte_end":172,"line_start":6,"line_end":6,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":159,"byte_end":174,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":1,"highlight_end":15},{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/mod.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":178,"byte_end":192,"line_start":7,"line_end":7,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":174,"byte_end":194,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use leptos_router::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/mod.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_router::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":198,"byte_end":214,"line_start":8,"line_end":8,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use leptos_router::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":194,"byte_end":216,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_router::*;","highlight_start":1,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_router::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/mod.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_router::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `AuthContext`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/login.rs","byte_start":55,"byte_end":66,"line_start":3,"line_end":3,"column_start":19,"column_end":30,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":19,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/login.rs","byte_start":55,"byte_end":68,"line_start":3,"line_end":3,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/pages/login.rs","byte_start":54,"byte_end":55,"line_start":3,"line_end":3,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/pages/login.rs","byte_start":84,"byte_end":85,"line_start":3,"line_end":3,"column_start":48,"column_end":49,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":48,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `AuthContext`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/login.rs:3:19\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::auth::{AuthContext, use_auth_context};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `AuthContext`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/logout.rs","byte_start":55,"byte_end":66,"line_start":3,"line_end":3,"column_start":19,"column_end":30,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":19,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/logout.rs","byte_start":55,"byte_end":68,"line_start":3,"line_end":3,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/pages/logout.rs","byte_start":54,"byte_end":55,"line_start":3,"line_end":3,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/pages/logout.rs","byte_start":84,"byte_end":85,"line_start":3,"line_end":3,"column_start":48,"column_end":49,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":48,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `AuthContext`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/logout.rs:3:19\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::auth::{AuthContext, use_auth_context};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `NavigateOptions` and `use_navigate`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/auth/mod.rs","byte_start":166,"byte_end":178,"line_start":6,"line_end":6,"column_start":21,"column_end":33,"is_primary":true,"text":[{"text":"use leptos_router::{use_navigate, NavigateOptions};","highlight_start":21,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/auth/mod.rs","byte_start":180,"byte_end":195,"line_start":6,"line_end":6,"column_start":35,"column_end":50,"is_primary":true,"text":[{"text":"use leptos_router::{use_navigate, NavigateOptions};","highlight_start":35,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/auth/mod.rs","byte_start":146,"byte_end":198,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_router::{use_navigate, NavigateOptions};","highlight_start":1,"highlight_end":52},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `NavigateOptions` and `use_navigate`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/auth/mod.rs:6:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_router::{use_navigate, NavigateOptions};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `embedded`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":4408,"byte_end":4428,"line_start":130,"line_end":130,"column_start":11,"column_end":31,"is_primary":true,"text":[{"text":"    #[cfg(feature = \"embedded\")]","highlight_start":11,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected values for `feature` are: `default`, `hydrate`, and `ssr`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `embedded` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unexpected_cfgs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition value: `embedded`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:130:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[cfg(feature = \"embedded\")]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected values for `feature` are: `default`, `hydrate`, and `ssr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding `embedded` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unexpected_cfgs)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `r#async`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":4489,"byte_end":4506,"line_start":133,"line_end":133,"column_start":11,"column_end":28,"is_primary":true,"text":[{"text":"    #[cfg(feature = \"async\")]","highlight_start":11,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected values for `feature` are: `default`, `hydrate`, and `ssr`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `r#async` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition value: `r#async`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:133:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[cfg(feature = \"async\")]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected values for `feature` are: `default`, `hydrate`, and `ssr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding `r#async` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `tls`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":4564,"byte_end":4579,"line_start":136,"line_end":136,"column_start":11,"column_end":26,"is_primary":true,"text":[{"text":"    #[cfg(feature = \"tls\")]","highlight_start":11,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected values for `feature` are: `default`, `hydrate`, and `ssr`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `tls` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition value: `tls`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:136:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[cfg(feature = \"tls\")]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected values for `feature` are: `default`, `hydrate`, and `ssr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding `tls` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `super::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":6414,"byte_end":6422,"line_start":209,"line_end":209,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":6410,"byte_end":6423,"line_start":209,"line_end":209,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `super::*`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:209:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m209\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use super::*;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":6432,"byte_end":6441,"line_start":210,"line_end":210,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    use leptos::*;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":6428,"byte_end":6442,"line_start":210,"line_end":210,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    use leptos::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos::*`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:210:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use leptos::*;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `show_mobile_menu`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/header.rs","byte_start":10129,"byte_end":10145,"line_start":227,"line_end":227,"column_start":10,"column_end":26,"is_primary":true,"text":[{"text":"    let (show_mobile_menu, set_show_mobile_menu) = create_signal(false);","highlight_start":10,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/header.rs","byte_start":10129,"byte_end":10145,"line_start":227,"line_end":227,"column_start":10,"column_end":26,"is_primary":true,"text":[{"text":"    let (show_mobile_menu, set_show_mobile_menu) = create_signal(false);","highlight_start":10,"highlight_end":26}],"label":null,"suggested_replacement":"_show_mobile_menu","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `show_mobile_menu`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/header.rs:227:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let (show_mobile_menu, set_show_mobile_menu) = create_signal(false);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_show_mobile_menu`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors; 71 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 2 previous errors; 71 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0432, E0433.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0432, E0433.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0432`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0432`.\u001b[0m\n"}
