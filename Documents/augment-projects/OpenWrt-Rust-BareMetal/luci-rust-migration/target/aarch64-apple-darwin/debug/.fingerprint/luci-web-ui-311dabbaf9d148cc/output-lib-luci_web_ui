{"$message_type":"diagnostic","message":"unused import: `luci_shared_types::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":264,"byte_end":284,"line_start":9,"line_end":9,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"use luci_shared_types::*;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":260,"byte_end":286,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use luci_shared_types::*;","highlight_start":1,"highlight_end":26},{"text":"use serde::{Serialize, Deserialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `luci_shared_types::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse luci_shared_types::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":190,"byte_end":199,"line_start":6,"line_end":6,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":186,"byte_end":201,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":1,"highlight_end":15},{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":205,"byte_end":219,"line_start":7,"line_end":7,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":201,"byte_end":221,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use leptos_router::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_router::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":225,"byte_end":241,"line_start":8,"line_end":8,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use leptos_router::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":221,"byte_end":243,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_router::*;","highlight_start":1,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_router::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_router::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UserInfo`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/header.rs","byte_start":246,"byte_end":254,"line_start":8,"line_end":8,"column_start":37,"column_end":45,"is_primary":true,"text":[{"text":"use crate::auth::{use_auth_context, UserInfo, RequirePermission, AdminOnly};","highlight_start":37,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/header.rs","byte_start":244,"byte_end":254,"line_start":8,"line_end":8,"column_start":35,"column_end":45,"is_primary":true,"text":[{"text":"use crate::auth::{use_auth_context, UserInfo, RequirePermission, AdminOnly};","highlight_start":35,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UserInfo`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/header.rs:8:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::auth::{use_auth_context, UserInfo, RequirePermission, AdminO\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AppState` and `VersionInfo`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/footer.rs","byte_start":187,"byte_end":195,"line_start":7,"line_end":7,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"use crate::{AppState, VersionInfo};","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/footer.rs","byte_start":197,"byte_end":208,"line_start":7,"line_end":7,"column_start":23,"column_end":34,"is_primary":true,"text":[{"text":"use crate::{AppState, VersionInfo};","highlight_start":23,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/footer.rs","byte_start":175,"byte_end":211,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::{AppState, VersionInfo};","highlight_start":1,"highlight_end":36},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `AppState` and `VersionInfo`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/footer.rs:7:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{AppState, VersionInfo};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/charts.rs","byte_start":55,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/charts.rs","byte_start":51,"byte_end":66,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":1,"highlight_end":15},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/charts.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize` and `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":158,"byte_end":169,"line_start":6,"line_end":6,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":171,"byte_end":180,"line_start":6,"line_end":6,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":146,"byte_end":183,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37},{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Deserialize` and `Serialize`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/package_manager.rs:6:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `PackageManager` and `PackageSearchCriteria`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":210,"byte_end":224,"line_start":7,"line_end":7,"column_start":28,"column_end":42,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus};","highlight_start":28,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":239,"byte_end":260,"line_start":7,"line_end":7,"column_start":57,"column_end":78,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus};","highlight_start":57,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":210,"byte_end":226,"line_start":7,"line_end":7,"column_start":28,"column_end":44,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus};","highlight_start":28,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/components/package_manager.rs","byte_start":237,"byte_end":260,"line_start":7,"line_end":7,"column_start":55,"column_end":78,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus};","highlight_start":55,"highlight_end":78}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `PackageManager` and `PackageSearchCriteria`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/package_manager.rs:7:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0manager::{PackageManager, PackageInfo, PackageSearchCriteria, Package\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLink` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLink` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(ambiguous_glob_reexports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLink` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLink` in the value namespace is also re-exported here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(ambiguous_glob_reexports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `__NavLink` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `__NavLink` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__NavLink` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__NavLink` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Repeated_field_href` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Repeated_field_href` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Repeated_field_href` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Repeated_field_href` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Repeated_field_children` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Repeated_field_children` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Repeated_field_children` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Repeated_field_children` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Missing_required_field_href` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Missing_required_field_href` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Missing_required_field_href` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Missing_required_field_href` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkPropsBuilder_Error_Missing_required_field_children` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkPropsBuilder_Error_Missing_required_field_children` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkPropsBuilder_Error_Missing_required_field_children` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkPropsBuilder_Error_Missing_required_field_children` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":512,"byte_end":521,"line_start":25,"line_end":25,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use header::*;","highlight_start":9,"highlight_end":18}],"label":"the name `NavLinkProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `NavLinkProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:25:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use header::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `NavLinkProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use footer::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `NavLinkProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonVariant` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonVariant` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonVariant` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonVariant` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonSize` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonSize` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonSize` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonSize` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `Button` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `Button` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `Button` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `Button` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `__Button` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `__Button` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__Button` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__Button` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_children` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_children` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_children` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_children` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_loading` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_loading` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_loading` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_loading` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_button_type` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_button_type` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_button_type` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_button_type` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonPropsBuilder_Error_Missing_required_field_children` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonPropsBuilder_Error_Missing_required_field_children` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonPropsBuilder_Error_Missing_required_field_children` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonPropsBuilder_Error_Missing_required_field_children` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `ButtonProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ButtonProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ButtonProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ButtonProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButton` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButton` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButton` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButton` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `__IconButton` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `__IconButton` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__IconButton` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__IconButton` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_icon` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_icon` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_icon` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_icon` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_disabled` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_on_click` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_tooltip` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_tooltip` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_tooltip` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_tooltip` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Repeated_field_class` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonPropsBuilder_Error_Missing_required_field_icon` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonPropsBuilder_Error_Missing_required_field_icon` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonPropsBuilder_Error_Missing_required_field_icon` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonPropsBuilder_Error_Missing_required_field_icon` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":550,"byte_end":563,"line_start":27,"line_end":27,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use navigation::*;","highlight_start":9,"highlight_end":22}],"label":"the name `IconButtonProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":667,"byte_end":677,"line_start":33,"line_end":33,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use buttons::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `IconButtonProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:27:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use navigation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `IconButtonProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use buttons::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `IconButtonProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBar` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBar` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBar` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBar` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `__ProgressBar` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `__ProgressBar` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__ProgressBar` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__ProgressBar` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Repeated_field_value` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Repeated_field_value` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Repeated_field_value` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Repeated_field_value` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Repeated_field_max` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Repeated_field_max` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Repeated_field_max` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Repeated_field_max` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Repeated_field_variant` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Repeated_field_variant` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Repeated_field_show_percentage` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Repeated_field_show_percentage` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Repeated_field_show_percentage` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Repeated_field_show_percentage` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarPropsBuilder_Error_Missing_required_field_value` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarPropsBuilder_Error_Missing_required_field_value` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarPropsBuilder_Error_Missing_required_field_value` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarPropsBuilder_Error_Missing_required_field_value` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `ProgressBarProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `ProgressBarProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `ProgressBarProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `ProgressBarProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `LoadingSpinner` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `LoadingSpinner` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `LoadingSpinner` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `LoadingSpinner` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `__LoadingSpinner` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `__LoadingSpinner` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `__LoadingSpinner` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `__LoadingSpinner` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `LoadingSpinnerPropsBuilder` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `LoadingSpinnerPropsBuilder` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `LoadingSpinnerPropsBuilder` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `LoadingSpinnerPropsBuilder` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `LoadingSpinnerPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `LoadingSpinnerPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `LoadingSpinnerPropsBuilder_Error_Repeated_field_size` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `LoadingSpinnerPropsBuilder_Error_Repeated_field_size` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":629,"byte_end":638,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `LoadingSpinnerProps` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":724,"byte_end":734,"line_start":36,"line_end":36,"column_start":9,"column_end":19,"is_primary":false,"text":[{"text":"pub use loading::*;","highlight_start":9,"highlight_end":19}],"label":"but the name `LoadingSpinnerProps` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `LoadingSpinnerProps` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use loading::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `LoadingSpinnerProps` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `charts::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":648,"byte_end":657,"line_start":32,"line_end":32,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use charts::*;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/mod.rs","byte_start":640,"byte_end":659,"line_start":32,"line_end":33,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use charts::*;","highlight_start":1,"highlight_end":19},{"text":"pub use buttons::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `charts::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/mod.rs:32:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use charts::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":163,"byte_end":172,"line_start":6,"line_end":6,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":159,"byte_end":174,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos::*;","highlight_start":1,"highlight_end":15},{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/mod.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":178,"byte_end":192,"line_start":7,"line_end":7,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":174,"byte_end":194,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use leptos_router::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/mod.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_router::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":198,"byte_end":214,"line_start":8,"line_end":8,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use leptos_router::*;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":194,"byte_end":216,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_router::*;","highlight_start":1,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_router::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/mod.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_router::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `AuthContext`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/login.rs","byte_start":55,"byte_end":66,"line_start":3,"line_end":3,"column_start":19,"column_end":30,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":19,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/login.rs","byte_start":55,"byte_end":68,"line_start":3,"line_end":3,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/pages/login.rs","byte_start":54,"byte_end":55,"line_start":3,"line_end":3,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/pages/login.rs","byte_start":84,"byte_end":85,"line_start":3,"line_end":3,"column_start":48,"column_end":49,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":48,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `AuthContext`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/login.rs:3:19\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::auth::{AuthContext, use_auth_context};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":19,"byte_end":33,"line_start":2,"line_end":2,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":15,"byte_end":35,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::time::Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":76,"byte_end":95,"line_start":4,"line_end":4,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":72,"byte_end":97,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":25},{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, PackageStats};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::time::Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::Duration;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `PackageError`, `PackageManager`, `PackagePriority`, `PackageSearchCriteria`, and `PackageStatus as PkgStatus`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":124,"byte_end":138,"line_start":5,"line_end":5,"column_start":28,"column_end":42,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, PackageStats};","highlight_start":28,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":153,"byte_end":174,"line_start":5,"line_end":5,"column_start":57,"column_end":78,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, PackageStats};","highlight_start":57,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":176,"byte_end":202,"line_start":5,"line_end":5,"column_start":80,"column_end":106,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, PackageStats};","highlight_start":80,"highlight_end":106}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":204,"byte_end":216,"line_start":5,"line_end":5,"column_start":108,"column_end":120,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, PackageStats};","highlight_start":108,"highlight_end":120}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":218,"byte_end":233,"line_start":5,"line_end":5,"column_start":122,"column_end":137,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, PackageStats};","highlight_start":122,"highlight_end":137}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":124,"byte_end":140,"line_start":5,"line_end":5,"column_start":28,"column_end":44,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, PackageStats};","highlight_start":28,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":151,"byte_end":233,"line_start":5,"line_end":5,"column_start":55,"column_end":137,"is_primary":true,"text":[{"text":"use luci_package_manager::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, PackageStats};","highlight_start":55,"highlight_end":137}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `PackageError`, `PackageManager`, `PackagePriority`, `PackageSearchCriteria`, and `PackageStatus as PkgStatus`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:5:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m::{PackageManager, PackageInfo, PackageSearchCriteria, PackageStatus as PkgStatus, PackageError, PackagePriority, P\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/status.rs","byte_start":19,"byte_end":33,"line_start":2,"line_end":2,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/status.rs","byte_start":15,"byte_end":35,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/status.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/processes.rs","byte_start":19,"byte_end":33,"line_start":2,"line_end":2,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/processes.rs","byte_start":15,"byte_end":35,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/processes.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/network_monitor.rs","byte_start":19,"byte_end":33,"line_start":2,"line_end":2,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/network_monitor.rs","byte_start":15,"byte_end":35,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/network_monitor.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/logs.rs","byte_start":19,"byte_end":33,"line_start":2,"line_end":2,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/logs.rs","byte_start":15,"byte_end":35,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/logs.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos_meta::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/device_info.rs","byte_start":19,"byte_end":33,"line_start":2,"line_end":2,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/device_info.rs","byte_start":15,"byte_end":35,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_meta::*;","highlight_start":1,"highlight_end":20},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos_meta::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/device_info.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_meta::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `AuthContext`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/logout.rs","byte_start":55,"byte_end":66,"line_start":3,"line_end":3,"column_start":19,"column_end":30,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":19,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/logout.rs","byte_start":55,"byte_end":68,"line_start":3,"line_end":3,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/pages/logout.rs","byte_start":54,"byte_end":55,"line_start":3,"line_end":3,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/web-ui/src/pages/logout.rs","byte_start":84,"byte_end":85,"line_start":3,"line_end":3,"column_start":48,"column_end":49,"is_primary":true,"text":[{"text":"use crate::auth::{AuthContext, use_auth_context};","highlight_start":48,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `AuthContext`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/logout.rs:3:19\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::auth::{AuthContext, use_auth_context};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":726,"byte_end":735,"line_start":37,"line_end":37,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use status::*;","highlight_start":9,"highlight_end":18}],"label":"the name `StorageInfo` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/pages/mod.rs","byte_start":812,"byte_end":826,"line_start":41,"line_end":41,"column_start":9,"column_end":23,"is_primary":false,"text":[{"text":"pub use device_info::*;","highlight_start":9,"highlight_end":23}],"label":"but the name `StorageInfo` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/mod.rs:37:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use status::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `StorageInfo` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use device_info::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `StorageInfo` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `NavigateOptions` and `use_navigate`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/auth/mod.rs","byte_start":166,"byte_end":178,"line_start":6,"line_end":6,"column_start":21,"column_end":33,"is_primary":true,"text":[{"text":"use leptos_router::{use_navigate, NavigateOptions};","highlight_start":21,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/auth/mod.rs","byte_start":180,"byte_end":195,"line_start":6,"line_end":6,"column_start":35,"column_end":50,"is_primary":true,"text":[{"text":"use leptos_router::{use_navigate, NavigateOptions};","highlight_start":35,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/auth/mod.rs","byte_start":146,"byte_end":198,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use leptos_router::{use_navigate, NavigateOptions};","highlight_start":1,"highlight_end":52},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `NavigateOptions` and `use_navigate`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/auth/mod.rs:6:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse leptos_router::{use_navigate, NavigateOptions};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `embedded`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":4482,"byte_end":4502,"line_start":131,"line_end":131,"column_start":11,"column_end":31,"is_primary":true,"text":[{"text":"    #[cfg(feature = \"embedded\")]","highlight_start":11,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected values for `feature` are: `default`, `hydrate`, and `ssr`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `embedded` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unexpected_cfgs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition value: `embedded`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:131:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[cfg(feature = \"embedded\")]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected values for `feature` are: `default`, `hydrate`, and `ssr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding `embedded` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unexpected_cfgs)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `r#async`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":4563,"byte_end":4580,"line_start":134,"line_end":134,"column_start":11,"column_end":28,"is_primary":true,"text":[{"text":"    #[cfg(feature = \"async\")]","highlight_start":11,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected values for `feature` are: `default`, `hydrate`, and `ssr`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `r#async` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition value: `r#async`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:134:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[cfg(feature = \"async\")]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected values for `feature` are: `default`, `hydrate`, and `ssr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding `r#async` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `tls`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":4638,"byte_end":4653,"line_start":137,"line_end":137,"column_start":11,"column_end":26,"is_primary":true,"text":[{"text":"    #[cfg(feature = \"tls\")]","highlight_start":11,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected values for `feature` are: `default`, `hydrate`, and `ssr`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `tls` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition value: `tls`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:137:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[cfg(feature = \"tls\")]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected values for `feature` are: `default`, `hydrate`, and `ssr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding `tls` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `super::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":6488,"byte_end":6496,"line_start":210,"line_end":210,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":6484,"byte_end":6497,"line_start":210,"line_end":210,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `super::*`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:210:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use super::*;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `leptos::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":6506,"byte_end":6515,"line_start":211,"line_end":211,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    use leptos::*;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/lib.rs","byte_start":6502,"byte_end":6516,"line_start":211,"line_end":211,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    use leptos::*;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `leptos::*`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/lib.rs:211:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m211\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use leptos::*;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `show_mobile_menu`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/header.rs","byte_start":10507,"byte_end":10523,"line_start":234,"line_end":234,"column_start":10,"column_end":26,"is_primary":true,"text":[{"text":"    let (show_mobile_menu, set_show_mobile_menu) = create_signal(false);","highlight_start":10,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/components/header.rs","byte_start":10507,"byte_end":10523,"line_start":234,"line_end":234,"column_start":10,"column_end":26,"is_primary":true,"text":[{"text":"    let (show_mobile_menu, set_show_mobile_menu) = create_signal(false);","highlight_start":10,"highlight_end":26}],"label":null,"suggested_replacement":"_show_mobile_menu","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `show_mobile_menu`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/header.rs:234:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m234\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let (show_mobile_menu, set_show_mobile_menu) = create_signal(false);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_show_mobile_menu`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `show_confirmation`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77326,"byte_end":77343,"line_start":1489,"line_end":1489,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    show_confirmation: ReadSignal<bool>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77326,"byte_end":77343,"line_start":1489,"line_end":1489,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    show_confirmation: ReadSignal<bool>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":"_show_confirmation","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `show_confirmation`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1489:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1489\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    show_confirmation: ReadSignal<bool>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_show_confirmation`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `pending_operation`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77413,"byte_end":77430,"line_start":1491,"line_end":1491,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    pending_operation: ReadSignal<Option<(String, String)>>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77413,"byte_end":77430,"line_start":1491,"line_end":1491,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    pending_operation: ReadSignal<Option<(String, String)>>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":"_pending_operation","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `pending_operation`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1491:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1491\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pending_operation: ReadSignal<Option<(String, String)>>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_pending_operation`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `set_operation_loading`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77581,"byte_end":77602,"line_start":1494,"line_end":1494,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    set_operation_loading: WriteSignal<bool>,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77581,"byte_end":77602,"line_start":1494,"line_end":1494,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    set_operation_loading: WriteSignal<bool>,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":"_set_operation_loading","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `set_operation_loading`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1494:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1494\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    set_operation_loading: WriteSignal<bool>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_set_operation_loading`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `set_operation_error`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77676,"byte_end":77695,"line_start":1496,"line_end":1496,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    set_operation_error: WriteSignal<Option<String>>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77676,"byte_end":77695,"line_start":1496,"line_end":1496,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    set_operation_error: WriteSignal<Option<String>>,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":"_set_operation_error","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `set_operation_error`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1496:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1496\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    set_operation_error: WriteSignal<Option<String>>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_set_operation_error`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `set_operation_success`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77781,"byte_end":77802,"line_start":1498,"line_end":1498,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    set_operation_success: WriteSignal<Option<String>>,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":77781,"byte_end":77802,"line_start":1498,"line_end":1498,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    set_operation_success: WriteSignal<Option<String>>,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":"_set_operation_success","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `set_operation_success`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1498:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1498\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    set_operation_success: WriteSignal<Option<String>>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_set_operation_success`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `pkg_name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":90823,"byte_end":90831,"line_start":1702,"line_end":1702,"column_start":41,"column_end":49,"is_primary":true,"text":[{"text":"                                    let pkg_name = pkg.name.clone();","highlight_start":41,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":90823,"byte_end":90831,"line_start":1702,"line_end":1702,"column_start":41,"column_end":49,"is_primary":true,"text":[{"text":"                                    let pkg_name = pkg.name.clone();","highlight_start":41,"highlight_end":49}],"label":null,"suggested_replacement":"_pkg_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `pkg_name`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1702:41\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1702\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                   let pkg_name = pkg.name.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_pkg_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `set_updating_packages`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100099,"byte_end":100120,"line_start":1993,"line_end":1993,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    set_updating_packages: WriteSignal<bool>,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100099,"byte_end":100120,"line_start":1993,"line_end":1993,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    set_updating_packages: WriteSignal<bool>,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":"_set_updating_packages","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `set_updating_packages`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1993:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1993\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    set_updating_packages: WriteSignal<bool>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_set_updating_packages`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `show_confirmation`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100145,"byte_end":100162,"line_start":1994,"line_end":1994,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    show_confirmation: ReadSignal<bool>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100145,"byte_end":100162,"line_start":1994,"line_end":1994,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    show_confirmation: ReadSignal<bool>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":"_show_confirmation","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `show_confirmation`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1994:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1994\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    show_confirmation: ReadSignal<bool>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_show_confirmation`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `pending_operation`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100232,"byte_end":100249,"line_start":1996,"line_end":1996,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    pending_operation: ReadSignal<Option<(String, String)>>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100232,"byte_end":100249,"line_start":1996,"line_end":1996,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    pending_operation: ReadSignal<Option<(String, String)>>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":"_pending_operation","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `pending_operation`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1996:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1996\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pending_operation: ReadSignal<Option<(String, String)>>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_pending_operation`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `global_operation_loading`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100359,"byte_end":100383,"line_start":1998,"line_end":1998,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"    global_operation_loading: ReadSignal<bool>,","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100359,"byte_end":100383,"line_start":1998,"line_end":1998,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"    global_operation_loading: ReadSignal<bool>,","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":"_global_operation_loading","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `global_operation_loading`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1998:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1998\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    global_operation_loading: ReadSignal<bool>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_global_operation_loading`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `set_global_operation_loading`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100407,"byte_end":100435,"line_start":1999,"line_end":1999,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"    set_global_operation_loading: WriteSignal<bool>,","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100407,"byte_end":100435,"line_start":1999,"line_end":1999,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"    set_global_operation_loading: WriteSignal<bool>,","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":"_set_global_operation_loading","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `set_global_operation_loading`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:1999:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1999\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    set_global_operation_loading: WriteSignal<bool>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_set_global_operation_loading`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `set_global_operation_error`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100516,"byte_end":100542,"line_start":2001,"line_end":2001,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    set_global_operation_error: WriteSignal<Option<String>>,","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100516,"byte_end":100542,"line_start":2001,"line_end":2001,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    set_global_operation_error: WriteSignal<Option<String>>,","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":"_set_global_operation_error","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `set_global_operation_error`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:2001:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2001\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    set_global_operation_error: WriteSignal<Option<String>>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_set_global_operation_error`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `set_global_operation_success`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100635,"byte_end":100663,"line_start":2003,"line_end":2003,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"    set_global_operation_success: WriteSignal<Option<String>>,","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":100635,"byte_end":100663,"line_start":2003,"line_end":2003,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"    set_global_operation_success: WriteSignal<Option<String>>,","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":"_set_global_operation_success","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `set_global_operation_success`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:2003:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2003\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    set_global_operation_success: WriteSignal<Option<String>>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_set_global_operation_success`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `changelog_clone`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":119853,"byte_end":119868,"line_start":2322,"line_end":2322,"column_start":45,"column_end":60,"is_primary":true,"text":[{"text":"                                        let changelog_clone = changelog.clone();","highlight_start":45,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/web-ui/src/pages/packages.rs","byte_start":119853,"byte_end":119868,"line_start":2322,"line_end":2322,"column_start":45,"column_end":60,"is_primary":true,"text":[{"text":"                                        let changelog_clone = changelog.clone();","highlight_start":45,"highlight_end":60}],"label":null,"suggested_replacement":"_changelog_clone","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `changelog_clone`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/pages/packages.rs:2322:45\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2322\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                   let changelog_clone = changelog.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_changelog_clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `to_color` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"crates/web-ui/src/components/status.rs","byte_start":362,"byte_end":377,"line_start":18,"line_end":18,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl StatusType {","highlight_start":1,"highlight_end":16}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/web-ui/src/components/status.rs","byte_start":749,"byte_end":757,"line_start":29,"line_end":29,"column_start":8,"column_end":16,"is_primary":true,"text":[{"text":"    fn to_color(&self) -> &'static str {","highlight_start":8,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: method `to_color` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/web-ui/src/components/status.rs:29:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl StatusType {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn to_color(&self) -> &'static str {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"96 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 96 warnings emitted\u001b[0m\n\n"}
