{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 2594889627657062481, "profile": 8276155916380437441, "path": 15148409618523079096, "deps": [[2828590642173593838, "cfg_if", false, 16056165954977868191], [4684437522915235464, "libc", false, 14620522789151788272], [7896293946984509699, "bitflags", false, 12059654358294224691]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/nix-58f0cbbff7af2d25/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}