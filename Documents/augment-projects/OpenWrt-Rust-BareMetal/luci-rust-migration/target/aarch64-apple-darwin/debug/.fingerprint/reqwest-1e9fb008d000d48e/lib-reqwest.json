{"rustc": 12610991425282158916, "features": "[\"__rustls\", \"__tls\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 8276155916380437441, "path": 1779726095666982138, "deps": [[40386456601120721, "percent_encoding", false, 12768663558983198052], [95042085696191081, "ipnet", false, 12797479751793359296], [264090853244900308, "sync_wrapper", false, 7738108708608915583], [784494742817713399, "tower_service", false, 4442681223396543130], [1044435446100926395, "hyper_rustls", false, 10750140772543390685], [1906322745568073236, "pin_project_lite", false, 134321414572171600], [3150220818285335163, "url", false, 17365812633056385040], [3722963349756955755, "once_cell", false, 334531857116249603], [4405182208873388884, "http", false, 4137392743471393626], [5986029879202738730, "log", false, 10834319452123524630], [7414427314941361239, "hyper", false, 9773880116616860877], [7620660491849607393, "futures_core", false, 13580037134113986251], [8915503303801890683, "http_body", false, 12274096530611768825], [9538054652646069845, "tokio", false, 10861959258753612748], [9689903380558560274, "serde", false, 14136723675802054954], [10229185211513642314, "mime", false, 18018448476383414204], [10629569228670356391, "futures_util", false, 9647879112712347318], [11107720164717273507, "system_configuration", false, 12389797365704627274], [11295624341523567602, "rustls", false, 4434311683637829848], [13809605890706463735, "h2", false, 13029364576000768914], [14564311161534545801, "encoding_rs", false, 9192485679871151553], [15367738274754116744, "serde_json", false, 13238213450351272871], [16066129441945555748, "bytes", false, 4565292215235062127], [16311359161338405624, "rustls_pemfile", false, 13003984269646512864], [16542808166767769916, "serde_urlencoded", false, 5962132738142893364], [16622232390123975175, "tokio_rustls", false, 1208730580187490855], [17652733826348741533, "webpki_roots", false, 48767020403797127], [18066890886671768183, "base64", false, 4217096401605978664]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/reqwest-1e9fb008d000d48e/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}