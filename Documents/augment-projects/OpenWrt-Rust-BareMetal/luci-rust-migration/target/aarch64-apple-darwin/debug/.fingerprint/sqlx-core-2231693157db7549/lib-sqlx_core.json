{"rustc": 12610991425282158916, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 8276155916380437441, "path": 1464324610927948152, "deps": [[5103565458935487, "futures_io", false, 2970476946043351126], [40386456601120721, "percent_encoding", false, 12768663558983198052], [530211389790465181, "hex", false, 9293654812647571534], [788558663644978524, "crossbeam_queue", false, 2672921764160910396], [966925859616469517, "ahash", false, 3421778691149007702], [1162433738665300155, "crc", false, 14554587877469771891], [1464803193346256239, "event_listener", false, 10970178403793022519], [1811549171721445101, "futures_channel", false, 1044082373667107368], [3150220818285335163, "url", false, 17365812633056385040], [3405817021026194662, "hashlink", false, 4928528739643940162], [3646857438214563691, "futures_intrusive", false, 8857970245638936917], [3666196340704888985, "smallvec", false, 10717565421150804034], [3712811570531045576, "byteorder", false, 849775014855067972], [3722963349756955755, "once_cell", false, 334531857116249603], [5986029879202738730, "log", false, 10834319452123524630], [6493259146304816786, "indexmap", false, 14810908250333954140], [7620660491849607393, "futures_core", false, 13580037134113986251], [8008191657135824715, "thiserror", false, 14771840948386242388], [8606274917505247608, "tracing", false, 16308205881308244551], [9538054652646069845, "tokio", false, 10861959258753612748], [9689903380558560274, "serde", false, 14136723675802054954], [9857275760291862238, "sha2", false, 9531985975045215587], [10629569228670356391, "futures_util", false, 9647879112712347318], [10862088793507253106, "sqlformat", false, 12771873729627650435], [11295624341523567602, "rustls", false, 4434311683637829848], [12170264697963848012, "either", false, 7963492147964576766], [15367738274754116744, "serde_json", false, 13238213450351272871], [15932120279885307830, "memchr", false, 7705667738631488573], [16066129441945555748, "bytes", false, 4565292215235062127], [16311359161338405624, "rustls_pemfile", false, 13003984269646512864], [16973251432615581304, "tokio_stream", false, 9021437871124156142], [17106256174509013259, "atoi", false, 4766840823442141503], [17605717126308396068, "paste", false, 16336438724705383013], [17652733826348741533, "webpki_roots", false, 48767020403797127]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/sqlx-core-2231693157db7549/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}