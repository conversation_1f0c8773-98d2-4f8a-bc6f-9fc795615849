{"rustc": 12610991425282158916, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 5347358027863023418, "path": 1464324610927948152, "deps": [[5103565458935487, "futures_io", false, 15376309003648435263], [40386456601120721, "percent_encoding", false, 2509561799552248605], [530211389790465181, "hex", false, 13883657252374227177], [788558663644978524, "crossbeam_queue", false, 18243242715559725702], [966925859616469517, "ahash", false, 4124037160932644340], [1162433738665300155, "crc", false, 5941227415777742484], [1464803193346256239, "event_listener", false, 4670365874066476319], [1811549171721445101, "futures_channel", false, 16415464856875380475], [3150220818285335163, "url", false, 17256223529465966128], [3405817021026194662, "hashlink", false, 10339899279260682155], [3646857438214563691, "futures_intrusive", false, 713935586902329798], [3666196340704888985, "smallvec", false, 12962829693529687877], [3712811570531045576, "byteorder", false, 6871077805452748601], [3722963349756955755, "once_cell", false, 2981104805269227908], [5986029879202738730, "log", false, 1770106447332161632], [6493259146304816786, "indexmap", false, 1262762110262403357], [7620660491849607393, "futures_core", false, 9871408481566969237], [8008191657135824715, "thiserror", false, 2760009951614222754], [8606274917505247608, "tracing", false, 12040434844566102166], [9538054652646069845, "tokio", false, 2828153908726863307], [9689903380558560274, "serde", false, 10546623119610134092], [9857275760291862238, "sha2", false, 6519965631980054170], [10629569228670356391, "futures_util", false, 17865305775096533088], [10862088793507253106, "sqlformat", false, 8954250368695005788], [11295624341523567602, "rustls", false, 4451573629530455376], [12170264697963848012, "either", false, 8101000874084354865], [15367738274754116744, "serde_json", false, 17312044369037065190], [15932120279885307830, "memchr", false, 3684347945741720878], [16066129441945555748, "bytes", false, 2535480087087026774], [16311359161338405624, "rustls_pemfile", false, 1142050160302753749], [16973251432615581304, "tokio_stream", false, 13037420510808397676], [17106256174509013259, "atoi", false, 13405504673646509886], [17605717126308396068, "paste", false, 16336438724705383013], [17652733826348741533, "webpki_roots", false, 2379076710472445952]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/sqlx-core-6493027b417f307e/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}