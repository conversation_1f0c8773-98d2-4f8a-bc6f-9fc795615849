{"rustc": 12610991425282158916, "features": "[\"_rt-tokio\", \"any\", \"default\", \"json\", \"macros\", \"migrate\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlite\", \"sqlx-macros\", \"sqlx-sqlite\", \"tls-rustls\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 8276155916380437441, "path": 17138315423223932329, "deps": [[228475551920078470, "sqlx_macros", false, 5739501244420404438], [996810380461694889, "sqlx_core", false, 858840423923576291], [11838249260056359578, "sqlx_sqlite", false, 12109991398872071181]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/sqlx-e4f79b80f0798a1a/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}