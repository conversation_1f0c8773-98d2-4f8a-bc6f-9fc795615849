{"rustc": 12610991425282158916, "features": "[\"cors\", \"default\", \"fs\", \"futures-util\", \"httpdate\", \"mime\", \"mime_guess\", \"percent-encoding\", \"set-status\", \"tokio\", \"tokio-util\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 8276155916380437441, "path": 14047276478755195388, "deps": [[40386456601120721, "percent_encoding", false, 12768663558983198052], [784494742817713399, "tower_service", false, 4442681223396543130], [1288403060204016458, "tokio_util", false, 17510627985722106482], [1906322745568073236, "pin_project_lite", false, 134321414572171600], [6304235478050270880, "httpdate", false, 14719243838630256911], [7712452662827335977, "tower_layer", false, 7854083056695682258], [7896293946984509699, "bitflags", false, 5406568567613549215], [8606274917505247608, "tracing", false, 16308205881308244551], [9010263965687315507, "http", false, 12613709012635136463], [9538054652646069845, "tokio", false, 10861959258753612748], [10229185211513642314, "mime", false, 18018448476383414204], [10629569228670356391, "futures_util", false, 9647879112712347318], [12475322156296016012, "http_range_header", false, 1785906797071509129], [14084095096285906100, "http_body", false, 299757274620178649], [16066129441945555748, "bytes", false, 4565292215235062127], [16900715236047033623, "http_body_util", false, 1987702712407650247], [18071510856783138481, "mime_guess", false, 5210526038662419225]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/tower-http-0ff0297613c2ec75/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 551198452465029181}