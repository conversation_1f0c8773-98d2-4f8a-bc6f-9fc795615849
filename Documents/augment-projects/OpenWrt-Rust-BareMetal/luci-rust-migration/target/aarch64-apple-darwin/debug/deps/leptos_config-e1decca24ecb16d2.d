/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/aarch64-apple-darwin/debug/deps/leptos_config-e1decca24ecb16d2.d: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/leptos_config-0.6.15/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/leptos_config-0.6.15/src/errors.rs

/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/aarch64-apple-darwin/debug/deps/libleptos_config-e1decca24ecb16d2.rmeta: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/leptos_config-0.6.15/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/leptos_config-0.6.15/src/errors.rs

/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/leptos_config-0.6.15/src/lib.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/leptos_config-0.6.15/src/errors.rs:

# env-dep:CARGO_CRATE_NAME=leptos_config
# env-dep:LEPTOS_OUTPUT_NAME
