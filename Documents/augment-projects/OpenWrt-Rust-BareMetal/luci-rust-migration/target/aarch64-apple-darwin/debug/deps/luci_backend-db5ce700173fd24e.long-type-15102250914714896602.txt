axum::middleware::FromFn<fn(axum::extract::State<Arc<AppState>>, axum::http::Request<Body>, Next) -> impl std::future::Future<Output = Result<Response<Body>, axum::http::StatusCode>> {csrf_middleware}, Arc<AppState>, AuthService<Route>, _>
axum::middleware::FromFn<fn(axum::extract::State<Arc<AppState>>, axum::http::Request<Body>, Next) -> impl std::future::Future<Output = Result<Response<Body>, axum::http::StatusCode>> {csrf_middleware}, Arc<AppState>, AuthService<Route>, _>: Service<axum::http::Request<Body>>
RequestIdService<axum::middleware::FromFn<fn(axum::extract::State<Arc<AppState>>, axum::http::Request<Body>, Next) -> impl std::future::Future<Output = Result<Response<Body>, axum::http::StatusCode>> {csrf_middleware}, Arc<AppState>, AuthService<Route>, _>>
tower_http::trace::Trace<Cors<RequestIdService<axum::middleware::FromFn<fn(axum::extract::State<Arc<AppState>>, axum::http::Request<Body>, Next) -> impl std::future::Future<Output = Result<Response<Body>, axum::http::StatusCode>> {csrf_middleware}, Arc<AppState>, AuthService<Route>, _>>>, SharedClassifier<ServerErrorsAsFailures>>
