axum::middleware::FromFn<fn(axum::extract::State<Arc<AppState>>, axum::http::Request<Body>, Next) -> impl std::future::Future<Output = Result<Response<Body>, axum::http::StatusCode>> {csrf_middleware}, Arc<AppState>, Route, _>
axum::middleware::FromFn<fn(axum::extract::State<Arc<AppState>>, axum::http::Request<Body>, Next) -> impl std::future::Future<Output = Result<Response<Body>, axum::http::StatusCode>> {csrf_middleware}, Arc<AppState>, Route, _>: Service<axum::http::Request<Body>>
