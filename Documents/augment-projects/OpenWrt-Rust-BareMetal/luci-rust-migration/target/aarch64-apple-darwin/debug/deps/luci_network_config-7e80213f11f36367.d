/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/aarch64-apple-darwin/debug/deps/luci_network_config-7e80213f11f36367.d: crates/network-config/src/lib.rs crates/network-config/src/interface.rs crates/network-config/src/wifi.rs crates/network-config/src/dhcp.rs crates/network-config/src/firewall.rs crates/network-config/src/routing.rs

/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/aarch64-apple-darwin/debug/deps/libluci_network_config-7e80213f11f36367.rlib: crates/network-config/src/lib.rs crates/network-config/src/interface.rs crates/network-config/src/wifi.rs crates/network-config/src/dhcp.rs crates/network-config/src/firewall.rs crates/network-config/src/routing.rs

/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/aarch64-apple-darwin/debug/deps/libluci_network_config-7e80213f11f36367.rmeta: crates/network-config/src/lib.rs crates/network-config/src/interface.rs crates/network-config/src/wifi.rs crates/network-config/src/dhcp.rs crates/network-config/src/firewall.rs crates/network-config/src/routing.rs

crates/network-config/src/lib.rs:
crates/network-config/src/interface.rs:
crates/network-config/src/wifi.rs:
crates/network-config/src/dhcp.rs:
crates/network-config/src/firewall.rs:
crates/network-config/src/routing.rs:
