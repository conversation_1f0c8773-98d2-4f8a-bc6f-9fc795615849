/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/aarch64-apple-darwin/debug/deps/luci_web_ui-92c4f37b0a0b55fc.d: crates/web-ui/src/lib.rs crates/web-ui/src/components/mod.rs crates/web-ui/src/components/header.rs crates/web-ui/src/components/footer.rs crates/web-ui/src/components/navigation.rs crates/web-ui/src/components/forms.rs crates/web-ui/src/components/tables.rs crates/web-ui/src/components/modals.rs crates/web-ui/src/components/status.rs crates/web-ui/src/components/charts.rs crates/web-ui/src/components/buttons.rs crates/web-ui/src/components/cards.rs crates/web-ui/src/components/alerts.rs crates/web-ui/src/components/loading.rs crates/web-ui/src/components/package_manager.rs crates/web-ui/src/pages/mod.rs crates/web-ui/src/pages/home.rs crates/web-ui/src/pages/login.rs crates/web-ui/src/pages/profile.rs crates/web-ui/src/pages/system.rs crates/web-ui/src/pages/network.rs crates/web-ui/src/pages/wireless.rs crates/web-ui/src/pages/services.rs crates/web-ui/src/pages/packages.rs crates/web-ui/src/pages/administration.rs crates/web-ui/src/pages/status.rs crates/web-ui/src/pages/processes.rs crates/web-ui/src/pages/network_monitor.rs crates/web-ui/src/pages/logs.rs crates/web-ui/src/pages/device_info.rs crates/web-ui/src/pages/logout.rs crates/web-ui/src/pages/not_found.rs crates/web-ui/src/api/mod.rs crates/web-ui/src/auth/mod.rs crates/web-ui/src/utils/mod.rs

/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/aarch64-apple-darwin/debug/deps/luci_web_ui-92c4f37b0a0b55fc: crates/web-ui/src/lib.rs crates/web-ui/src/components/mod.rs crates/web-ui/src/components/header.rs crates/web-ui/src/components/footer.rs crates/web-ui/src/components/navigation.rs crates/web-ui/src/components/forms.rs crates/web-ui/src/components/tables.rs crates/web-ui/src/components/modals.rs crates/web-ui/src/components/status.rs crates/web-ui/src/components/charts.rs crates/web-ui/src/components/buttons.rs crates/web-ui/src/components/cards.rs crates/web-ui/src/components/alerts.rs crates/web-ui/src/components/loading.rs crates/web-ui/src/components/package_manager.rs crates/web-ui/src/pages/mod.rs crates/web-ui/src/pages/home.rs crates/web-ui/src/pages/login.rs crates/web-ui/src/pages/profile.rs crates/web-ui/src/pages/system.rs crates/web-ui/src/pages/network.rs crates/web-ui/src/pages/wireless.rs crates/web-ui/src/pages/services.rs crates/web-ui/src/pages/packages.rs crates/web-ui/src/pages/administration.rs crates/web-ui/src/pages/status.rs crates/web-ui/src/pages/processes.rs crates/web-ui/src/pages/network_monitor.rs crates/web-ui/src/pages/logs.rs crates/web-ui/src/pages/device_info.rs crates/web-ui/src/pages/logout.rs crates/web-ui/src/pages/not_found.rs crates/web-ui/src/api/mod.rs crates/web-ui/src/auth/mod.rs crates/web-ui/src/utils/mod.rs

crates/web-ui/src/lib.rs:
crates/web-ui/src/components/mod.rs:
crates/web-ui/src/components/header.rs:
crates/web-ui/src/components/footer.rs:
crates/web-ui/src/components/navigation.rs:
crates/web-ui/src/components/forms.rs:
crates/web-ui/src/components/tables.rs:
crates/web-ui/src/components/modals.rs:
crates/web-ui/src/components/status.rs:
crates/web-ui/src/components/charts.rs:
crates/web-ui/src/components/buttons.rs:
crates/web-ui/src/components/cards.rs:
crates/web-ui/src/components/alerts.rs:
crates/web-ui/src/components/loading.rs:
crates/web-ui/src/components/package_manager.rs:
crates/web-ui/src/pages/mod.rs:
crates/web-ui/src/pages/home.rs:
crates/web-ui/src/pages/login.rs:
crates/web-ui/src/pages/profile.rs:
crates/web-ui/src/pages/system.rs:
crates/web-ui/src/pages/network.rs:
crates/web-ui/src/pages/wireless.rs:
crates/web-ui/src/pages/services.rs:
crates/web-ui/src/pages/packages.rs:
crates/web-ui/src/pages/administration.rs:
crates/web-ui/src/pages/status.rs:
crates/web-ui/src/pages/processes.rs:
crates/web-ui/src/pages/network_monitor.rs:
crates/web-ui/src/pages/logs.rs:
crates/web-ui/src/pages/device_info.rs:
crates/web-ui/src/pages/logout.rs:
crates/web-ui/src/pages/not_found.rs:
crates/web-ui/src/api/mod.rs:
crates/web-ui/src/auth/mod.rs:
crates/web-ui/src/utils/mod.rs:

# env-dep:CARGO_PKG_VERSION=0.1.0
