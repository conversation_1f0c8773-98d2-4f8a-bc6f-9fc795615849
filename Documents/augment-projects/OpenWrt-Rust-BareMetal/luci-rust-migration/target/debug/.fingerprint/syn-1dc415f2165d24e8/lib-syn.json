{"rustc": 12610991425282158916, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 5347358027863023418, "path": 246365071399544307, "deps": [[1988483478007900009, "unicode_ident", false, 7051989512881297407], [3060637413840920116, "proc_macro2", false, 10640795608452349674], [17990358020177143287, "quote", false, 18333074591917059830]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-1dc415f2165d24e8/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}