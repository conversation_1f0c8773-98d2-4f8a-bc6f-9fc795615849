{"rustc": 12610991425282158916, "features": "[\"convert-case\", \"convert_case\", \"toml\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 1398949370558892206, "path": 18251780531834609415, "deps": [[6502365400774175331, "nom", false, 910542721486896130], [6517602928339163454, "path<PERSON><PERSON>", false, 17832312495831660820], [9689903380558560274, "serde", false, 3848762872012495298], [13475460906694513802, "convert_case", false, 14035273312574989948], [15609422047640926750, "toml", false, 1556929952764332265]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/config-404bf871861fcad3/dep-lib-config", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}