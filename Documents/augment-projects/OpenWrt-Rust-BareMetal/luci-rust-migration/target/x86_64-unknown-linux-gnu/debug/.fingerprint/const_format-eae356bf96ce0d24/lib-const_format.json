{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"__debug\", \"__docsrs\", \"__inline_const_pat_tests\", \"__only_new_tests\", \"__test\", \"all\", \"assert\", \"assertc\", \"assertcp\", \"const_generics\", \"constant_time_as_str\", \"default\", \"derive\", \"fmt\", \"konst\", \"more_str_macros\", \"nightly_const_generics\", \"rust_1_51\", \"rust_1_64\", \"rust_1_83\"]", "target": 18050621619102943376, "profile": 8276155916380437441, "path": 18179775019003437824, "deps": [[18351378648494636016, "const_format_proc_macros", false, 17494927987624146538]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/const_format-eae356bf96ce0d24/dep-lib-const_format", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}