{"rustc": 12610991425282158916, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 336243669335521001, "path": 12816429852546212729, "deps": [[5103565458935487, "futures_io", false, 9618460121682252315], [1811549171721445101, "futures_channel", false, 6619149441586977114], [7013762810557009322, "futures_sink", false, 2226861642752555191], [7620660491849607393, "futures_core", false, 15796512297331967126], [10629569228670356391, "futures_util", false, 14192550503455506588], [12779779637805422465, "futures_executor", false, 11090784000218427671], [16240732885093539806, "futures_task", false, 2480569834083479842]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-6bb6b794dfaa0106/dep-lib-futures", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}