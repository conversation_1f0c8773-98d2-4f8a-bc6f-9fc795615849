{"rustc": 12610991425282158916, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 336243669335521001, "path": 6011282182865037972, "deps": [[5103565458935487, "futures_io", false, 9618460121682252315], [1615478164327904835, "pin_utils", false, 5615366490408702909], [1811549171721445101, "futures_channel", false, 6619149441586977114], [1906322745568073236, "pin_project_lite", false, 5323191018017362993], [5451793922601807560, "slab", false, 4976081879072057436], [7013762810557009322, "futures_sink", false, 2226861642752555191], [7620660491849607393, "futures_core", false, 15796512297331967126], [10565019901765856648, "futures_macro", false, 1097966073998957207], [15932120279885307830, "memchr", false, 4299244883900047162], [16240732885093539806, "futures_task", false, 2480569834083479842]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-util-374c776bd45d3ba1/dep-lib-futures_util", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}