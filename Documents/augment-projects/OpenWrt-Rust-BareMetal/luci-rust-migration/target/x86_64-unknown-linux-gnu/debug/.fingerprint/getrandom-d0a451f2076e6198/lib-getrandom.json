{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 8276155916380437441, "path": 9288519678686256829, "deps": [[2828590642173593838, "cfg_if", false, 12852206555468660464], [4684437522915235464, "libc", false, 10698173523991304841]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/getrandom-d0a451f2076e6198/dep-lib-getrandom", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}