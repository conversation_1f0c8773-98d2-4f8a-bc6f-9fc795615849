{"rustc": 12610991425282158916, "features": "[\"default\", \"eventsource\", \"futures-channel\", \"futures-core\", \"futures-sink\", \"http\", \"json\", \"pin-project\", \"serde\", \"serde_json\", \"websocket\"]", "declared_features": "[\"default\", \"eventsource\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-sink\", \"http\", \"io-util\", \"json\", \"pin-project\", \"serde\", \"serde_json\", \"websocket\"]", "target": 7289951416308014359, "profile": 8276155916380437441, "path": 9145437262956966740, "deps": [[1811549171721445101, "futures_channel", false, 8488502335257080605], [5921074888975346911, "gloo_utils", false, 3323730848816761869], [6264115378959545688, "pin_project", false, 1090928494353607045], [6946689283190175495, "wasm_bindgen", false, 8369606442925031806], [7013762810557009322, "futures_sink", false, 10637894365016026443], [7620660491849607393, "futures_core", false, 12019518742760719579], [8008191657135824715, "thiserror", false, 505562848608999413], [8264480821543757363, "web_sys", false, 14983989196059031668], [9003359908906038687, "js_sys", false, 12468347507749864799], [9010263965687315507, "http", false, 16055653144562189156], [9689903380558560274, "serde", false, 5454848078472089762], [15367738274754116744, "serde_json", false, 3914536398149862816], [15917073803248137067, "wasm_bindgen_futures", false, 2196133078609114715]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/gloo-net-842a9b2853cc9592/dep-lib-gloo_net", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}