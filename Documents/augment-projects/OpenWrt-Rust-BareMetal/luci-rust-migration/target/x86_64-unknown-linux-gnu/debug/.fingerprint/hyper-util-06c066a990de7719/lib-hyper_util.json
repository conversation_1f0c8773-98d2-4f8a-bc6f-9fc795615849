{"rustc": 12610991425282158916, "features": "[\"default\", \"http1\", \"server\", \"service\", \"tokio\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"client\", \"client-legacy\", \"client-proxy\", \"client-proxy-system\", \"default\", \"full\", \"http1\", \"http2\", \"server\", \"server-auto\", \"server-graceful\", \"service\", \"tokio\", \"tracing\"]", "target": 11100538814903412163, "profile": 8276155916380437441, "path": 7819165594066076225, "deps": [[784494742817713399, "tower_service", false, 15780374687700419677], [1906322745568073236, "pin_project_lite", false, 5323191018017362993], [7620660491849607393, "futures_core", false, 12019518742760719579], [9010263965687315507, "http", false, 16055653144562189156], [9538054652646069845, "tokio", false, 16360372107967791165], [11957360342995674422, "hyper", false, 15700873010947325356], [14084095096285906100, "http_body", false, 4575142527393318410], [16066129441945555748, "bytes", false, 6513950132950299987]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/hyper-util-06c066a990de7719/dep-lib-hyper_util", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}