{"rustc": 12610991425282158916, "features": "[\"async\", \"default\", \"futures\", \"tokio\"]", "declared_features": "[\"async\", \"default\", \"embedded\", \"futures\", \"heapless\", \"tokio\"]", "target": 9547349434854658631, "profile": 2330448797067240312, "path": 15797202546639175259, "deps": [[2706460456408817945, "futures", false, 5724969143726236598], [4684437522915235464, "libc", false, 10698173523991304841], [5986029879202738730, "log", false, 12505227303937941088], [6462525672642925639, "nix", false, 10708413809638136979], [8008191657135824715, "thiserror", false, 3499798811992374282], [8319709847752024821, "uuid", false, 177046616733966257], [9538054652646069845, "tokio", false, 12349215854270953497], [9689903380558560274, "serde", false, 11122198959288429526], [9897246384292347999, "chrono", false, 17615299954058252999], [12984406303388944089, "luci_shared_types", false, 13627163228231109404], [13625485746686963219, "anyhow", false, 14954495546622415525], [14940550291033027160, "luci_utilities", false, 12476616950063425069], [15367738274754116744, "serde_json", false, 2950909924165467761], [16126725806440309987, "build_script_build", false, 3553727270994510909]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/luci-opkg-integration-618e39b856e0cd3f/dep-lib-luci_opkg_integration", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}