{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16126725806440309987, "build_script_build", false, 990648407031037282]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-gnu/debug/build/luci-opkg-integration-7aef5af3b5eb09ac/output", "paths": ["src/", "build.rs"]}}, {"RerunIfEnvChanged": {"var": "OPENWRT_SDK_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBOPKG_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALLOW_CROSS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALLOW_CROSS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_PKG_CONFIG_ALLOW_CROSS", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALLOW_CROSS", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "TARGET_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 0, "compile_kind": 0}