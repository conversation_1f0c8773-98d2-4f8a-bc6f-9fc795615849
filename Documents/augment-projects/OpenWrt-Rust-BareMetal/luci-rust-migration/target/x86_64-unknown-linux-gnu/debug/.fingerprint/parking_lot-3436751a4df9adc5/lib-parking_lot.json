{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"hardware-lock-elision\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\"]", "target": 9887373948397848517, "profile": 8276155916380437441, "path": 7635392820602258278, "deps": [[4269498962362888130, "parking_lot_core", false, 10764939395942948154], [8081351675046095464, "lock_api", false, 17322985351356782164]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/parking_lot-3436751a4df9adc5/dep-lib-parking_lot", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}