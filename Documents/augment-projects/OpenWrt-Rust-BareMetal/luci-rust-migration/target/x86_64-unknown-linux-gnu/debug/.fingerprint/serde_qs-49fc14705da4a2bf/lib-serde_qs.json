{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"actix\", \"actix-web2\", \"actix-web3\", \"actix-web4\", \"actix2\", \"actix3\", \"actix4\", \"axum\", \"axum-framework\", \"default\", \"futures\", \"tracing\", \"warp\", \"warp-framework\"]", "target": 2985555824720197883, "profile": 8276155916380437441, "path": 2646537982214479835, "deps": [[40386456601120721, "percent_encoding", false, 11022033732633488580], [8008191657135824715, "thiserror", false, 505562848608999413], [9689903380558560274, "serde", false, 3848762872012495298]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/serde_qs-49fc14705da4a2bf/dep-lib-serde_qs", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}