{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 15943748010645046320, "profile": 9397467488959674509, "path": 15645375341567252865, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/toml_write-ee60d2d9da627b13/dep-lib-toml_write", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}