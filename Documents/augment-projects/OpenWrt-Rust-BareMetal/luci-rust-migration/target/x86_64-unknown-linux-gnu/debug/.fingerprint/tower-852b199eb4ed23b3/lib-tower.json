{"rustc": 12610991425282158916, "features": "[\"default\", \"log\", \"tracing\"]", "declared_features": "[\"__common\", \"balance\", \"buffer\", \"default\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project\", \"pin-project-lite\", \"rand\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"timeout\", \"tokio\", \"tokio-stream\", \"tokio-util\", \"tracing\", \"util\"]", "target": 3486700084251681313, "profile": 5347358027863023418, "path": 12131259316515473555, "deps": [[784494742817713399, "tower_service", false, 2425922253611155421], [7712452662827335977, "tower_layer", false, 1713016323481235305], [8606274917505247608, "tracing", false, 12872239363484259693]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/tower-852b199eb4ed23b3/dep-lib-tower", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}