{"$message_type":"diagnostic","message":"linking with `cc` failed: exit status: 1","code":null,"level":"error","spans":[],"children":[{"message":" \"cc\" \"-Wl,--version-script=/var/folders/68/zyhcqh4544qf2tw0x_bmcwxr0000gn/T/rustcZfWVrD/list\" \"-Wl,--no-undefined-version\" \"-m64\" \"/var/folders/68/zyhcqh4544qf2tw0x_bmcwxr0000gn/T/rustcZfWVrD/symbols.o\" \"<5 object files omitted>\" \"-Wl,--as-needed\" \"-Wl,-Bstatic\" \"/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/x86_64-unknown-linux-gnu/debug/deps/{libwasm_bindgen_futures-3cae40cfb0ca1279.rlib,libfutures_util-f6b3caf1907529d6.rlib,libmemchr-ca8c14071039a2b8.rlib,libfutures_io-7efcb5bc299719b6.rlib,libslab-5c4ae4ef27a4b8e8.rlib,libfutures_channel-b6c1b0c8522f092f.rlib,libpin_project_lite-076d05a5aafc44dc.rlib,libfutures_sink-5cf8ecd062960f72.rlib,libfutures_task-03073cccd21e36f0.rlib,libpin_utils-704de6fae01f048b.rlib,libfutures_core-740b4eac91fce2b6.rlib,libweb_sys-e4c677a8ca1a76bc.rlib,libjs_sys-f1976e6fbe522045.rlib,libwasm_bindgen-04aa74bbc28c99c1.rlib,libonce_cell-144e9a50f1f0f555.rlib,libcfg_if-738d21ef36e83a17.rlib}.rlib\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-gnu/lib/{libstd-*,libpanic_unwind-*,libobject-*,libmemchr-*,libaddr2line-*,libgimli-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libminiz_oxide-*,libadler2-*,libunwind-*,libcfg_if-*,liblibc-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"-Wl,-Bdynamic\" \"-lgcc_s\" \"-lutil\" \"-lrt\" \"-lpthread\" \"-lm\" \"-ldl\" \"-lc\" \"-L\" \"/var/folders/68/zyhcqh4544qf2tw0x_bmcwxr0000gn/T/rustcZfWVrD/raw-dylibs\" \"-Wl,--eh-frame-hdr\" \"-Wl,-z,noexecstack\" \"-L\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-gnu/lib\" \"-o\" \"/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/x86_64-unknown-linux-gnu/debug/deps/libwasm_streams-1aec239ce3dee5de.so\" \"-Wl,--gc-sections\" \"-shared\" \"-Wl,-z,relro,-z,now\" \"-nodefaultlibs\" \"-nostartfiles\" \"-static\"","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"some arguments are omitted. use `--verbose` to show all linker arguments","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"ld: unknown options: --version-script=/var/folders/68/zyhcqh4544qf2tw0x_bmcwxr0000gn/T/rustcZfWVrD/list --no-undefined-version --as-needed -Bstatic -Bdynamic --eh-frame-hdr -z --gc-sections -z -z \nclang: error: linker command failed with exit code 1 (use -v to see invocation)\n","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: linking with `cc` failed: exit status: 1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m:  \"cc\" \"-Wl,--version-script=/var/folders/68/zyhcqh4544qf2tw0x_bmcwxr0000gn/T/rustcZfWVrD/list\" \"-Wl,--no-undefined-version\" \"-m64\" \"/var/folders/68/zyhcqh4544qf2tw0x_bmcwxr0000gn/T/rustcZfWVrD/symbols.o\" \"<5 object files omitted>\" \"-Wl,--as-needed\" \"-Wl,-Bstatic\" \"/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/x86_64-unknown-linux-gnu/debug/deps/{libwasm_bindgen_futures-3cae40cfb0ca1279.rlib,libfutures_util-f6b3caf1907529d6.rlib,libmemchr-ca8c14071039a2b8.rlib,libfutures_io-7efcb5bc299719b6.rlib,libslab-5c4ae4ef27a4b8e8.rlib,libfutures_channel-b6c1b0c8522f092f.rlib,libpin_project_lite-076d05a5aafc44dc.rlib,libfutures_sink-5cf8ecd062960f72.rlib,libfutures_task-03073cccd21e36f0.rlib,libpin_utils-704de6fae01f048b.rlib,libfutures_core-740b4eac91fce2b6.rlib,libweb_sys-e4c677a8ca1a76bc.rlib,libjs_sys-f1976e6fbe522045.rlib,libwasm_bindgen-04aa74bbc28c99c1.rlib,libonce_cell-144e9a50f1f0f555.rlib,libcfg_if-738d21ef36e83a17.rlib}.rlib\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-gnu/lib/{libstd-*,libpanic_unwind-*,libobject-*,libmemchr-*,libaddr2line-*,libgimli-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libminiz_oxide-*,libadler2-*,libunwind-*,libcfg_if-*,liblibc-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"-Wl,-Bdynamic\" \"-lgcc_s\" \"-lutil\" \"-lrt\" \"-lpthread\" \"-lm\" \"-ldl\" \"-lc\" \"-L\" \"/var/folders/68/zyhcqh4544qf2tw0x_bmcwxr0000gn/T/rustcZfWVrD/raw-dylibs\" \"-Wl,--eh-frame-hdr\" \"-Wl,-z,noexecstack\" \"-L\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-gnu/lib\" \"-o\" \"/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/luci-rust-migration/target/x86_64-unknown-linux-gnu/debug/deps/libwasm_streams-1aec239ce3dee5de.so\" \"-Wl,--gc-sections\" \"-shared\" \"-Wl,-z,relro,-z,now\" \"-nodefaultlibs\" \"-nostartfiles\" \"-static\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: some arguments are omitted. use `--verbose` to show all linker arguments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: ld: unknown options: --version-script=/var/folders/68/zyhcqh4544qf2tw0x_bmcwxr0000gn/T/rustcZfWVrD/list --no-undefined-version --as-needed -Bstatic -Bdynamic --eh-frame-hdr -z --gc-sections -z -z \u001b[0m\n\u001b[0m          clang: error: linker command failed with exit code 1 (use -v to see invocation)\u001b[0m\n\u001b[0m          \u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error\u001b[0m\n\n"}
