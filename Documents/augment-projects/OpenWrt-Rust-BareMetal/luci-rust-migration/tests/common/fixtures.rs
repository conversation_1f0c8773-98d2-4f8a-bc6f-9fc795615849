// Test fixtures and setup utilities

use super::*;
use std::sync::Once;

static INIT: Once = Once::new();

/// Initialize global test environment (called once per test run)
pub fn init_test_environment() {
    INIT.call_once(|| {
        // Initialize logging for tests
        let _ = env_logger::builder()
            .filter_level(log::LevelFilter::Debug)
            .is_test(true)
            .try_init();
        
        // Set test-specific environment variables
        std::env::set_var("RUST_BACKTRACE", "1");
        std::env::set_var("TEST_MODE", "1");
    });
}

/// Test fixture for OPKG integration testing
pub struct OpkgTestFixture {
    pub temp_dir: TempDir,
    pub opkg_dir: PathBuf,
    pub lists_dir: PathBuf,
    pub info_dir: PathBuf,
    pub status_file: PathBuf,
}

impl OpkgTestFixture {
    pub fn new() -> TestResult<Self> {
        init_test_environment();
        
        let temp_dir = TempDir::new()?;
        let base_path = temp_dir.path();
        
        let opkg_dir = base_path.join("var/opkg-lists");
        let lists_dir = opkg_dir.clone();
        let info_dir = base_path.join("usr/lib/opkg/info");
        let status_file = base_path.join("usr/lib/opkg/status");
        
        // Create directory structure
        std::fs::create_dir_all(&opkg_dir)?;
        std::fs::create_dir_all(&info_dir)?;
        std::fs::create_dir_all(status_file.parent().unwrap())?;
        
        // Create initial status file
        std::fs::write(&status_file, "")?;
        
        Ok(Self {
            temp_dir,
            opkg_dir,
            lists_dir,
            info_dir,
            status_file,
        })
    }
    
    pub fn add_package_list(&self, repo_name: &str, packages: &[&str]) -> TestResult<()> {
        let list_file = self.lists_dir.join(format!("{}.gz", repo_name));
        let content = packages.join("\n");
        
        // For simplicity, we'll write uncompressed content
        // In a real test, you might want to use gzip compression
        std::fs::write(list_file.with_extension(""), content)?;
        Ok(())
    }
    
    pub fn add_installed_package(&self, name: &str, version: &str, info: &str) -> TestResult<()> {
        // Add to status file
        let status_entry = format!(
            "Package: {}\nVersion: {}\nStatus: install user installed\n\n",
            name, version
        );
        
        let mut current_status = std::fs::read_to_string(&self.status_file)?;
        current_status.push_str(&status_entry);
        std::fs::write(&self.status_file, current_status)?;
        
        // Add info files
        let control_file = self.info_dir.join(format!("{}.control", name));
        std::fs::write(control_file, info)?;
        
        let list_file = self.info_dir.join(format!("{}.list", name));
        std::fs::write(list_file, format!("/usr/bin/{}\n", name))?;
        
        Ok(())
    }
    
    pub fn get_opkg_dir(&self) -> &PathBuf {
        &self.opkg_dir
    }
    
    pub fn get_status_file(&self) -> &PathBuf {
        &self.status_file
    }
}

/// Test fixture for UCI configuration testing
pub struct UciTestFixture {
    pub temp_dir: TempDir,
    pub config_dir: PathBuf,
}

impl UciTestFixture {
    pub fn new() -> TestResult<Self> {
        init_test_environment();
        
        let temp_dir = TempDir::new()?;
        let config_dir = temp_dir.path().join("etc/config");
        
        std::fs::create_dir_all(&config_dir)?;
        
        Ok(Self {
            temp_dir,
            config_dir,
        })
    }
    
    pub fn create_config_file(&self, name: &str, content: &str) -> TestResult<PathBuf> {
        let config_file = self.config_dir.join(name);
        std::fs::write(&config_file, content)?;
        Ok(config_file)
    }
    
    pub fn create_network_config(&self) -> TestResult<PathBuf> {
        let content = crate::common::mock_data::MockUciData::create_network_config();
        self.create_config_file("network", &content)
    }
    
    pub fn create_wireless_config(&self) -> TestResult<PathBuf> {
        let content = crate::common::mock_data::MockUciData::create_wireless_config();
        self.create_config_file("wireless", &content)
    }
    
    pub fn create_system_config(&self) -> TestResult<PathBuf> {
        let content = crate::common::mock_data::MockUciData::create_system_config();
        self.create_config_file("system", &content)
    }
    
    pub fn get_config_dir(&self) -> &PathBuf {
        &self.config_dir
    }
}

/// Test fixture for network interface testing
pub struct NetworkTestFixture {
    pub temp_dir: TempDir,
    pub sys_class_net: PathBuf,
    pub proc_net: PathBuf,
}

impl NetworkTestFixture {
    pub fn new() -> TestResult<Self> {
        init_test_environment();
        
        let temp_dir = TempDir::new()?;
        let base_path = temp_dir.path();
        
        let sys_class_net = base_path.join("sys/class/net");
        let proc_net = base_path.join("proc/net");
        
        std::fs::create_dir_all(&sys_class_net)?;
        std::fs::create_dir_all(&proc_net)?;
        
        Ok(Self {
            temp_dir,
            sys_class_net,
            proc_net,
        })
    }
    
    pub fn create_interface(&self, name: &str, mac: &str, stats: &str) -> TestResult<()> {
        let interface_dir = self.sys_class_net.join(name);
        std::fs::create_dir_all(&interface_dir)?;
        
        // Create address file (MAC address)
        std::fs::write(interface_dir.join("address"), mac)?;
        
        // Create operstate file
        std::fs::write(interface_dir.join("operstate"), "up")?;
        
        // Create statistics directory
        let stats_dir = interface_dir.join("statistics");
        std::fs::create_dir_all(&stats_dir)?;
        
        // Parse and write statistics
        for line in stats.lines() {
            if let Some((key, value)) = line.split_once(':') {
                std::fs::write(stats_dir.join(key.trim()), value.trim())?;
            }
        }
        
        Ok(())
    }
    
    pub fn create_proc_net_dev(&self, interfaces: &[(&str, &str)]) -> TestResult<()> {
        let mut content = String::from("Inter-|   Receive                                                |  Transmit\n");
        content.push_str(" face |bytes    packets errs drop fifo frame compressed multicast|bytes    packets errs drop fifo colls carrier compressed\n");
        
        for (name, stats) in interfaces {
            content.push_str(&format!("{}: {}\n", name, stats));
        }
        
        std::fs::write(self.proc_net.join("dev"), content)?;
        Ok(())
    }
    
    pub fn get_sys_class_net(&self) -> &PathBuf {
        &self.sys_class_net
    }
    
    pub fn get_proc_net(&self) -> &PathBuf {
        &self.proc_net
    }
}

/// Test fixture for authentication testing
pub struct AuthTestFixture {
    pub temp_dir: TempDir,
    pub passwd_file: PathBuf,
    pub shadow_file: PathBuf,
    pub group_file: PathBuf,
}

impl AuthTestFixture {
    pub fn new() -> TestResult<Self> {
        init_test_environment();
        
        let temp_dir = TempDir::new()?;
        let base_path = temp_dir.path();
        
        let passwd_file = base_path.join("etc/passwd");
        let shadow_file = base_path.join("etc/shadow");
        let group_file = base_path.join("etc/group");
        
        std::fs::create_dir_all(base_path.join("etc"))?;
        
        // Create basic files
        std::fs::write(&passwd_file, "root:x:0:0:root:/root:/bin/ash\n")?;
        std::fs::write(&shadow_file, "root:$1$salt$hash:19000:0:99999:7:::\n")?;
        std::fs::write(&group_file, "root:x:0:\nadmin:x:1000:\nusers:x:1001:\n")?;
        
        Ok(Self {
            temp_dir,
            passwd_file,
            shadow_file,
            group_file,
        })
    }
    
    pub fn add_user(&self, username: &str, uid: u32, gid: u32, groups: &[&str]) -> TestResult<()> {
        // Add to passwd
        let passwd_entry = format!("{}:x:{}:{}:{}:/home/<USER>/bin/ash\n", 
                                 username, uid, gid, username, username);
        let mut passwd_content = std::fs::read_to_string(&self.passwd_file)?;
        passwd_content.push_str(&passwd_entry);
        std::fs::write(&self.passwd_file, passwd_content)?;
        
        // Add to shadow
        let shadow_entry = format!("{}:$1$salt$hash:19000:0:99999:7:::\n", username);
        let mut shadow_content = std::fs::read_to_string(&self.shadow_file)?;
        shadow_content.push_str(&shadow_entry);
        std::fs::write(&self.shadow_file, shadow_content)?;
        
        // Add to groups
        let mut group_content = std::fs::read_to_string(&self.group_file)?;
        for group in groups {
            if !group_content.contains(&format!("{}:", group)) {
                group_content.push_str(&format!("{}:x:{}:\n", group, 2000 + groups.len()));
            }
            // Add user to group (simplified)
            group_content = group_content.replace(
                &format!("{}:x:", group),
                &format!("{}:x:{}:{}", group, 2000 + groups.len(), username)
            );
        }
        std::fs::write(&self.group_file, group_content)?;
        
        Ok(())
    }
    
    pub fn get_passwd_file(&self) -> &PathBuf {
        &self.passwd_file
    }
    
    pub fn get_shadow_file(&self) -> &PathBuf {
        &self.shadow_file
    }
    
    pub fn get_group_file(&self) -> &PathBuf {
        &self.group_file
    }
}

/// Test fixture for system monitoring
pub struct SystemMonitorTestFixture {
    pub temp_dir: TempDir,
    pub proc_dir: PathBuf,
    pub sys_dir: PathBuf,
}

impl SystemMonitorTestFixture {
    pub fn new() -> TestResult<Self> {
        init_test_environment();
        
        let temp_dir = TempDir::new()?;
        let base_path = temp_dir.path();
        
        let proc_dir = base_path.join("proc");
        let sys_dir = base_path.join("sys");
        
        std::fs::create_dir_all(&proc_dir)?;
        std::fs::create_dir_all(&sys_dir)?;
        
        Ok(Self {
            temp_dir,
            proc_dir,
            sys_dir,
        })
    }
    
    pub fn create_proc_stat(&self, cpu_stats: &str) -> TestResult<()> {
        std::fs::write(self.proc_dir.join("stat"), cpu_stats)?;
        Ok(())
    }
    
    pub fn create_proc_meminfo(&self, memory_info: &str) -> TestResult<()> {
        std::fs::write(self.proc_dir.join("meminfo"), memory_info)?;
        Ok(())
    }
    
    pub fn create_proc_uptime(&self, uptime: &str) -> TestResult<()> {
        std::fs::write(self.proc_dir.join("uptime"), uptime)?;
        Ok(())
    }
    
    pub fn create_proc_loadavg(&self, loadavg: &str) -> TestResult<()> {
        std::fs::write(self.proc_dir.join("loadavg"), loadavg)?;
        Ok(())
    }
    
    pub fn get_proc_dir(&self) -> &PathBuf {
        &self.proc_dir
    }
    
    pub fn get_sys_dir(&self) -> &PathBuf {
        &self.sys_dir
    }
}
