// Mock data generators for testing

use super::*;
use luci_shared_types::*;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// Generate mock package data for testing
pub struct MockPackageData;

impl MockPackageData {
    pub fn create_package_list(count: usize) -> Vec<PackageInfo> {
        (0..count)
            .map(|i| Self::create_package(&format!("test-package-{}", i), &format!("1.{}.0", i)))
            .collect()
    }
    
    pub fn create_package(name: &str, version: &str) -> PackageInfo {
        PackageInfo {
            name: name.to_string(),
            version: version.to_string(),
            description: format!("Mock package {} for testing purposes", name),
            size: 1024 + (name.len() * 100) as u64,
            installed: false,
            available: true,
            repository: "mock-repo".to_string(),
            section: "test".to_string(),
            maintainer: "<EMAIL>".to_string(),
            architecture: "all".to_string(),
            depends: vec![],
            conflicts: vec![],
            provides: vec![],
            replaces: vec![],
            filename: format!("{}-{}.ipk", name, version),
            md5sum: format!("{:032x}", md5::compute(format!("{}-{}", name, version))),
            sha256sum: format!("{:064x}", sha2::Sha256::digest(format!("{}-{}", name, version).as_bytes())),
            tags: vec!["test".to_string(), "mock".to_string()],
            status: PackageStatus::Available,
            priority: PackagePriority::Optional,
            essential: false,
            auto_installed: false,
            install_time: None,
        }
    }
    
    pub fn create_installed_package(name: &str, version: &str) -> PackageInfo {
        let mut package = Self::create_package(name, version);
        package.installed = true;
        package.status = PackageStatus::Installed;
        package.install_time = Some(Utc::now());
        package
    }
    
    pub fn create_package_with_dependencies(name: &str, version: &str, deps: Vec<String>) -> PackageInfo {
        let mut package = Self::create_package(name, version);
        package.depends = deps;
        package
    }
    
    pub fn create_essential_package(name: &str, version: &str) -> PackageInfo {
        let mut package = Self::create_package(name, version);
        package.essential = true;
        package.priority = PackagePriority::Required;
        package.installed = true;
        package.status = PackageStatus::Installed;
        package
    }
}

/// Generate mock network configuration data
pub struct MockNetworkData;

impl MockNetworkData {
    pub fn create_ethernet_interface(name: &str) -> NetworkInterface {
        NetworkInterface {
            name: name.to_string(),
            device: format!("eth{}", name.chars().last().unwrap_or('0')),
            protocol: NetworkProtocol::Static,
            enabled: true,
            auto: true,
            ip_address: Some(format!("192.168.{}.1", name.chars().last().unwrap_or('1') as u8)),
            netmask: Some("*************".to_string()),
            gateway: Some(format!("192.168.{}.254", name.chars().last().unwrap_or('1') as u8)),
            dns_servers: vec!["*******".to_string(), "*******".to_string()],
            mtu: Some(1500),
            mac_address: Some(format!("00:11:22:33:44:{:02x}", name.len())),
            bridge_ports: vec![],
            vlan_id: None,
            wifi_config: None,
        }
    }
    
    pub fn create_wifi_interface(name: &str, ssid: &str) -> NetworkInterface {
        let wifi_config = WifiConfig {
            ssid: ssid.to_string(),
            encryption: WifiEncryption::WPA2,
            password: Some("test-password".to_string()),
            hidden: false,
            channel: 6,
            mode: WifiMode::AccessPoint,
            country_code: "US".to_string(),
            max_clients: 50,
            isolate_clients: false,
        };
        
        NetworkInterface {
            name: name.to_string(),
            device: format!("wlan{}", name.chars().last().unwrap_or('0')),
            protocol: NetworkProtocol::Static,
            enabled: true,
            auto: true,
            ip_address: Some("***********".to_string()),
            netmask: Some("*************".to_string()),
            gateway: None,
            dns_servers: vec![],
            mtu: Some(1500),
            mac_address: Some(format!("02:11:22:33:44:{:02x}", name.len())),
            bridge_ports: vec![],
            vlan_id: None,
            wifi_config: Some(wifi_config),
        }
    }
    
    pub fn create_bridge_interface(name: &str, ports: Vec<String>) -> NetworkInterface {
        NetworkInterface {
            name: name.to_string(),
            device: format!("br-{}", name),
            protocol: NetworkProtocol::Static,
            enabled: true,
            auto: true,
            ip_address: Some("***********".to_string()),
            netmask: Some("*************".to_string()),
            gateway: None,
            dns_servers: vec![],
            mtu: Some(1500),
            mac_address: Some(format!("03:11:22:33:44:{:02x}", name.len())),
            bridge_ports: ports,
            vlan_id: None,
            wifi_config: None,
        }
    }
    
    pub fn create_dhcp_interface(name: &str) -> NetworkInterface {
        NetworkInterface {
            name: name.to_string(),
            device: format!("eth{}", name.chars().last().unwrap_or('0')),
            protocol: NetworkProtocol::DHCP,
            enabled: true,
            auto: true,
            ip_address: None,
            netmask: None,
            gateway: None,
            dns_servers: vec![],
            mtu: Some(1500),
            mac_address: Some(format!("04:11:22:33:44:{:02x}", name.len())),
            bridge_ports: vec![],
            vlan_id: None,
            wifi_config: None,
        }
    }
}

/// Generate mock system monitoring data
pub struct MockSystemData;

impl MockSystemData {
    pub fn create_cpu_usage() -> CpuUsage {
        CpuUsage {
            overall: 25.5,
            per_core: vec![20.0, 30.0, 25.0, 28.0],
            user: 15.0,
            system: 8.0,
            idle: 75.0,
            iowait: 1.5,
            irq: 0.3,
            softirq: 0.2,
            steal: 0.0,
            guest: 0.0,
        }
    }
    
    pub fn create_memory_usage() -> MemoryUsage {
        MemoryUsage {
            total: 1024 * 1024 * 1024, // 1GB
            used: 512 * 1024 * 1024,   // 512MB
            free: 512 * 1024 * 1024,   // 512MB
            available: 768 * 1024 * 1024, // 768MB
            buffers: 64 * 1024 * 1024,  // 64MB
            cached: 128 * 1024 * 1024,  // 128MB
            swap_total: 256 * 1024 * 1024, // 256MB
            swap_used: 0,
            swap_free: 256 * 1024 * 1024,
        }
    }
    
    pub fn create_storage_usage() -> StorageUsage {
        StorageUsage {
            total: 8 * 1024 * 1024 * 1024, // 8GB
            used: 2 * 1024 * 1024 * 1024,  // 2GB
            available: 6 * 1024 * 1024 * 1024, // 6GB
            filesystem: "/dev/root".to_string(),
            mount_point: "/".to_string(),
            filesystem_type: "ext4".to_string(),
        }
    }
    
    pub fn create_network_stats(interface: &str) -> NetworkInterfaceStats {
        NetworkInterfaceStats {
            interface: interface.to_string(),
            rx_bytes: 1024 * 1024 * 10, // 10MB
            tx_bytes: 1024 * 1024 * 5,  // 5MB
            rx_packets: 10000,
            tx_packets: 8000,
            rx_errors: 0,
            tx_errors: 0,
            rx_dropped: 0,
            tx_dropped: 0,
            collisions: 0,
            multicast: 100,
            speed: Some(1000), // 1Gbps
            duplex: Some("full".to_string()),
            link_detected: true,
        }
    }
    
    pub fn create_system_info() -> SystemInfo {
        SystemInfo {
            hostname: "test-router".to_string(),
            kernel_version: "5.15.0".to_string(),
            architecture: "aarch64".to_string(),
            cpu_model: "ARM Cortex-A53".to_string(),
            cpu_cores: 4,
            total_memory: 1024 * 1024 * 1024, // 1GB
            uptime: Uptime {
                seconds: 86400, // 1 day
                load_average: LoadAverage {
                    one_minute: 0.5,
                    five_minutes: 0.3,
                    fifteen_minutes: 0.2,
                    running_processes: 2,
                    total_processes: 150,
                },
            },
            boot_time: Utc::now() - chrono::Duration::days(1),
        }
    }
    
    pub fn create_process_info(pid: u32, name: &str) -> ProcessInfo {
        ProcessInfo {
            pid,
            name: name.to_string(),
            command: format!("/usr/bin/{}", name),
            state: ProcessState::Running,
            cpu_usage: 2.5,
            memory_usage: 1024 * 1024, // 1MB
            start_time: Utc::now() - chrono::Duration::hours(1),
            user: "root".to_string(),
            group: "root".to_string(),
        }
    }
}

/// Generate mock authentication data
pub struct MockAuthData;

impl MockAuthData {
    pub fn create_user_info(username: &str) -> UserInfo {
        UserInfo {
            username: username.to_string(),
            full_name: format!("Test User {}", username),
            email: format!("{}@test.example.com", username),
            groups: vec!["users".to_string()],
            shell: "/bin/sh".to_string(),
            home_directory: format!("/home/<USER>", username),
            last_login: Some(Utc::now() - chrono::Duration::hours(2)),
            account_locked: false,
            password_expired: false,
            created_at: Utc::now() - chrono::Duration::days(30),
            updated_at: Utc::now() - chrono::Duration::days(1),
        }
    }
    
    pub fn create_admin_user() -> UserInfo {
        let mut user = Self::create_user_info("admin");
        user.groups = vec!["admin".to_string(), "users".to_string()];
        user
    }
    
    pub fn create_locked_user(username: &str) -> UserInfo {
        let mut user = Self::create_user_info(username);
        user.account_locked = true;
        user
    }
}

/// Generate mock UCI configuration data
pub struct MockUciData;

impl MockUciData {
    pub fn create_network_config() -> String {
        r#"
config interface 'loopback'
    option ifname 'lo'
    option proto 'static'
    option ipaddr '127.0.0.1'
    option netmask '*********'

config interface 'lan'
    option ifname 'eth0'
    option proto 'static'
    option ipaddr '***********'
    option netmask '*************'

config interface 'wan'
    option ifname 'eth1'
    option proto 'dhcp'
"#.trim().to_string()
    }
    
    pub fn create_wireless_config() -> String {
        r#"
config wifi-device 'radio0'
    option type 'mac80211'
    option channel '6'
    option hwmode '11g'
    option path 'platform/********.wmac'
    option htmode 'HT20'

config wifi-iface 'default_radio0'
    option device 'radio0'
    option network 'lan'
    option mode 'ap'
    option ssid 'OpenWrt'
    option encryption 'psk2'
    option key 'test-password'
"#.trim().to_string()
    }
    
    pub fn create_system_config() -> String {
        r#"
config system
    option hostname 'OpenWrt'
    option timezone 'UTC'
    option ttylogin '0'
    option log_size '64'
    option urandom_seed '0'

config timeserver 'ntp'
    option enabled '1'
    option enable_server '0'
    list server '0.openwrt.pool.ntp.org'
    list server '1.openwrt.pool.ntp.org'
"#.trim().to_string()
    }
}

/// Generate mock OPKG command outputs
pub struct MockOpkgData;

impl MockOpkgData {
    pub fn create_package_list_output() -> String {
        r#"base-files - 1.0-1 - Base filesystem for OpenWrt
busybox - 1.35.0-1 - Core utilities for embedded systems
dropbear - 2022.83-1 - Small SSH server and client
firewall4 - 2023.05.10-1 - OpenWrt firewall based on nftables
kernel - 5.15.118-1 - Virtual kernel package
kmod-ath9k - 5.15.118-1 - Atheros 802.11n wireless driver
libc - 1.2.4-1 - C library
logd - 2023.05.15-1 - OpenWrt system log daemon
netifd - 2023.05.12-1 - Network interface daemon
opkg - 2023.05.31-1 - Package manager
uci - 2023.08.10-1 - Unified Configuration Interface
uhttpd - 2023.06.25-1 - Tiny HTTP server"#.to_string()
    }
    
    pub fn create_package_info_output(package_name: &str) -> String {
        format!(r#"Package: {}
Version: 1.0-1
Depends: libc
Status: install user installed
Architecture: all
Installed-Time: 1234567890
Auto-Installed: no
Installed-Size: 1024
Description: Test package for {}"#, package_name, package_name)
    }
    
    pub fn create_update_output() -> String {
        r#"Downloading http://downloads.openwrt.org/releases/23.05.0/packages/aarch64_generic/base/Packages.gz
Updated list of available packages in /var/opkg-lists/openwrt_base
Downloading http://downloads.openwrt.org/releases/23.05.0/packages/aarch64_generic/packages/Packages.gz
Updated list of available packages in /var/opkg-lists/openwrt_packages"#.to_string()
    }
    
    pub fn create_install_output(package_name: &str) -> String {
        format!(r#"Installing {} (1.0-1) to root...
Downloading http://downloads.openwrt.org/releases/23.05.0/packages/aarch64_generic/base/{}_1.0-1_all.ipk
Installing {} (1.0-1) to root...
Configuring {}."#, package_name, package_name, package_name, package_name)
    }
    
    pub fn create_remove_output(package_name: &str) -> String {
        format!("Removing package {} from root...", package_name)
    }
}
