// Common test utilities and helpers for the LuCI Rust migration test suite

pub mod test_helpers;
pub mod mock_data;
pub mod fixtures;

// Re-export commonly used testing utilities
pub use test_helpers::*;
pub use mock_data::*;
pub use fixtures::*;

// Common test imports that most tests will need
pub use std::collections::HashMap;
pub use std::path::PathBuf;
pub use std::sync::Arc;
pub use tokio::sync::RwLock;

// Test-specific dependencies
pub use tempfile::{TempDir, NamedTempFile};

// Common test result type
pub type TestResult<T = ()> = Result<T, Box<dyn std::error::Error + Send + Sync>>;

// Test configuration constants
pub const TEST_TIMEOUT_SECONDS: u64 = 30;
pub const TEST_RETRY_COUNT: usize = 3;
pub const TEST_PACKAGE_COUNT: usize = 100;
pub const TEST_USER_COUNT: usize = 10;

// Test environment setup
pub fn setup_test_environment() -> TestResult<()> {
    // Initialize test logging
    let _ = env_logger::builder()
        .filter_level(log::LevelFilter::Debug)
        .is_test(true)
        .try_init();
    
    Ok(())
}

// Test cleanup
pub fn cleanup_test_environment() -> TestResult<()> {
    // Cleanup any global test state
    Ok(())
}

// Async test helper macro
#[macro_export]
macro_rules! async_test {
    ($test_name:ident, $test_body:expr) => {
        #[tokio::test]
        async fn $test_name() -> TestResult<()> {
            setup_test_environment()?;
            let result = $test_body.await;
            cleanup_test_environment()?;
            result
        }
    };
}

// Test assertion helpers
#[macro_export]
macro_rules! assert_contains {
    ($haystack:expr, $needle:expr) => {
        assert!(
            $haystack.contains($needle),
            "Expected '{}' to contain '{}'",
            $haystack,
            $needle
        );
    };
}

#[macro_export]
macro_rules! assert_not_contains {
    ($haystack:expr, $needle:expr) => {
        assert!(
            !$haystack.contains($needle),
            "Expected '{}' to not contain '{}'",
            $haystack,
            $needle
        );
    };
}

#[macro_export]
macro_rules! assert_json_eq {
    ($left:expr, $right:expr) => {
        let left_json: serde_json::Value = serde_json::from_str($left)
            .expect("Failed to parse left JSON");
        let right_json: serde_json::Value = serde_json::from_str($right)
            .expect("Failed to parse right JSON");
        assert_eq!(left_json, right_json);
    };
}

// Performance testing helpers
pub struct PerformanceTimer {
    start: std::time::Instant,
    name: String,
}

impl PerformanceTimer {
    pub fn new(name: &str) -> Self {
        Self {
            start: std::time::Instant::now(),
            name: name.to_string(),
        }
    }
    
    pub fn elapsed(&self) -> std::time::Duration {
        self.start.elapsed()
    }
    
    pub fn assert_under(&self, max_duration: std::time::Duration) {
        let elapsed = self.elapsed();
        assert!(
            elapsed <= max_duration,
            "Performance test '{}' took {:?}, expected under {:?}",
            self.name,
            elapsed,
            max_duration
        );
    }
}

impl Drop for PerformanceTimer {
    fn drop(&mut self) {
        println!("Performance test '{}' completed in {:?}", self.name, self.elapsed());
    }
}

// Memory usage testing
pub fn get_memory_usage() -> usize {
    // Simple memory usage approximation for testing
    // In a real implementation, this would use more sophisticated memory tracking
    std::mem::size_of::<usize>() * 1024 // Placeholder
}

pub fn assert_memory_under(max_bytes: usize) {
    let current = get_memory_usage();
    assert!(
        current <= max_bytes,
        "Memory usage {} bytes exceeds maximum {} bytes",
        current,
        max_bytes
    );
}

// Network testing helpers
pub fn is_port_available(port: u16) -> bool {
    std::net::TcpListener::bind(("127.0.0.1", port)).is_ok()
}

pub fn find_available_port() -> u16 {
    for port in 8000..9000 {
        if is_port_available(port) {
            return port;
        }
    }
    panic!("No available ports found in range 8000-9000");
}

// File system testing helpers
pub fn create_test_file(content: &str) -> TestResult<NamedTempFile> {
    use std::io::Write;
    
    let mut file = NamedTempFile::new()?;
    file.write_all(content.as_bytes())?;
    file.flush()?;
    Ok(file)
}

pub fn create_test_dir() -> TestResult<TempDir> {
    Ok(TempDir::new()?)
}

// Process testing helpers
pub fn is_process_running(name: &str) -> bool {
    // Simple process check for testing
    // In a real implementation, this would check actual running processes
    !name.is_empty() // Placeholder
}

// Configuration testing helpers
pub fn create_test_config(config_data: &str) -> TestResult<PathBuf> {
    let temp_file = create_test_file(config_data)?;
    let path = temp_file.path().to_path_buf();
    std::mem::forget(temp_file); // Keep file alive
    Ok(path)
}

// Database testing helpers (for future use)
pub fn setup_test_database() -> TestResult<String> {
    // Return in-memory SQLite URL for testing
    Ok("sqlite::memory:".to_string())
}

// HTTP testing helpers
pub fn create_test_client() -> reqwest::Client {
    reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(TEST_TIMEOUT_SECONDS))
        .build()
        .expect("Failed to create test HTTP client")
}

// Retry logic for flaky tests
pub async fn retry_async<F, Fut, T, E>(mut f: F, max_retries: usize) -> Result<T, E>
where
    F: FnMut() -> Fut,
    Fut: std::future::Future<Output = Result<T, E>>,
    E: std::fmt::Debug,
{
    let mut last_error = None;
    
    for attempt in 0..=max_retries {
        match f().await {
            Ok(result) => return Ok(result),
            Err(error) => {
                if attempt == max_retries {
                    last_error = Some(error);
                    break;
                } else {
                    // Wait before retry with exponential backoff
                    let delay = std::time::Duration::from_millis(100 * (1 << attempt));
                    tokio::time::sleep(delay).await;
                }
            }
        }
    }
    
    Err(last_error.unwrap())
}

// Test data validation helpers
pub fn validate_package_info(package: &luci_shared_types::PackageInfo) -> TestResult<()> {
    assert!(!package.name.is_empty(), "Package name cannot be empty");
    assert!(!package.version.is_empty(), "Package version cannot be empty");
    Ok(())
}

pub fn validate_network_interface(interface: &luci_shared_types::NetworkInterface) -> TestResult<()> {
    assert!(!interface.name.is_empty(), "Interface name cannot be empty");
    Ok(())
}

// Test environment detection
pub fn is_ci_environment() -> bool {
    std::env::var("CI").is_ok() || std::env::var("GITHUB_ACTIONS").is_ok()
}

pub fn is_openwrt_environment() -> bool {
    std::path::Path::new("/etc/openwrt_release").exists()
}

// Skip test helper for environment-specific tests
#[macro_export]
macro_rules! skip_if_not_openwrt {
    () => {
        if !is_openwrt_environment() {
            println!("Skipping test: Not running on OpenWrt");
            return Ok(());
        }
    };
}

#[macro_export]
macro_rules! skip_if_ci {
    () => {
        if is_ci_environment() {
            println!("Skipping test: Running in CI environment");
            return Ok(());
        }
    };
}
