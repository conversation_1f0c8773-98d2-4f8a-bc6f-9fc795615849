// Test helper functions and utilities

use super::*;
use std::process::{Command, Stdio};
use std::io::{<PERSON><PERSON>R<PERSON>, BufReader};

/// Test helper for creating temporary directories with specific structure
pub struct TestEnvironment {
    pub temp_dir: TempDir,
    pub config_dir: PathBuf,
    pub data_dir: PathBuf,
    pub log_dir: PathBuf,
}

impl TestEnvironment {
    pub fn new() -> TestResult<Self> {
        let temp_dir = TempDir::new()?;
        let base_path = temp_dir.path();
        
        let config_dir = base_path.join("config");
        let data_dir = base_path.join("data");
        let log_dir = base_path.join("logs");
        
        std::fs::create_dir_all(&config_dir)?;
        std::fs::create_dir_all(&data_dir)?;
        std::fs::create_dir_all(&log_dir)?;
        
        Ok(Self {
            temp_dir,
            config_dir,
            data_dir,
            log_dir,
        })
    }
    
    pub fn create_config_file(&self, name: &str, content: &str) -> TestResult<PathBuf> {
        let file_path = self.config_dir.join(name);
        std::fs::write(&file_path, content)?;
        Ok(file_path)
    }
    
    pub fn create_data_file(&self, name: &str, content: &[u8]) -> TestResult<PathBuf> {
        let file_path = self.data_dir.join(name);
        std::fs::write(&file_path, content)?;
        Ok(file_path)
    }
    
    pub fn get_log_path(&self, name: &str) -> PathBuf {
        self.log_dir.join(name)
    }
}

/// Mock command executor for testing system commands
pub struct MockCommandExecutor {
    pub commands: Arc<RwLock<Vec<String>>>,
    pub responses: HashMap<String, (i32, String, String)>, // command -> (exit_code, stdout, stderr)
}

impl MockCommandExecutor {
    pub fn new() -> Self {
        Self {
            commands: Arc::new(RwLock::new(Vec::new())),
            responses: HashMap::new(),
        }
    }
    
    pub fn add_response(&mut self, command: &str, exit_code: i32, stdout: &str, stderr: &str) {
        self.responses.insert(
            command.to_string(),
            (exit_code, stdout.to_string(), stderr.to_string())
        );
    }
    
    pub async fn execute(&self, command: &str, args: &[&str]) -> TestResult<(i32, String, String)> {
        let full_command = format!("{} {}", command, args.join(" "));
        
        // Record the command
        self.commands.write().await.push(full_command.clone());
        
        // Return mock response if available
        if let Some((exit_code, stdout, stderr)) = self.responses.get(&full_command) {
            Ok((*exit_code, stdout.clone(), stderr.clone()))
        } else {
            // Default response for unknown commands
            Ok((0, "".to_string(), "".to_string()))
        }
    }
    
    pub async fn get_executed_commands(&self) -> Vec<String> {
        self.commands.read().await.clone()
    }
    
    pub async fn clear_commands(&self) {
        self.commands.write().await.clear();
    }
}

/// Test helper for creating mock HTTP servers
pub struct MockHttpServer {
    pub port: u16,
    pub responses: HashMap<String, (u16, String)>, // path -> (status_code, body)
}

impl MockHttpServer {
    pub fn new() -> Self {
        Self {
            port: find_available_port(),
            responses: HashMap::new(),
        }
    }
    
    pub fn add_response(&mut self, path: &str, status_code: u16, body: &str) {
        self.responses.insert(path.to_string(), (status_code, body.to_string()));
    }
    
    pub fn url(&self) -> String {
        format!("http://127.0.0.1:{}", self.port)
    }
    
    pub fn endpoint_url(&self, path: &str) -> String {
        format!("{}{}", self.url(), path)
    }
}

/// Test helper for validating JSON responses
pub fn assert_json_structure(json_str: &str, expected_keys: &[&str]) -> TestResult<()> {
    let json: serde_json::Value = serde_json::from_str(json_str)?;
    
    if let serde_json::Value::Object(obj) = json {
        for key in expected_keys {
            assert!(
                obj.contains_key(*key),
                "JSON missing expected key: {}",
                key
            );
        }
    } else {
        panic!("Expected JSON object, got: {}", json_str);
    }
    
    Ok(())
}

/// Test helper for validating API error responses
pub fn assert_api_error(response: &str, expected_error_code: &str) -> TestResult<()> {
    let json: serde_json::Value = serde_json::from_str(response)?;
    
    if let Some(error_code) = json.get("error_code") {
        assert_eq!(
            error_code.as_str().unwrap(),
            expected_error_code,
            "Unexpected error code in response: {}",
            response
        );
    } else {
        panic!("Response missing error_code field: {}", response);
    }
    
    Ok(())
}

/// Test helper for creating test packages
pub fn create_test_package(name: &str, version: &str) -> luci_shared_types::PackageInfo {
    luci_shared_types::PackageInfo {
        name: name.to_string(),
        version: version.to_string(),
        description: format!("Test package {}", name),
        size: 1024,
        installed: false,
        available: true,
        repository: "test-repo".to_string(),
        section: "test".to_string(),
        maintainer: "<EMAIL>".to_string(),
        architecture: "all".to_string(),
        depends: vec![],
        conflicts: vec![],
        provides: vec![],
        replaces: vec![],
        filename: format!("{}-{}.ipk", name, version),
        md5sum: "d41d8cd98f00b204e9800998ecf8427e".to_string(),
        sha256sum: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855".to_string(),
        tags: vec![],
        status: luci_shared_types::PackageStatus::Available,
        priority: luci_shared_types::PackagePriority::Optional,
        essential: false,
        auto_installed: false,
        install_time: None,
    }
}

/// Test helper for creating test network interfaces
pub fn create_test_network_interface(name: &str) -> luci_shared_types::NetworkInterface {
    luci_shared_types::NetworkInterface {
        name: name.to_string(),
        device: format!("{}-device", name),
        protocol: luci_shared_types::NetworkProtocol::Static,
        enabled: true,
        auto: true,
        ip_address: Some("***********".to_string()),
        netmask: Some("*************".to_string()),
        gateway: Some("*************".to_string()),
        dns_servers: vec!["*******".to_string(), "*******".to_string()],
        mtu: Some(1500),
        mac_address: Some("00:11:22:33:44:55".to_string()),
        bridge_ports: vec![],
        vlan_id: None,
        wifi_config: None,
    }
}

/// Test helper for creating test user info
pub fn create_test_user(username: &str) -> luci_shared_types::UserInfo {
    luci_shared_types::UserInfo {
        username: username.to_string(),
        full_name: format!("Test User {}", username),
        email: format!("{}@test.example.com", username),
        groups: vec!["users".to_string()],
        shell: "/bin/sh".to_string(),
        home_directory: format!("/home/<USER>", username),
        last_login: None,
        account_locked: false,
        password_expired: false,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    }
}

/// Test helper for waiting for conditions
pub async fn wait_for_condition<F>(mut condition: F, timeout: std::time::Duration) -> TestResult<()>
where
    F: FnMut() -> bool,
{
    let start = std::time::Instant::now();
    
    while start.elapsed() < timeout {
        if condition() {
            return Ok(());
        }
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;
    }
    
    Err("Condition not met within timeout".into())
}

/// Test helper for comparing file contents
pub fn assert_file_contents(file_path: &std::path::Path, expected_content: &str) -> TestResult<()> {
    let actual_content = std::fs::read_to_string(file_path)?;
    assert_eq!(
        actual_content.trim(),
        expected_content.trim(),
        "File contents do not match for: {}",
        file_path.display()
    );
    Ok(())
}

/// Test helper for checking if a service is running
pub fn is_service_running(service_name: &str) -> bool {
    // Mock implementation for testing
    // In a real OpenWrt environment, this would check actual service status
    !service_name.is_empty()
}

/// Test helper for generating random test data
pub fn generate_random_string(length: usize) -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                            abcdefghijklmnopqrstuvwxyz\
                            0123456789";
    let mut rng = rand::thread_rng();
    
    (0..length)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

/// Test helper for creating temporary UCI configuration
pub fn create_test_uci_config(config_name: &str, sections: &[(&str, &[(&str, &str)])]) -> TestResult<String> {
    let mut config_content = String::new();
    
    for (section_type, options) in sections {
        config_content.push_str(&format!("config {} 'test_section'\n", section_type));
        for (option, value) in *options {
            config_content.push_str(&format!("\toption {} '{}'\n", option, value));
        }
        config_content.push('\n');
    }
    
    Ok(config_content)
}

/// Test helper for validating command output
pub fn assert_command_success(output: &std::process::Output) -> TestResult<()> {
    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("Command failed with stderr: {}", stderr).into());
    }
    Ok(())
}

/// Test helper for parsing command output
pub fn parse_command_output(output: &[u8]) -> TestResult<Vec<String>> {
    let output_str = String::from_utf8_lossy(output);
    Ok(output_str.lines().map(|line| line.to_string()).collect())
}
