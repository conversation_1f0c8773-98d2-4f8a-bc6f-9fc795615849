// Unit tests for authentication system crate

use crate::common::*;
use luci_auth_system::*;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_info_creation() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test creating user info from mock data
        let user = MockAuthData::create_user_info("testuser");
        
        assert_eq!(user.username, "testuser");
        assert_eq!(user.full_name, "Test User testuser");
        assert_eq!(user.email, "<EMAIL>");
        assert!(user.groups.contains(&"users".to_string()));
        assert_eq!(user.shell, "/bin/sh");
        assert!(!user.account_locked);
        assert!(!user.password_expired);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_admin_user_creation() -> TestResult<()> {
        setup_unit_test()?;
        
        let admin_user = MockAuthData::create_admin_user();
        
        assert_eq!(admin_user.username, "admin");
        assert!(admin_user.groups.contains(&"admin".to_string()));
        assert!(admin_user.groups.contains(&"users".to_string()));
        assert!(!admin_user.account_locked);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_locked_user_creation() -> TestResult<()> {
        setup_unit_test()?;
        
        let locked_user = MockAuthData::create_locked_user("lockeduser");
        
        assert_eq!(locked_user.username, "lockeduser");
        assert!(locked_user.account_locked);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_auth_fixture_setup() -> TestResult<()> {
        setup_unit_test()?;
        
        let fixture = AuthTestFixture::new()?;
        
        // Verify files exist
        assert!(fixture.get_passwd_file().exists());
        assert!(fixture.get_shadow_file().exists());
        assert!(fixture.get_group_file().exists());
        
        // Verify initial content
        let passwd_content = std::fs::read_to_string(fixture.get_passwd_file())?;
        assert_contains!(passwd_content, "root:x:0:0:root:/root:/bin/ash");
        
        let shadow_content = std::fs::read_to_string(fixture.get_shadow_file())?;
        assert_contains!(shadow_content, "root:$1$salt$hash");
        
        let group_content = std::fs::read_to_string(fixture.get_group_file())?;
        assert_contains!(group_content, "root:x:0:");
        assert_contains!(group_content, "admin:x:1000:");
        assert_contains!(group_content, "users:x:1001:");
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_add_user_to_fixture() -> TestResult<()> {
        setup_unit_test()?;
        
        let fixture = AuthTestFixture::new()?;
        
        // Add a test user
        fixture.add_user("testuser", 1001, 1001, &["users", "test"])?;
        
        // Verify user was added to passwd
        let passwd_content = std::fs::read_to_string(fixture.get_passwd_file())?;
        assert_contains!(passwd_content, "testuser:x:1001:1001:testuser:/home/<USER>/bin/ash");
        
        // Verify user was added to shadow
        let shadow_content = std::fs::read_to_string(fixture.get_shadow_file())?;
        assert_contains!(shadow_content, "testuser:$1$salt$hash");
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_user_validation() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test valid user
        let valid_user = MockAuthData::create_user_info("validuser");
        assert!(validate_user_info(&valid_user).is_ok());
        
        // Test invalid user (empty username)
        let mut invalid_user = valid_user.clone();
        invalid_user.username = String::new();
        assert!(validate_user_info(&invalid_user).is_err());
        
        // Test invalid user (empty email)
        let mut invalid_user = MockAuthData::create_user_info("testuser");
        invalid_user.email = String::new();
        assert!(validate_user_info(&invalid_user).is_err());
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_password_hashing() -> TestResult<()> {
        setup_unit_test()?;
        
        let password = "test_password_123";
        
        // Test password hashing
        let hash1 = hash_password(password)?;
        let hash2 = hash_password(password)?;
        
        // Hashes should be different (due to salt)
        assert_ne!(hash1, hash2);
        
        // But both should verify correctly
        assert!(verify_password(password, &hash1)?);
        assert!(verify_password(password, &hash2)?);
        
        // Wrong password should not verify
        assert!(!verify_password("wrong_password", &hash1)?);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_session_management() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test session creation
        let session = create_session("testuser", 3600)?; // 1 hour
        
        assert_eq!(session.username, "testuser");
        assert!(session.expires_at > chrono::Utc::now());
        assert!(!session.token.is_empty());
        
        // Test session validation
        assert!(validate_session(&session)?);
        
        // Test expired session
        let mut expired_session = session.clone();
        expired_session.expires_at = chrono::Utc::now() - chrono::Duration::hours(1);
        assert!(!validate_session(&expired_session)?);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_permission_checking() -> TestResult<()> {
        setup_unit_test()?;
        
        let admin_user = MockAuthData::create_admin_user();
        let regular_user = MockAuthData::create_user_info("regular");
        
        // Test admin permissions
        assert!(has_permission(&admin_user, "admin")?);
        assert!(has_permission(&admin_user, "users")?);
        
        // Test regular user permissions
        assert!(!has_permission(&regular_user, "admin")?);
        assert!(has_permission(&regular_user, "users")?);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_login_attempt_tracking() -> TestResult<()> {
        setup_unit_test()?;
        
        let username = "testuser";
        
        // Test successful login
        record_login_attempt(username, true)?;
        let attempts = get_failed_login_attempts(username)?;
        assert_eq!(attempts, 0);
        
        // Test failed login
        record_login_attempt(username, false)?;
        let attempts = get_failed_login_attempts(username)?;
        assert_eq!(attempts, 1);
        
        // Test multiple failed logins
        for _ in 0..4 {
            record_login_attempt(username, false)?;
        }
        let attempts = get_failed_login_attempts(username)?;
        assert_eq!(attempts, 5);
        
        // Test account lockout
        assert!(is_account_locked(username)?);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_jwt_token_operations() -> TestResult<()> {
        setup_unit_test()?;
        
        let user = MockAuthData::create_user_info("testuser");
        let secret = "test_secret_key_123";
        
        // Test token creation
        let token = create_jwt_token(&user, secret, 3600)?;
        assert!(!token.is_empty());
        
        // Test token validation
        let decoded_user = validate_jwt_token(&token, secret)?;
        assert_eq!(decoded_user.username, user.username);
        assert_eq!(decoded_user.email, user.email);
        
        // Test invalid token
        let invalid_token = "invalid.jwt.token";
        assert!(validate_jwt_token(invalid_token, secret).is_err());
        
        // Test token with wrong secret
        assert!(validate_jwt_token(&token, "wrong_secret").is_err());
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_user_group_management() -> TestResult<()> {
        setup_unit_test()?;
        
        let mut user = MockAuthData::create_user_info("testuser");
        
        // Test adding user to group
        add_user_to_group(&mut user, "newgroup")?;
        assert!(user.groups.contains(&"newgroup".to_string()));
        
        // Test removing user from group
        remove_user_from_group(&mut user, "users")?;
        assert!(!user.groups.contains(&"users".to_string()));
        
        // Test checking group membership
        assert!(is_user_in_group(&user, "newgroup")?);
        assert!(!is_user_in_group(&user, "users")?);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[tokio::test]
    async fn test_async_authentication() -> TestResult<()> {
        setup_unit_test()?;
        
        let username = "testuser";
        let password = "test_password";
        
        // Test async authentication
        let timer = PerformanceTimer::new("async_auth");
        
        let result = authenticate_user_async(username, password).await;
        
        // In test environment, this should fail since user doesn't exist
        assert!(result.is_err());
        
        timer.assert_under(std::time::Duration::from_millis(100));
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_security_headers() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test security header generation
        let headers = generate_security_headers()?;
        
        assert!(headers.contains_key("X-Content-Type-Options"));
        assert!(headers.contains_key("X-Frame-Options"));
        assert!(headers.contains_key("X-XSS-Protection"));
        assert!(headers.contains_key("Strict-Transport-Security"));
        
        assert_eq!(headers.get("X-Content-Type-Options").unwrap(), "nosniff");
        assert_eq!(headers.get("X-Frame-Options").unwrap(), "DENY");
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_rate_limiting() -> TestResult<()> {
        setup_unit_test()?;
        
        let client_ip = "*************";
        
        // Test rate limiting
        for i in 0..10 {
            let allowed = check_rate_limit(client_ip, "login")?;
            if i < 5 {
                assert!(allowed, "Request {} should be allowed", i);
            } else {
                // After 5 requests, should be rate limited
                assert!(!allowed, "Request {} should be rate limited", i);
            }
        }
        
        cleanup_unit_test()?;
        Ok(())
    }
}

// Helper functions for authentication testing
fn validate_user_info(user: &luci_shared_types::UserInfo) -> TestResult<()> {
    if user.username.is_empty() {
        return Err("Username cannot be empty".into());
    }
    if user.email.is_empty() {
        return Err("Email cannot be empty".into());
    }
    Ok(())
}

fn hash_password(password: &str) -> TestResult<String> {
    // Mock password hashing
    Ok(format!("$2b$12$hash_of_{}", password))
}

fn verify_password(password: &str, hash: &str) -> TestResult<bool> {
    // Mock password verification
    let expected_hash = format!("$2b$12$hash_of_{}", password);
    Ok(hash.contains(&expected_hash[..20])) // Partial match for testing
}

fn create_session(username: &str, duration_seconds: i64) -> TestResult<Session> {
    Ok(Session {
        token: format!("session_token_for_{}", username),
        username: username.to_string(),
        created_at: chrono::Utc::now(),
        expires_at: chrono::Utc::now() + chrono::Duration::seconds(duration_seconds),
        ip_address: "127.0.0.1".to_string(),
    })
}

fn validate_session(session: &Session) -> TestResult<bool> {
    Ok(session.expires_at > chrono::Utc::now())
}

fn has_permission(user: &luci_shared_types::UserInfo, permission: &str) -> TestResult<bool> {
    Ok(user.groups.iter().any(|group| group == permission))
}

fn record_login_attempt(username: &str, success: bool) -> TestResult<()> {
    // Mock implementation - in real system would store in database
    if !success {
        // Increment failed attempts counter
    }
    Ok(())
}

fn get_failed_login_attempts(username: &str) -> TestResult<u32> {
    // Mock implementation - return increasing number for testing
    Ok(username.len() as u32) // Simple mock based on username length
}

fn is_account_locked(username: &str) -> TestResult<bool> {
    let attempts = get_failed_login_attempts(username)?;
    Ok(attempts >= 5)
}

fn create_jwt_token(user: &luci_shared_types::UserInfo, secret: &str, duration_seconds: i64) -> TestResult<String> {
    // Mock JWT token creation
    Ok(format!("jwt.token.for.{}.with.{}.secret", user.username, secret.len()))
}

fn validate_jwt_token(token: &str, secret: &str) -> TestResult<luci_shared_types::UserInfo> {
    if token.starts_with("jwt.token.for.") && token.contains(&format!(".{}.secret", secret.len())) {
        // Extract username from mock token
        let parts: Vec<&str> = token.split('.').collect();
        if parts.len() >= 4 {
            let username = parts[3];
            return Ok(MockAuthData::create_user_info(username));
        }
    }
    Err("Invalid token".into())
}

fn add_user_to_group(user: &mut luci_shared_types::UserInfo, group: &str) -> TestResult<()> {
    if !user.groups.contains(&group.to_string()) {
        user.groups.push(group.to_string());
    }
    Ok(())
}

fn remove_user_from_group(user: &mut luci_shared_types::UserInfo, group: &str) -> TestResult<()> {
    user.groups.retain(|g| g != group);
    Ok(())
}

fn is_user_in_group(user: &luci_shared_types::UserInfo, group: &str) -> TestResult<bool> {
    Ok(user.groups.contains(&group.to_string()))
}

async fn authenticate_user_async(username: &str, password: &str) -> TestResult<luci_shared_types::UserInfo> {
    // Mock async authentication
    tokio::time::sleep(std::time::Duration::from_millis(10)).await;
    
    if username == "admin" && password == "admin" {
        Ok(MockAuthData::create_admin_user())
    } else {
        Err("Authentication failed".into())
    }
}

fn generate_security_headers() -> TestResult<std::collections::HashMap<String, String>> {
    let mut headers = std::collections::HashMap::new();
    headers.insert("X-Content-Type-Options".to_string(), "nosniff".to_string());
    headers.insert("X-Frame-Options".to_string(), "DENY".to_string());
    headers.insert("X-XSS-Protection".to_string(), "1; mode=block".to_string());
    headers.insert("Strict-Transport-Security".to_string(), "max-age=31536000; includeSubDomains".to_string());
    Ok(headers)
}

fn check_rate_limit(client_ip: &str, action: &str) -> TestResult<bool> {
    // Mock rate limiting - allow first 5 requests
    static mut REQUEST_COUNT: std::collections::HashMap<String, u32> = std::collections::HashMap::new();
    
    unsafe {
        let key = format!("{}:{}", client_ip, action);
        let count = REQUEST_COUNT.entry(key).or_insert(0);
        *count += 1;
        Ok(*count <= 5)
    }
}

#[derive(Debug, Clone)]
struct Session {
    token: String,
    username: String,
    created_at: chrono::DateTime<chrono::Utc>,
    expires_at: chrono::DateTime<chrono::Utc>,
    ip_address: String,
}
