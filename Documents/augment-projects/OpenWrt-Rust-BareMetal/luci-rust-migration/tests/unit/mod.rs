// Unit tests module organization

// Import common test utilities
use crate::common::*;

// Unit test modules for each crate
pub mod opkg_integration_test;
pub mod auth_system_test;
pub mod network_config_test;
pub mod system_monitor_test;
pub mod package_manager_test;
pub mod uci_bindings_test;
pub mod utilities_test;

// Re-export test utilities for use in test modules
pub use crate::common::{TestResult, async_test, assert_contains, assert_not_contains};

// Common test setup for unit tests
pub fn setup_unit_test() -> TestResult<()> {
    crate::common::setup_test_environment()
}

// Common test cleanup for unit tests
pub fn cleanup_unit_test() -> TestResult<()> {
    crate::common::cleanup_test_environment()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_unit_test_setup() -> TestResult<()> {
        setup_unit_test()?;
        cleanup_unit_test()?;
        Ok(())
    }
    
    #[test]
    fn test_common_utilities_available() {
        // Test that common utilities are accessible
        let timer = crate::common::PerformanceTimer::new("test");
        assert!(timer.elapsed().as_nanos() > 0);
        
        // Test port availability check
        let port = crate::common::find_available_port();
        assert!(port >= 8000 && port < 9000);
        
        // Test memory usage function
        let memory = crate::common::get_memory_usage();
        assert!(memory > 0);
    }
    
    #[test]
    fn test_mock_data_generation() -> TestResult<()> {
        // Test package mock data
        let package = crate::common::MockPackageData::create_package("test-pkg", "1.0.0");
        assert_eq!(package.name, "test-pkg");
        assert_eq!(package.version, "1.0.0");
        assert!(!package.description.is_empty());
        
        // Test network mock data
        let interface = crate::common::MockNetworkData::create_ethernet_interface("lan");
        assert_eq!(interface.name, "lan");
        assert!(interface.enabled);
        
        // Test system mock data
        let cpu_usage = crate::common::MockSystemData::create_cpu_usage();
        assert!(cpu_usage.overall > 0.0);
        assert!(!cpu_usage.per_core.is_empty());
        
        Ok(())
    }
    
    #[test]
    fn test_test_fixtures() -> TestResult<()> {
        // Test OPKG fixture
        let opkg_fixture = crate::common::OpkgTestFixture::new()?;
        assert!(opkg_fixture.get_opkg_dir().exists());
        assert!(opkg_fixture.get_status_file().exists());
        
        // Test UCI fixture
        let uci_fixture = crate::common::UciTestFixture::new()?;
        assert!(uci_fixture.get_config_dir().exists());
        
        // Test network fixture
        let network_fixture = crate::common::NetworkTestFixture::new()?;
        assert!(network_fixture.get_sys_class_net().exists());
        assert!(network_fixture.get_proc_net().exists());
        
        Ok(())
    }
    
    #[tokio::test]
    async fn test_async_utilities() -> TestResult<()> {
        // Test retry mechanism
        let mut attempt_count = 0;
        let result = crate::common::retry_async(
            || {
                attempt_count += 1;
                async move {
                    if attempt_count < 3 {
                        Err("Not ready yet")
                    } else {
                        Ok("Success")
                    }
                }
            },
            5
        ).await;
        
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "Success");
        assert_eq!(attempt_count, 3);
        
        // Test wait for condition
        let mut condition_met = false;
        tokio::spawn(async move {
            tokio::time::sleep(std::time::Duration::from_millis(100)).await;
            // In a real test, this would be set by the condition we're waiting for
        });
        
        // Simulate condition being met immediately for test
        condition_met = true;
        
        let result = crate::common::wait_for_condition(
            || condition_met,
            std::time::Duration::from_secs(1)
        ).await;
        
        assert!(result.is_ok());
        
        Ok(())
    }
    
    #[test]
    fn test_environment_detection() {
        // Test CI environment detection
        let is_ci = crate::common::is_ci_environment();
        // This will vary depending on where tests are run
        
        // Test OpenWrt environment detection
        let is_openwrt = crate::common::is_openwrt_environment();
        // This will be false in most test environments
        assert!(!is_openwrt); // Assuming we're not running on actual OpenWrt
    }
    
    #[test]
    fn test_file_utilities() -> TestResult<()> {
        // Test temporary file creation
        let content = "test content";
        let temp_file = crate::common::create_test_file(content)?;
        
        // Verify file contents
        crate::common::assert_file_contents(temp_file.path(), content)?;
        
        // Test temporary directory creation
        let temp_dir = crate::common::create_test_dir()?;
        assert!(temp_dir.path().exists());
        assert!(temp_dir.path().is_dir());
        
        Ok(())
    }
    
    #[test]
    fn test_random_data_generation() {
        // Test random string generation
        let random_str1 = crate::common::generate_random_string(10);
        let random_str2 = crate::common::generate_random_string(10);
        
        assert_eq!(random_str1.len(), 10);
        assert_eq!(random_str2.len(), 10);
        assert_ne!(random_str1, random_str2); // Very unlikely to be the same
        
        // Test that generated string contains only valid characters
        for ch in random_str1.chars() {
            assert!(ch.is_ascii_alphanumeric());
        }
    }
    
    #[test]
    fn test_uci_config_generation() -> TestResult<()> {
        let sections = vec![
            ("interface", vec![("proto", "static"), ("ipaddr", "***********")].as_slice()),
            ("interface", vec![("proto", "dhcp")].as_slice()),
        ];
        
        let config = crate::common::create_test_uci_config("network", &sections)?;
        
        assert_contains!(config, "config interface 'test_section'");
        assert_contains!(config, "option proto 'static'");
        assert_contains!(config, "option ipaddr '***********'");
        assert_contains!(config, "option proto 'dhcp'");
        
        Ok(())
    }
    
    #[test]
    fn test_json_utilities() -> TestResult<()> {
        let json_str = r#"{"name": "test", "version": "1.0", "enabled": true}"#;
        
        // Test JSON structure validation
        crate::common::assert_json_structure(json_str, &["name", "version", "enabled"])?;
        
        // Test API error validation
        let error_response = r#"{"error_code": "INVALID_REQUEST", "message": "Bad request"}"#;
        crate::common::assert_api_error(error_response, "INVALID_REQUEST")?;
        
        Ok(())
    }
    
    #[test]
    fn test_performance_timer() {
        let timer = crate::common::PerformanceTimer::new("test_operation");
        
        // Simulate some work
        std::thread::sleep(std::time::Duration::from_millis(10));
        
        let elapsed = timer.elapsed();
        assert!(elapsed >= std::time::Duration::from_millis(10));
        
        // Test assertion (this should pass)
        timer.assert_under(std::time::Duration::from_millis(100));
    }
    
    #[test]
    fn test_network_utilities() {
        // Test port availability
        let port = crate::common::find_available_port();
        assert!(crate::common::is_port_available(port));
        
        // Test HTTP client creation
        let client = crate::common::create_test_client();
        // Just verify it was created successfully
        assert!(client.timeout().is_some());
    }
}
