// Unit tests for OPKG integration crate

use crate::common::*;
use luci_opkg_integration::*;
use std::process::{Command, Stdio};

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_opkg_interface_creation() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test creating OPKG interface
        // Note: This will fail if opkg is not installed, which is expected in test environments
        let result = OpkgInterface::new();
        
        // In test environment, we expect this to fail since opkg is not available
        // This tests the error handling path
        assert!(result.is_err());
        
        if let Err(OpkgError::CommandFailed(msg)) = result {
            assert_contains!(msg, "opkg");
        }
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_opkg_error_types() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test different error types
        let command_error = OpkgError::CommandFailed("test command failed".to_string());
        assert_contains!(format!("{}", command_error), "test command failed");
        
        let parse_error = OpkgError::ParseError("test parse error".to_string());
        assert_contains!(format!("{}", parse_error), "test parse error");
        
        let io_error = OpkgError::IoError(std::io::Error::new(
            std::io::ErrorKind::NotFound,
            "test io error"
        ));
        assert_contains!(format!("{}", io_error), "test io error");
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_package_info_creation() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test creating package info from mock data
        let package = MockPackageData::create_package("test-package", "1.0.0");
        
        assert_eq!(package.name, "test-package");
        assert_eq!(package.version, "1.0.0");
        assert!(!package.description.is_empty());
        assert_eq!(package.repository, "mock-repo");
        assert_eq!(package.status, luci_shared_types::PackageStatus::Available);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_package_search_criteria() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test package search criteria
        let criteria = PackageSearchCriteria {
            name_pattern: Some("test".to_string()),
            section: Some("base".to_string()),
            installed_only: false,
            available_only: true,
        };
        
        assert_eq!(criteria.name_pattern, Some("test".to_string()));
        assert_eq!(criteria.section, Some("base".to_string()));
        assert!(!criteria.installed_only);
        assert!(criteria.available_only);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_opkg_command_parsing() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test parsing OPKG list output
        let list_output = MockOpkgData::create_package_list_output();
        
        // Verify the mock output contains expected packages
        assert_contains!(list_output, "base-files");
        assert_contains!(list_output, "busybox");
        assert_contains!(list_output, "dropbear");
        assert_contains!(list_output, "firewall4");
        
        // Test parsing individual package info
        let info_output = MockOpkgData::create_package_info_output("test-package");
        assert_contains!(info_output, "Package: test-package");
        assert_contains!(info_output, "Version: 1.0-1");
        assert_contains!(info_output, "Status: install user installed");
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_opkg_operation_outputs() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test update operation output
        let update_output = MockOpkgData::create_update_output();
        assert_contains!(update_output, "Downloading");
        assert_contains!(update_output, "Updated list of available packages");
        
        // Test install operation output
        let install_output = MockOpkgData::create_install_output("test-package");
        assert_contains!(install_output, "Installing test-package");
        assert_contains!(install_output, "Configuring test-package");
        
        // Test remove operation output
        let remove_output = MockOpkgData::create_remove_output("test-package");
        assert_contains!(remove_output, "Removing package test-package");
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_opkg_fixture_setup() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test OPKG test fixture
        let fixture = OpkgTestFixture::new()?;
        
        // Verify directory structure
        assert!(fixture.get_opkg_dir().exists());
        assert!(fixture.get_status_file().exists());
        
        // Test adding package list
        fixture.add_package_list("base", &[
            "base-files - 1.0-1 - Base filesystem",
            "busybox - 1.35.0-1 - Core utilities"
        ])?;
        
        // Test adding installed package
        fixture.add_installed_package(
            "test-package",
            "1.0.0",
            "Package: test-package\nVersion: 1.0.0\nDescription: Test package"
        )?;
        
        // Verify status file was updated
        let status_content = std::fs::read_to_string(fixture.get_status_file())?;
        assert_contains!(status_content, "Package: test-package");
        assert_contains!(status_content, "Version: 1.0.0");
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_package_status_enum() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test package status enumeration
        use luci_shared_types::PackageStatus;
        
        let available = PackageStatus::Available;
        let installed = PackageStatus::Installed;
        let upgradable = PackageStatus::Upgradable;
        let broken = PackageStatus::Broken;
        
        // Test that they're different
        assert_ne!(available, installed);
        assert_ne!(installed, upgradable);
        assert_ne!(upgradable, broken);
        
        // Test serialization/deserialization if implemented
        // This would require serde features to be enabled
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_package_priority_enum() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test package priority enumeration
        use luci_shared_types::PackagePriority;
        
        let required = PackagePriority::Required;
        let important = PackagePriority::Important;
        let standard = PackagePriority::Standard;
        let optional = PackagePriority::Optional;
        let extra = PackagePriority::Extra;
        
        // Test that they're different
        assert_ne!(required, important);
        assert_ne!(important, standard);
        assert_ne!(standard, optional);
        assert_ne!(optional, extra);
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_opkg_binary_detection() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test binary detection logic
        // In a real environment, this would test the actual binary detection
        // For testing, we simulate the behavior
        
        let common_paths = vec![
            "/bin/opkg",
            "/usr/bin/opkg",
            "/sbin/opkg",
            "/usr/sbin/opkg",
        ];
        
        // Test that paths are reasonable
        for path in common_paths {
            assert!(path.starts_with('/'));
            assert!(path.ends_with("opkg"));
        }
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_command_argument_building() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test command argument construction
        let base_args = vec!["list"];
        let search_args = vec!["list", "*test*"];
        let install_args = vec!["install", "package-name"];
        let remove_args = vec!["remove", "package-name"];
        let update_args = vec!["update"];
        
        // Verify argument structure
        assert_eq!(base_args[0], "list");
        assert_eq!(search_args[1], "*test*");
        assert_eq!(install_args[0], "install");
        assert_eq!(install_args[1], "package-name");
        assert_eq!(remove_args[0], "remove");
        assert_eq!(update_args[0], "update");
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_error_handling_scenarios() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test various error scenarios that the OPKG integration should handle
        
        // Test command not found error
        let not_found_error = OpkgError::CommandFailed("opkg: command not found".to_string());
        assert_contains!(format!("{}", not_found_error), "command not found");
        
        // Test permission denied error
        let permission_error = OpkgError::CommandFailed("Permission denied".to_string());
        assert_contains!(format!("{}", permission_error), "Permission denied");
        
        // Test package not found error
        let package_error = OpkgError::CommandFailed("Package not found".to_string());
        assert_contains!(format!("{}", package_error), "Package not found");
        
        // Test network error
        let network_error = OpkgError::CommandFailed("Failed to download".to_string());
        assert_contains!(format!("{}", network_error), "Failed to download");
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_package_validation() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test package info validation
        let valid_package = MockPackageData::create_package("valid-package", "1.0.0");
        validate_package_info(&valid_package)?;
        
        // Test that validation catches empty names
        let mut invalid_package = valid_package.clone();
        invalid_package.name = String::new();
        
        let result = validate_package_info(&invalid_package);
        assert!(result.is_err());
        
        // Test that validation catches empty versions
        let mut invalid_package = MockPackageData::create_package("test", "1.0.0");
        invalid_package.version = String::new();
        
        let result = validate_package_info(&invalid_package);
        assert!(result.is_err());
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[tokio::test]
    async fn test_async_operations() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test async operation patterns that would be used with OPKG
        let timer = PerformanceTimer::new("async_opkg_operation");
        
        // Simulate async OPKG operation
        tokio::time::sleep(std::time::Duration::from_millis(10)).await;
        
        // Verify timing
        timer.assert_under(std::time::Duration::from_millis(100));
        
        cleanup_unit_test()?;
        Ok(())
    }

    #[test]
    fn test_mock_command_executor() -> TestResult<()> {
        setup_unit_test()?;
        
        // Test the mock command executor for OPKG testing
        let mut executor = MockCommandExecutor::new();
        
        // Add mock responses
        executor.add_response(
            "opkg list",
            0,
            &MockOpkgData::create_package_list_output(),
            ""
        );
        
        executor.add_response(
            "opkg info test-package",
            0,
            &MockOpkgData::create_package_info_output("test-package"),
            ""
        );
        
        // Test that responses are stored correctly
        assert!(executor.responses.contains_key("opkg list"));
        assert!(executor.responses.contains_key("opkg info test-package"));
        
        cleanup_unit_test()?;
        Ok(())
    }
}
